from flask import Blueprint, request, jsonify
from models.models import (
    db, DatabaseChatSession, DatabaseChatMessage, ExternalDatabase,
    user_database_permissions
)
from utils.decorators import login_required, require_scope, SCOPE_DATABASE_CHAT
from utils.database_utils import db_connection_manager
from utils.database_agent_utils import process_database_question
from utils.request_utils import get_request_json
from utils.json_serialization_utils import clean_response_for_storage
from datetime import datetime
import logging
import time

logger = logging.getLogger(__name__)

database_chat_bp = Blueprint('database_chat', __name__)

@database_chat_bp.route('/database-chat/sessions/<int:session_id>/messages', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_session_messages(session_id):
    """Get all messages for a database chat session"""
    try:
        # Verify session belongs to user
        session = DatabaseChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id,
            is_active=True
        ).first_or_404()
        
        messages = DatabaseChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(DatabaseChatMessage.created_at.asc()).all()
        
        result = []
        for message in messages:
            result.append({
                'id': message.id,
                'question': message.question,
                'answer': message.answer,
                'sql_queries': message.sql_queries,
                'query_results': message.query_results,
                'execution_plan': message.execution_plan,
                'qa_feedback': message.qa_feedback,
                'token_usage': message.token_usage,
                'execution_time_ms': message.execution_time_ms,
                'error_message': message.error_message,
                # NEW VISUALIZATION FIELDS
                'visualization_data': message.visualization_data,
                'visualization_type': message.visualization_type,
                'visualization_image': message.visualization_image,
                'visualization_metadata': message.visualization_metadata,
                'created_at': message.created_at.isoformat()
            })
        
        return jsonify({
            'session_id': session_id,
            'session_name': session.session_name,
            'database_name': session.database.name,
            'messages': result,
            'total': len(result)
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching session messages: {str(e)}")
        return jsonify({'error': 'Failed to fetch session messages'}), 500

@database_chat_bp.route('/database-chat/sessions/<int:session_id>/messages', methods=['POST'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def add_database_message(session_id):
    """Add a new message to a database chat session and process it"""
    try:
        # Verify session belongs to user and is connected
        session = DatabaseChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id,
            is_active=True
        ).first_or_404()
        
        if not session.is_connected:
            return jsonify({'error': 'Database session is not connected'}), 400
        
        data = get_request_json()
        
        if not data or 'question' not in data:
            return jsonify({'error': 'No question provided'}), 400
        
        question = data['question'].strip()
        if not question:
            return jsonify({'error': 'Question cannot be empty'}), 400
        
        # Check user permission level
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=request.user.id,
            external_database_id=session.external_database_id
        ).first()
        
        if not permission:
            return jsonify({'error': 'You do not have permission to access this database'}), 403
        
        # Check if permission has expired
        if permission.expires_at and permission.expires_at < datetime.utcnow():
            return jsonify({'error': 'Your permission to access this database has expired'}), 403
        
        # Check schema permissions if user has restricted access
        if permission.allowed_schemas is not None:
            # User has specific schema permissions - we need to ensure queries only access allowed schemas
            # This will be handled in the database agent utils
            pass
        
        # Get database configuration
        database = session.database
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Store permission info separately (not in connection config)
        permission_info = {
            'allowed_schemas': permission.allowed_schemas,
            'permission_level': permission.permission_level
        }
        
        # Get previous messages for context
        previous_messages = DatabaseChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(DatabaseChatMessage.created_at.desc()).limit(5).all()
        
        # Process the question using the agentic system
        start_time = time.time()
        
        try:
            # Import the agentic chat system
            from utils.database_agent_utils import process_database_question
            
            result = process_database_question(
                question=question,
                db_config=db_config,
                previous_messages=previous_messages,
                permission_info=permission_info,
                user_id=request.user.id
            )
            
            execution_time_ms = int((time.time() - start_time) * 1000)
            
            # Clean the result for JSON serialization before storing
            cleaned_result = clean_response_for_storage(result)
            
            # Create and save the message with visualization data
            message = DatabaseChatMessage(
                session_id=session_id,
                question=question,
                answer=cleaned_result['answer'],
                sql_queries=cleaned_result.get('sql_queries', []),
                query_results=cleaned_result.get('query_results', []),
                execution_plan=cleaned_result.get('execution_plan'),
                qa_feedback=cleaned_result.get('qa_feedback'),
                token_usage=cleaned_result.get('token_usage'),
                execution_time_ms=execution_time_ms,
                error_message=cleaned_result.get('error_message'),
                # NEW VISUALIZATION FIELDS
                visualization_data=cleaned_result.get('visualization_data'),
                visualization_type=cleaned_result.get('visualization_type'),
                visualization_image=cleaned_result.get('visualization_image'),
                visualization_metadata=cleaned_result.get('visualization_metadata')
            )
            
            db.session.add(message)
            
            # Update session timestamp
            session.updated_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'id': message.id,
                'question': message.question,
                'answer': message.answer,
                'sql_queries': message.sql_queries,
                'query_results': message.query_results,
                'execution_plan': message.execution_plan,
                'qa_feedback': message.qa_feedback,
                'token_usage': message.token_usage,
                'execution_time_ms': message.execution_time_ms,
                'error_message': message.error_message,
                # NEW VISUALIZATION FIELDS
                'visualization_data': message.visualization_data,
                'visualization_type': message.visualization_type,
                'visualization_image': message.visualization_image,
                'visualization_metadata': message.visualization_metadata,
                'created_at': message.created_at.isoformat(),
                'reloop_attempts': cleaned_result.get('reloop_attempts', 0)
            }), 201
            
        except ImportError:
            # Fallback if agentic system is not available
            logger.warning("Database agent utils not available, using simple query execution")
            
            # Simple query execution for basic functionality (READ-ONLY)
            if question.upper().strip().startswith('SELECT'):
                # Validate query safety (only SELECT queries allowed)
                is_safe, safety_error = db_connection_manager.validate_query_safety(question)
                if not is_safe:
                    return jsonify({'error': f'Query not allowed: {safety_error}'}), 400
                
                # Execute the query
                query_result = db_connection_manager.execute_query(db_config, question)
                execution_time_ms = int((time.time() - start_time) * 1000)
                
                if query_result['success']:
                    answer = f"Query executed successfully. Found {query_result.get('row_count', 0)} rows."
                    if query_result.get('data'):
                        # Limit the number of rows shown in the answer
                        data_preview = query_result['data'][:10]  # Show first 10 rows
                        answer += f"\n\nFirst {len(data_preview)} rows:\n"
                        for row in data_preview:
                            answer += f"{row}\n"
                        if len(query_result['data']) > 10:
                            answer += f"\n... and {len(query_result['data']) - 10} more rows."
                else:
                    answer = f"Query failed: {query_result['error']}"
                
                # Create and save the message
                message = DatabaseChatMessage(
                    session_id=session_id,
                    question=question,
                    answer=answer,
                    sql_queries=[question],
                    query_results=[query_result],
                    execution_time_ms=execution_time_ms,
                    error_message=query_result.get('error')
                )
                
                db.session.add(message)
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
                return jsonify({
                    'id': message.id,
                    'question': message.question,
                    'answer': message.answer,
                    'sql_queries': message.sql_queries,
                    'query_results': message.query_results,
                    'execution_time_ms': message.execution_time_ms,
                    'error_message': message.error_message,
                    'created_at': message.created_at.isoformat()
                }), 201
            else:
                # For non-SELECT queries, return a helpful message
                answer = "I can only help you query the database with SELECT statements to retrieve data. This is a read-only chat system and does not support data modification operations for security reasons."
                
                message = DatabaseChatMessage(
                    session_id=session_id,
                    question=question,
                    answer=answer,
                    sql_queries=[],
                    query_results=[],
                    execution_time_ms=int((time.time() - start_time) * 1000)
                )
                
                db.session.add(message)
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
                return jsonify({
                    'id': message.id,
                    'question': message.question,
                    'answer': message.answer,
                    'sql_queries': message.sql_queries,
                    'query_results': message.query_results,
                    'execution_time_ms': message.execution_time_ms,
                    'created_at': message.created_at.isoformat()
                }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error processing database message: {str(e)}")
        return jsonify({'error': 'Failed to process database message'}), 500

# NEW ENDPOINT: Get visualization suggestions for a message
@database_chat_bp.route('/database-chat/messages/<int:message_id>/visualization-suggestions', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_visualization_suggestions(message_id):
    """Get alternative visualization suggestions for a database message"""
    try:
        # Verify message belongs to user
        message = DatabaseChatMessage.query.join(DatabaseChatSession).filter(
            DatabaseChatMessage.id == message_id,
            DatabaseChatSession.user_id == request.user.id
        ).first_or_404()
        
        # Import visualization service
        from services.database_visualization_service import database_visualization_service
        
        # Get suggestions
        suggestions = database_visualization_service.get_visualization_suggestions(
            message.question, 
            message.query_results or []
        )
        
        return jsonify({
            'message_id': message_id,
            'suggestions': suggestions,
            'current_visualization_type': message.visualization_type
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting visualization suggestions: {str(e)}")
        return jsonify({'error': 'Failed to get visualization suggestions'}), 500

# NEW ENDPOINT: Regenerate visualization with different chart type
@database_chat_bp.route('/database-chat/messages/<int:message_id>/regenerate-visualization', methods=['POST'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def regenerate_visualization(message_id):
    """Regenerate visualization for a message with a different chart type"""
    try:
        # Verify message belongs to user
        message = DatabaseChatMessage.query.join(DatabaseChatSession).filter(
            DatabaseChatMessage.id == message_id,
            DatabaseChatSession.user_id == request.user.id
        ).first_or_404()
        
        data = get_request_json()
        chart_type = data.get('chart_type', 'bar')
        
        if not message.query_results:
            return jsonify({'error': 'No query results available for visualization'}), 400
        
        # Import visualization service
        from services.database_visualization_service import database_visualization_service
        
        # Generate new visualization with specified chart type
        # We need to modify the question to request the specific chart type
        modified_question = f"{message.question} (show as {chart_type} chart)"
        
        visualization_result = database_visualization_service.generate_visualization(
            modified_question, 
            message.query_results
        )
        
        if not visualization_result:
            return jsonify({'error': 'Could not generate visualization with specified chart type'}), 400
        
        # Update the message with new visualization
        message.visualization_data = visualization_result.get('visualization_data')
        message.visualization_type = visualization_result.get('visualization_type')
        message.visualization_image = visualization_result.get('visualization_image')
        message.visualization_metadata = visualization_result.get('visualization_metadata')
        
        db.session.commit()
        
        return jsonify({
            'message_id': message_id,
            'visualization_data': message.visualization_data,
            'visualization_type': message.visualization_type,
            'visualization_image': message.visualization_image,
            'visualization_metadata': message.visualization_metadata
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error regenerating visualization: {str(e)}")
        return jsonify({'error': 'Failed to regenerate visualization'}), 500

# Direct SQL execution has been removed for security reasons.
# This is a read-only chat system that only allows SELECT queries through the AI agent.
 