from flask import Blueprint, request, jsonify
from models.models import (
    db, ExternalDatabase, User, user_database_permissions,
    DatabaseChatSession
)
from utils.decorators import login_required, require_scope, SCOPE_DATABASE_CHAT
from utils.database_utils import db_connection_manager
from utils.request_utils import get_request_json
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

database_user_bp = Blueprint('database_user', __name__)

@database_user_bp.route('/databases/accessible', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_accessible_databases():
    """Get databases accessible to the current user with optional filtering"""
    try:
        user_id = request.user.id
        
        # Get query parameters for filtering
        database_type = request.args.get('database_type')
        permission_level = request.args.get('permission_level')
        test_status = request.args.get('test_status')
        include_expired = request.args.get('include_expired', 'false').lower() == 'true'
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build base query - use explicit column selection for more predictable results
        query = db.session.query(
            ExternalDatabase.id,
            ExternalDatabase.name,
            ExternalDatabase.db_type,
            ExternalDatabase.host,
            ExternalDatabase.port,
            ExternalDatabase.database_name,
            ExternalDatabase.last_tested_at,
            ExternalDatabase.test_status,
            user_database_permissions.c.permission_level,
            user_database_permissions.c.expires_at
        ).join(
            user_database_permissions,
            ExternalDatabase.id == user_database_permissions.c.external_database_id
        ).filter(
            user_database_permissions.c.user_id == user_id,
            ExternalDatabase.is_active == True
        )
        
        # Apply filters
        if database_type:
            query = query.filter(ExternalDatabase.db_type == database_type)
        
        if permission_level:
            query = query.filter(user_database_permissions.c.permission_level == permission_level)
        
        if test_status:
            query = query.filter(ExternalDatabase.test_status == test_status)
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply pagination
        accessible_dbs = query.offset(offset).limit(limit).all()
        
        result = []
        for row in accessible_dbs:
            # With explicit column selection, row is a tuple with specific order
            try:
                # Unpack the row based on the query column order
                (db_id, db_name, db_type, host, port, database_name, 
                 last_tested_at, test_status, permission_level, expires_at) = row
                
                # Check if permission has expired (unless include_expired is True)
                if not include_expired and expires_at and expires_at < datetime.utcnow():
                    continue
                
                result.append({
                    'id': db_id,
                    'name': db_name,
                    'db_type': db_type,
                    'host': host,
                    'port': port,
                    'database_name': database_name,
                    'permission_level': permission_level,
                    'last_tested_at': last_tested_at.isoformat() if last_tested_at else None,
                    'test_status': test_status,
                    'expires_at': expires_at.isoformat() if expires_at else None,
                    'is_expired': expires_at and expires_at < datetime.utcnow()
                })
            except Exception as e:
                logger.error(f"Error processing row {row}: {str(e)}")
                continue
        
        return jsonify({
            'databases': result,
            'total': total_count,
            'limit': limit,
            'offset': offset,
            'filters_applied': {
                'database_type': database_type,
                'permission_level': permission_level,
                'test_status': test_status,
                'include_expired': include_expired
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching accessible databases: {str(e)}")
        return jsonify({'error': 'Failed to fetch accessible databases'}), 500

@database_user_bp.route('/databases/<int:db_id>/connect', methods=['POST'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def connect_to_database(db_id):
    """Test connection to a database and create a chat session"""
    try:
        user_id = request.user.id
        
        # Check if user has permission to access this database
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=user_id,
            external_database_id=db_id
        ).first()
        
        if not permission:
            return jsonify({'error': 'You do not have permission to access this database'}), 403
        
        # Check if permission has expired
        if permission.expires_at and permission.expires_at < datetime.utcnow():
            return jsonify({'error': 'Your permission to access this database has expired'}), 403
        
        # Get database configuration
        database = ExternalDatabase.query.filter_by(id=db_id, is_active=True).first()
        if not database:
            return jsonify({'error': 'Database not found or inactive'}), 404
        
        # Prepare database configuration for connection
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Test the connection
        success, error_message = db_connection_manager.test_connection(db_config)
        
        if not success:
            return jsonify({
                'success': False,
                'error': f'Failed to connect to database: {error_message}'
            }), 400
        
        # Create or get existing chat session
        data = get_request_json()
        session_name = data.get('session_name', f"Chat with {database.name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        # Check if there's an existing active session
        existing_session = DatabaseChatSession.query.filter_by(
            user_id=user_id,
            external_database_id=db_id,
            is_active=True
        ).first()
        
        if existing_session:
            # Update existing session
            existing_session.is_connected = True
            existing_session.connection_established_at = datetime.utcnow()
            existing_session.connection_error = None
            existing_session.updated_at = datetime.utcnow()
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'Connected to database successfully',
                'session': {
                    'id': existing_session.id,
                    'session_name': existing_session.session_name,
                    'database_name': database.name,
                    'connected_at': existing_session.connection_established_at.isoformat(),
                    'permission_level': permission.permission_level
                }
            }), 200
        else:
            # Create new session
            new_session = DatabaseChatSession(
                user_id=user_id,
                external_database_id=db_id,
                session_name=session_name,
                is_connected=True,
                connection_established_at=datetime.utcnow()
            )
            
            db.session.add(new_session)
            db.session.commit()
            
            return jsonify({
                'success': True,
                'message': 'Connected to database successfully',
                'session': {
                    'id': new_session.id,
                    'session_name': new_session.session_name,
                    'database_name': database.name,
                    'connected_at': new_session.connection_established_at.isoformat(),
                    'permission_level': permission.permission_level
                }
            }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error connecting to database: {str(e)}")
        return jsonify({'error': 'Failed to connect to database'}), 500

@database_user_bp.route('/databases/sessions', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_user_database_sessions():
    """Get all database chat sessions for the current user with optional filtering"""
    try:
        user_id = request.user.id
        
        # Get query parameters for filtering
        database_type = request.args.get('database_type')
        is_connected = request.args.get('is_connected')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Build base query
        query = DatabaseChatSession.query.filter_by(
            user_id=user_id,
            is_active=True
        )
        
        # Apply filters
        if database_type:
            query = query.join(ExternalDatabase).filter(
                ExternalDatabase.db_type == database_type
            )
        
        if is_connected is not None:
            is_connected_bool = is_connected.lower() == 'true'
            query = query.filter(DatabaseChatSession.is_connected == is_connected_bool)
        
        if date_from:
            try:
                from_date = datetime.fromisoformat(date_from.replace('Z', '+00:00'))
                query = query.filter(DatabaseChatSession.created_at >= from_date)
            except ValueError:
                return jsonify({'error': 'Invalid date_from format. Use ISO format (YYYY-MM-DDTHH:MM:SS)'}), 400
        
        if date_to:
            try:
                to_date = datetime.fromisoformat(date_to.replace('Z', '+00:00'))
                query = query.filter(DatabaseChatSession.created_at <= to_date)
            except ValueError:
                return jsonify({'error': 'Invalid date_to format. Use ISO format (YYYY-MM-DDTHH:MM:SS)'}), 400
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply ordering and pagination
        sessions = query.order_by(DatabaseChatSession.updated_at.desc()).offset(offset).limit(limit).all()
        
        result = []
        for session in sessions:
            result.append({
                'id': session.id,
                'session_name': session.session_name,
                'database_name': session.database.name,
                'database_type': session.database.db_type,
                'is_connected': session.is_connected,
                'connection_established_at': session.connection_established_at.isoformat() if session.connection_established_at else None,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'message_count': len(session.messages)
            })
        
        return jsonify({
            'sessions': result,
            'total': total_count,
            'limit': limit,
            'offset': offset,
            'filters_applied': {
                'database_type': database_type,
                'is_connected': is_connected,
                'date_from': date_from,
                'date_to': date_to
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching user database sessions: {str(e)}")
        return jsonify({'error': 'Failed to fetch database sessions'}), 500

@database_user_bp.route('/databases/sessions/<int:session_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def delete_database_session(session_id):
    """Delete a database chat session"""
    try:
        session = DatabaseChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()
        
        # Mark session as inactive instead of deleting
        session.is_active = False
        session.is_connected = False
        session.updated_at = datetime.utcnow()
        db.session.commit()
        
        return jsonify({
            'message': 'Database session deleted successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting database session: {str(e)}")
        return jsonify({'error': 'Failed to delete database session'}), 500

@database_user_bp.route('/databases/<int:db_id>/schema', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_user_database_schema(db_id):
    """Get schema information for a database (user version with limited info)"""
    try:
        user_id = request.user.id
        data = request.args
        
        # Check if specific schema is requested
        schema_name = data.get('schema')
        
        # Check if user has permission to access this database
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=user_id,
            external_database_id=db_id
        ).first()
        
        if not permission:
            return jsonify({'error': 'You do not have permission to access this database'}), 403
        
        # Check if permission has expired
        if permission.expires_at and permission.expires_at < datetime.utcnow():
            return jsonify({'error': 'Your permission to access this database has expired'}), 403
        
        # Check schema permissions if specific schema is requested
        if schema_name and permission.allowed_schemas is not None:
            if schema_name not in permission.allowed_schemas:
                return jsonify({
                    'error': f'You do not have permission to access schema "{schema_name}"'
                }), 403
        
        # Get database configuration
        database = ExternalDatabase.query.filter_by(id=db_id, is_active=True).first()
        if not database:
            return jsonify({'error': 'Database not found or inactive'}), 404
        
        # Prepare database configuration
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Add schema to additional params if specified
        if schema_name:
            db_config['additional_params']['schema'] = schema_name
        
        # Get schema information
        schema_result = db_connection_manager.get_database_schema(db_config)
        
        if not schema_result.get('success'):
            return jsonify({
                'error': 'Failed to retrieve database schema',
                'details': schema_result.get('error')
            }), 500
        
        # Return limited schema information for users
        schema = schema_result['schema']
        limited_schema = {
            'tables': {},
            'views': {}
        }
        
        # For tables, only return basic column information
        for table_name, table_info in schema.get('tables', {}).items():
            limited_schema['tables'][table_name] = {
                'columns': [
                    {
                        'name': col['name'],
                        'type': str(col['type']),
                        'nullable': col.get('nullable', True)
                    }
                    for col in table_info.get('columns', [])
                ]
            }
        
        # For views, return column information
        for view_name, view_info in schema.get('views', {}).items():
            limited_schema['views'][view_name] = {
                'columns': [
                    {
                        'name': col['name'],
                        'type': str(col['type']),
                        'nullable': col.get('nullable', True)
                    }
                    for col in view_info.get('columns', [])
                ]
            }
        
        return jsonify({
            'success': True,
            'database_name': database.name,
            'schema': limited_schema,
            'schema_name': schema_name,
            'allowed_schemas': permission.allowed_schemas
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user database schema: {str(e)}")
        return jsonify({'error': 'Failed to get database schema'}), 500

@database_user_bp.route('/databases/<int:db_id>/schemas', methods=['GET'])
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
def get_user_database_schemas(db_id):
    """Get list of available schemas in a database (user version)"""
    try:
        user_id = request.user.id
        
        # Check if user has permission to access this database
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=user_id,
            external_database_id=db_id
        ).first()
        
        if not permission:
            return jsonify({'error': 'You do not have permission to access this database'}), 403
        
        # Check if permission has expired
        if permission.expires_at and permission.expires_at < datetime.utcnow():
            return jsonify({'error': 'Your permission to access this database has expired'}), 403
        
        # Get database configuration
        database = ExternalDatabase.query.filter_by(id=db_id, is_active=True).first()
        if not database:
            return jsonify({'error': 'Database not found or inactive'}), 404
        
        # Prepare database configuration
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Get available schemas
        schemas_result = db_connection_manager.get_available_schemas(db_config)
        
        if not schemas_result.get('success'):
            return jsonify({
                'error': 'Failed to retrieve database schemas',
                'details': schemas_result.get('error')
            }), 500
        
        # Filter schemas based on user permissions
        all_schemas = schemas_result.get('schemas', [])
        if permission.allowed_schemas is not None:
            # User has specific schema permissions - filter the list
            allowed_schema_names = set(permission.allowed_schemas)
            filtered_schemas = [
                schema for schema in all_schemas 
                if schema['name'] in allowed_schema_names
            ]
        else:
            # User has access to all schemas
            filtered_schemas = all_schemas
        
        return jsonify({
            'success': True,
            'database_name': database.name,
            'schemas': filtered_schemas,
            'total': len(filtered_schemas),
            'total_available': len(all_schemas),
            'has_restricted_access': permission.allowed_schemas is not None
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user database schemas: {str(e)}")
        return jsonify({'error': 'Failed to get database schemas'}), 500
