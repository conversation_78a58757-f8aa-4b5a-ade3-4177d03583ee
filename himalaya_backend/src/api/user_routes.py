from flask import Blueprint, jsonify, request
from sqlalchemy import text, update
from models.models import (
        db, User, Vertical, Department,
        user_departments, user_verticals, theme_owners,
        ChatSession, ChatMessage, FGDChatSession, FGDChatMessage,
        File, ExternalDatabase, user_database_permissions
)
from utils.decorators import admin_required, SCOPE_FGD_OWNER

user_bp = Blueprint('user', __name__)

@user_bp.route('/users', methods=['GET'])
def get_users():
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    # Optional email search parameter
    search = request.args.get('search', '', type=str)

    # Build the base query and apply email filter if provided
    query = User.query
    if search:
        query = query.filter(User.email.ilike(f"%{search}%"))

    users = query.paginate(
        page=page, 
        per_page=per_page,
        error_out=False
    )
    
    return jsonify({
        'items': [{
            'id': user.id,
            'user_name': user.user_name,
            'email': user.email,
            'is_admin': user.is_admin,
            'verticals': [{
                'id': v.id,
                'name': v.name
            } for v in user.verticals],
            'departments': [{
                'id': d.id,
                'name': d.name,
                'vertical_id': d.vertical_id
            } for d in user.departments],
            'databases': [{
                'id': db_perm.external_database_id,
                'name': next((db.name for db in ExternalDatabase.query.all() if db.id == db_perm.external_database_id), 'Unknown'),
                'permission_level': db_perm.permission_level,
                'expires_at': db_perm.expires_at.isoformat() if db_perm.expires_at else None
            } for db_perm in db.session.query(user_database_permissions).filter_by(user_id=user.id).all()],
            'scopes': user.scopes
        } for user in users.items],
        'pagination': {
            'total_items': users.total,
            'total_pages': users.pages,
            'current_page': users.page,
            'per_page': per_page,
            'has_next': users.has_next,
            'has_prev': users.has_prev
        }
    })

@user_bp.route('/users/all', methods=['GET'])
def get_all_users():
    """Get all users without pagination but with optional search functionality"""
    # Optional email search parameter
    search = request.args.get('search', '', type=str)

    # Build the base query and apply email filter if provided
    query = User.query
    if search:
        query = query.filter(User.email.ilike(f"%{search}%"))

    users = query.all()
    
    return jsonify({
        'users': [{
            'id': user.id,
            'user_name': user.user_name,
            'email': user.email,
            'is_admin': user.is_admin,
            'verticals': [{
                'id': v.id,
                'name': v.name
            } for v in user.verticals],
            'departments': [{
                'id': d.id,
                'name': d.name,
                'vertical_id': d.vertical_id
            } for d in user.departments],
            'databases': [{
                'id': db_perm.external_database_id,
                'name': next((db.name for db in ExternalDatabase.query.all() if db.id == db_perm.external_database_id), 'Unknown'),
                'permission_level': db_perm.permission_level,
                'expires_at': db_perm.expires_at.isoformat() if db_perm.expires_at else None
            } for db_perm in db.session.query(user_database_permissions).filter_by(user_id=user.id).all()],
            'scopes': user.scopes
        } for user in users],
        'total_count': len(users)
    })

@user_bp.route('/users/bulk', methods=['POST'])
@admin_required
def bulk_add_users():
    users_data = request.json
    try:
        new_users = []
        for user_data in users_data:
            # Create user
            user = User(
                user_name=user_data['user_name'],
                email=user_data['email'],
                scopes=user_data.get('scopes', []),
                is_admin=user_data.get('is_admin', False)
            )
            
            # Add verticals if provided
            if 'vertical_ids' in user_data:
                verticals = Vertical.query.filter(
                    Vertical.id.in_(user_data['vertical_ids'])
                ).all()
                user.verticals = verticals
            
            # Add departments if provided
            if 'departments' in user_data:
                for dept_data in user_data['departments']:
                    vertical_id = dept_data['vertical_id']
                    department_ids = dept_data['department_ids']
                    
                    departments = Department.query.filter(
                        Department.id.in_(department_ids),
                        Department.vertical_id == vertical_id
                    ).all()
                    
                    for dept in departments:
                        stmt = user_departments.insert().values(
                            user_id=user.id,
                            department_id=dept.id,
                            vertical_id=vertical_id
                        )
                        db.session.execute(stmt)
            
            new_users.append(user)
        
        db.session.bulk_save_objects(new_users)
        db.session.flush()  # Flush to get user IDs

        # Handle database permissions for each user
        for i, user_data in enumerate(users_data):
            if 'databases' in user_data:
                from datetime import datetime
                user = new_users[i]

                for db_data in user_data['databases']:
                    database_id = db_data['database_id']
                    permission_level = db_data.get('permission_level', 'read')
                    expires_at = None

                    if db_data.get('expires_at'):
                        try:
                            expires_at = datetime.fromisoformat(db_data['expires_at'])
                        except ValueError:
                            pass  # Skip invalid date format

                    # Check if database exists
                    database = ExternalDatabase.query.get(database_id)
                    if database:
                        stmt = user_database_permissions.insert().values(
                            user_id=user.id,
                            external_database_id=database_id,
                            permission_level=permission_level,
                            granted_by=request.user.id if hasattr(request, 'user') else None,
                            created_at=datetime.utcnow(),
                            expires_at=expires_at
                        )
                        db.session.execute(stmt)

        db.session.commit()
        
        return jsonify({
            'message': f'Successfully added {len(new_users)} users',
            'status': 'success'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'message': f'Error adding users: {str(e)}',
            'status': 'error'
        }), 400

@user_bp.route('/users/bulk-update', methods=['PUT'])
@admin_required
def bulk_update_users():
    users_data = request.json
    try:
        updated_count = 0
        for user_data in users_data:
            user = User.query.get(user_data['id'])
            if user:
                # Update basic fields
                for field in ['user_name', 'scopes', 'is_admin']:
                    if field in user_data:
                        setattr(user, field, user_data[field])
                
                # Update verticals if provided
                if 'vertical_ids' in user_data:
                    verticals = Vertical.query.filter(
                        Vertical.id.in_(user_data['vertical_ids'])
                    ).all()
                    user.verticals = verticals
                
                # Update departments if provided
                if 'departments' in user_data:
                    # First, remove all existing department assignments for this user
                    stmt = user_departments.delete().where(
                        user_departments.c.user_id == user.id
                    )
                    db.session.execute(stmt)
                    
                    # Then add new department assignments
                    for dept_data in user_data['departments']:
                        vertical_id = dept_data['vertical_id']
                        department_ids = dept_data['department_ids']
                        
                        departments = Department.query.filter(
                            Department.id.in_(department_ids),
                            Department.vertical_id == vertical_id
                        ).all()
                        
                        for dept in departments:
                            stmt = user_departments.insert().values(
                                user_id=user.id,
                                department_id=dept.id,
                                vertical_id=vertical_id
                            )
                            db.session.execute(stmt)

                # Update database permissions if provided
                if 'databases' in user_data:
                    from datetime import datetime
                    # Remove existing database permissions
                    stmt = user_database_permissions.delete().where(
                        user_database_permissions.c.user_id == user.id
                    )
                    db.session.execute(stmt)

                    # Add new database permissions
                    for db_data in user_data['databases']:
                        database_id = db_data['database_id']
                        permission_level = db_data.get('permission_level', 'read')
                        expires_at = None

                        if db_data.get('expires_at'):
                            try:
                                expires_at = datetime.fromisoformat(db_data['expires_at'])
                            except ValueError:
                                pass  # Skip invalid date format

                        # Check if database exists
                        database = ExternalDatabase.query.get(database_id)
                        if database:
                            stmt = user_database_permissions.insert().values(
                                user_id=user.id,
                                external_database_id=database_id,
                                permission_level=permission_level,
                                granted_by=request.user.id if hasattr(request, 'user') else None,
                                created_at=datetime.utcnow(),
                                expires_at=expires_at
                            )
                            db.session.execute(stmt)

                
                updated_count += 1
        
        db.session.commit()
        return jsonify({
            'message': f'Successfully updated {updated_count} users',
            'status': 'success'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'message': f'Error updating users: {str(e)}',
            'status': 'error'
        }), 400

@user_bp.route('/users/bulk-delete', methods=['DELETE'])
@admin_required
def bulk_delete_users():
    user_ids = request.json.get('user_ids', [])
    try:
        # Delete from user_departments first
        stmt = user_departments.delete().where(
            user_departments.c.user_id.in_(user_ids)
        )
        db.session.execute(stmt)
        
        # Then delete the users
        result = User.query.filter(User.id.in_(user_ids)).delete(synchronize_session=False)
        db.session.commit()
        
        return jsonify({
            'message': f'Successfully deleted {result} users',
            'status': 'success'
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'message': f'Error deleting users: {str(e)}',
            'status': 'error'
        }), 400 

@user_bp.route('/users', methods=['POST'])
@admin_required
def create_user():
    data = request.json
    try:
        user = User(
            user_name=data['user_name'],
            email=data['email'],
            scopes=data.get('scopes', []),
            is_admin=data.get('is_admin', False)
        )
        
        # Add verticals if provided
        if 'vertical_ids' in data:
            verticals = Vertical.query.filter(
                Vertical.id.in_(data['vertical_ids'])
            ).all()
            user.verticals = verticals
        
        # Add departments if provided
        if 'departments' in data:
            for dept_data in data['departments']:
                vertical_id = dept_data['vertical_id']
                department_ids = dept_data['department_ids']
                
                departments = Department.query.filter(
                    Department.id.in_(department_ids),
                    Department.vertical_id == vertical_id
                ).all()
                
                for dept in departments:
                    stmt = user_departments.insert().values(
                        user_id=user.id,
                        department_id=dept.id,
                        vertical_id=vertical_id
                    )
                    db.session.execute(stmt)
        
        db.session.add(user)
        db.session.flush()  # Flush to get user.id

        # Add database permissions if provided
        if 'databases' in data:
            from datetime import datetime
            for db_data in data['databases']:
                database_id = db_data['database_id']
                permission_level = db_data.get('permission_level', 'read')
                expires_at = None

                if db_data.get('expires_at'):
                    try:
                        expires_at = datetime.fromisoformat(db_data['expires_at'])
                    except ValueError:
                        pass  # Skip invalid date format

                # Check if database exists
                database = ExternalDatabase.query.get(database_id)
                if database:
                    stmt = user_database_permissions.insert().values(
                        user_id=user.id,
                        external_database_id=database_id,
                        permission_level=permission_level,
                        granted_by=request.user.id if hasattr(request, 'user') else None,
                        created_at=datetime.utcnow(),
                        expires_at=expires_at
                    )
                    db.session.execute(stmt)

        db.session.commit()
        
        return jsonify({
            'message': 'User created successfully',
            'user': {
                'id': user.id,
                'email': user.email,
                'user_name': user.user_name,
                'is_admin': user.is_admin
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@user_bp.route('/users/<int:user_id>', methods=['PUT', 'DELETE'])
@admin_required
def manage_user(user_id):
    user = User.query.get_or_404(user_id)
    
    if request.method == 'DELETE':
        try:
            # Store user name and set uploaded_by to NULL for files
            update_stmt = update(File).where(
                File.uploaded_by == user_id
            ).values(
                archived_uploader_name=user.user_name,
                uploaded_by=None
            )
            db.session.execute(update_stmt)

            # Delete chat sessions and messages
            ChatMessage.query.filter(
                ChatMessage.session_id.in_(
                    ChatSession.query.with_entities(ChatSession.id)
                    .filter_by(user_id=user_id)
                )
            ).delete(synchronize_session=False)
            
            ChatSession.query.filter_by(user_id=user_id).delete(synchronize_session=False)
            
            # Delete FGD chat sessions and messages
            FGDChatMessage.query.filter(
                FGDChatMessage.session_id.in_(
                    FGDChatSession.query.with_entities(FGDChatSession.id)
                    .filter_by(user_id=user_id)
                )
            ).delete(synchronize_session=False)
            
            FGDChatSession.query.filter_by(user_id=user_id).delete(synchronize_session=False)
            
            # Delete user's department associations
            stmt = user_departments.delete().where(
                user_departments.c.user_id == user_id
            )
            db.session.execute(stmt)
            
            # Delete user's vertical associations
            stmt = user_verticals.delete().where(
                user_verticals.c.user_id == user_id
            )
            db.session.execute(stmt)
            
            # Delete theme ownership associations
            stmt = theme_owners.delete().where(
                theme_owners.c.user_id == user_id
            )
            db.session.execute(stmt)
            
            # Finally delete the user
            db.session.delete(user)
            db.session.commit()
            
            return jsonify({'message': 'User and associated data deleted successfully'})
            
        except Exception as e:
            db.session.rollback()
            return jsonify({
                'error': 'Failed to delete user',
                'details': str(e)
            }), 400
    
    # PUT method
    data = request.json
    try:
        # Update basic fields
        for field in ['user_name', 'scopes', 'is_admin']:
            if field in data:
                setattr(user, field, data[field])
        
        # Update verticals if provided
        if 'vertical_ids' in data:
            verticals = Vertical.query.filter(
                Vertical.id.in_(data['vertical_ids'])
            ).all()
            user.verticals = verticals
        
        # Update departments if provided
        if 'departments' in data:
            # Remove existing department assignments
            stmt = user_departments.delete().where(
                user_departments.c.user_id == user.id
            )
            db.session.execute(stmt)
            
            # Add new department assignments
            for dept_data in data['departments']:
                vertical_id = dept_data['vertical_id']
                department_ids = dept_data['department_ids']
                
                departments = Department.query.filter(
                    Department.id.in_(department_ids),
                    Department.vertical_id == vertical_id
                ).all()
                
                for dept in departments:
                    stmt = user_departments.insert().values(
                        user_id=user.id,
                        department_id=dept.id,
                        vertical_id=vertical_id
                    )
                    db.session.execute(stmt)

        # Update database permissions if provided
        if 'databases' in data:
            from datetime import datetime
            # Remove existing database permissions
            stmt = user_database_permissions.delete().where(
                user_database_permissions.c.user_id == user.id
            )
            db.session.execute(stmt)

            # Add new database permissions
            for db_data in data['databases']:
                database_id = db_data['database_id']
                permission_level = db_data.get('permission_level', 'read')
                expires_at = None

                if db_data.get('expires_at'):
                    try:
                        expires_at = datetime.fromisoformat(db_data['expires_at'])
                    except ValueError:
                        pass  # Skip invalid date format

                # Check if database exists
                database = ExternalDatabase.query.get(database_id)
                if database:
                    stmt = user_database_permissions.insert().values(
                        user_id=user.id,
                        external_database_id=database_id,
                        permission_level=permission_level,
                        granted_by=request.user.id if hasattr(request, 'user') else None,
                        created_at=datetime.utcnow(),
                        expires_at=expires_at
                    )
                    db.session.execute(stmt)

        
        db.session.commit()
        return jsonify({
            'message': 'User updated successfully',
            'user': {
                'id': user.id,
                'email': user.email,
                'user_name': user.user_name,
                'is_admin': user.is_admin
            }
        })
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400 

@user_bp.route('/users/fgd-owners', methods=['GET'])
def get_fgd_owners():
    """Get all users who have FGD owner access"""
    try:
        # Query users who have SCOPE_FGD_OWNER in their scopes array
        fgd_owners = User.query.filter(
            User.scopes.contains([SCOPE_FGD_OWNER])
        ).all()
        
        return jsonify({
            'status': 'success',
            'fgd_owners': [{
                'id': user.id,
                'user_name': user.user_name,
                'email': user.email
            } for user in fgd_owners]
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Error fetching FGD owners: {str(e)}'
        }), 400 