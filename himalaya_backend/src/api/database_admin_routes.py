from flask import Blueprint, request, jsonify
from models.models import (
    db, ExternalDatabase, User, user_database_permissions
)
from utils.decorators import admin_required, login_required, require_scope, SCOPE_DATABASE_ADMIN
from utils.database_utils import db_connection_manager
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

database_admin_bp = Blueprint('database_admin', __name__)

# Database type configurations with specific requirements
DATABASE_TYPE_CONFIGS = {
    'postgresql': {
        'name': 'PostgreSQL',
        'default_port': 5432,
        'required_fields': ['name', 'host', 'port', 'database_name', 'username', 'password'],
        'optional_fields': ['ssl_enabled', 'ssl_cert_path', 'connection_string_template', 'schema'],
        'additional_params': {
            'sslmode': 'prefer',
            'connect_timeout': 10,
            'application_name': 'himalaya_chat'
        },
        'schema_support': True,
        'default_schema': 'public',
        'ssl_options': ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full'],
        'description': 'PostgreSQL database with advanced features and ACID compliance'
    },
    'mysql': {
        'name': 'MySQL',
        'default_port': 3306,
        'required_fields': ['name', 'host', 'port', 'database_name', 'username', 'password'],
        'optional_fields': ['ssl_enabled', 'connection_string_template', 'schema'],
        'additional_params': {
            'charset': 'utf8mb4',
            'autocommit': True,
            'connect_timeout': 10
        },
        'schema_support': True,
        'default_schema': None,  # MySQL uses database name as schema
        'description': 'MySQL database with high performance and reliability'
    },
    'sqlserver': {
        'name': 'SQL Server',
        'default_port': 1433,
        'required_fields': ['name', 'host', 'port', 'database_name', 'username', 'password'],
        'optional_fields': ['ssl_enabled', 'connection_string_template', 'schema'],
        'additional_params': {
            'driver': 'ODBC Driver 17 for SQL Server',
            'timeout': 30,
            'encrypt': 'yes',
            'trust_server_certificate': 'yes'
        },
        'schema_support': True,
        'default_schema': 'dbo',
        'description': 'Microsoft SQL Server with enterprise features'
    },
    'oracle': {
        'name': 'Oracle',
        'default_port': 1521,
        'required_fields': ['name', 'host', 'port', 'database_name', 'username', 'password'],
        'optional_fields': ['ssl_enabled', 'connection_string_template', 'schema'],
        'additional_params': {
            'encoding': 'UTF-8',
            'nencoding': 'UTF-8',
            'threaded': True
        },
        'schema_support': True,
        'default_schema': None,  # Oracle uses username as default schema
        'description': 'Oracle Database with enterprise-grade features'
    }
}

@database_admin_bp.route('/admin/databases/types', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_database_types():
    """Get available database types and their configurations"""
    try:
        return jsonify({
            'database_types': DATABASE_TYPE_CONFIGS,
            'total': len(DATABASE_TYPE_CONFIGS)
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching database types: {str(e)}")
        return jsonify({'error': 'Failed to fetch database types'}), 500

@database_admin_bp.route('/admin/databases', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_databases():
    """Get all external databases (admin only)"""
    try:
        databases = ExternalDatabase.query.all()
        
        result = []
        for db_config in databases:
            # Extract schema from additional_params
            schema = None
            if db_config.additional_params:
                schema = db_config.additional_params.get('schema')
            
            result.append({
                'id': db_config.id,
                'name': db_config.name,
                'db_type': db_config.db_type,
                'host': db_config.host,
                'port': db_config.port,
                'database_name': db_config.database_name,
                'username': db_config.username,
                'schema': schema,
                'ssl_enabled': db_config.ssl_enabled,
                'is_active': db_config.is_active,
                'created_by': db_config.creator.user_name if db_config.creator else None,
                'created_at': db_config.created_at.isoformat(),
                'updated_at': db_config.updated_at.isoformat(),
                'last_tested_at': db_config.last_tested_at.isoformat() if db_config.last_tested_at else None,
                'test_status': db_config.test_status,
                'test_error_message': db_config.test_error_message,
                'authorized_users_count': len(db_config.authorized_users)
            })
        
        return jsonify({
            'databases': result,
            'total': len(result)
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching databases: {str(e)}")
        return jsonify({'error': 'Failed to fetch databases'}), 500

@database_admin_bp.route('/admin/databases', methods=['POST'])
@login_required
@require_scope('DATABASE_ADMIN')
def create_database():
    """Create a new external database configuration"""
    try:
        data = request.json
        
        # Validate database type
        db_type = data.get('db_type', '').lower()
        if db_type not in DATABASE_TYPE_CONFIGS:
            supported_types = list(DATABASE_TYPE_CONFIGS.keys())
            return jsonify({
                'error': f'Unsupported database type. Supported types: {", ".join(supported_types)}'
            }), 400
        
        # Get database type configuration
        db_config = DATABASE_TYPE_CONFIGS[db_type]
        
        # Validate required fields
        for field in db_config['required_fields']:
            if not data.get(field):
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Set default port if not provided and database type has one
        if 'port' not in data and db_config.get('default_port'):
            data['port'] = db_config['default_port']
        
        # Validate port for all databases
        try:
            port = int(data['port'])
            if port <= 0 or port > 65535:
                return jsonify({'error': 'Port must be between 1 and 65535'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Port must be a valid number'}), 400
        
        # SECURITY: Prevent localhost connections
        host = data.get('host', '').lower().strip()
        localhost_patterns = [
            'localhost', '127.0.0.1', '::1', '0.0.0.0',
            'localhost.localdomain', 'localhost.local',
            '*********/8', '::1/128'
        ]
        
        if any(pattern in host for pattern in localhost_patterns):
            return jsonify({
                'error': 'Localhost connections are not allowed for security reasons. Please use a remote database server.'
            }), 400
        
        # Validate host is not empty or invalid
        if not host or len(host) < 3:
            return jsonify({'error': 'Host must be a valid remote database server address'}), 400
        
        # Encrypt password
        encrypted_password = db_connection_manager.encrypt_password(data['password'])
        
        # Handle schema configuration
        schema = data.get('schema')
        if schema:
            # Validate schema name
            if not isinstance(schema, str) or not schema.strip():
                return jsonify({'error': 'Schema must be a non-empty string'}), 400
            if len(schema) > 100:  # Reasonable limit for schema name length
                return jsonify({'error': 'Schema name must be 100 characters or less'}), 400
            
            # Add schema to additional params
            additional_params = db_config.get('additional_params', {}).copy()
            additional_params['schema'] = schema.strip()
        else:
            # Use default schema if available
            additional_params = db_config.get('additional_params', {}).copy()
            if db_config.get('default_schema'):
                additional_params['schema'] = db_config['default_schema']
        
        # Merge additional parameters from request
        if data.get('additional_params'):
            additional_params.update(data['additional_params'])
        
        # Create database configuration
        new_db = ExternalDatabase(
            name=data['name'],
            db_type=db_type,
            host=data['host'],
            port=int(data['port']),
            database_name=data['database_name'],
            username=data['username'],
            password_encrypted=encrypted_password,
            connection_string_template=data.get('connection_string_template'),
            ssl_enabled=data.get('ssl_enabled', False),
            ssl_cert_path=data.get('ssl_cert_path'),
            additional_params=additional_params,
            is_active=data.get('is_active', True),
            created_by=request.user.id
        )
        
        db.session.add(new_db)
        db.session.commit()
        
        # Extract schema from additional_params for response
        schema = None
        if new_db.additional_params:
            schema = new_db.additional_params.get('schema')
        
        return jsonify({
            'message': 'Database configuration created successfully',
            'database': {
                'id': new_db.id,
                'name': new_db.name,
                'db_type': new_db.db_type,
                'host': new_db.host,
                'port': new_db.port,
                'database_name': new_db.database_name,
                'schema': schema,
                'is_active': new_db.is_active
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating database configuration: {str(e)}")
        return jsonify({'error': 'Failed to create database configuration'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>', methods=['PUT'])
@login_required
@require_scope('DATABASE_ADMIN')
def update_database(db_id):
    """Update an external database configuration"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        data = request.json
        
        # Get database type configuration
        db_config = DATABASE_TYPE_CONFIGS.get(database.db_type, {})
        
        # Update fields if provided
        if 'name' in data:
            database.name = data['name']
        if 'host' in data and database.db_type != 'sqlite':
            database.host = data['host']
        if 'port' in data and database.db_type != 'sqlite':
            try:
                port = int(data['port'])
                if port <= 0 or port > 65535:
                    return jsonify({'error': 'Port must be between 1 and 65535'}), 400
                database.port = port
            except (ValueError, TypeError):
                return jsonify({'error': 'Port must be a valid number'}), 400
        if 'database_name' in data:
            database.database_name = data['database_name']
        if 'username' in data and database.db_type != 'sqlite':
            database.username = data['username']
        if 'password' in data and database.db_type != 'sqlite':
            database.password_encrypted = db_connection_manager.encrypt_password(data['password'])
        if 'ssl_enabled' in data and database.db_type != 'sqlite':
            database.ssl_enabled = data['ssl_enabled']
        if 'ssl_cert_path' in data:
            database.ssl_cert_path = data['ssl_cert_path']
        if 'schema' in data:
            # Validate schema name
            schema = data['schema']
            if schema is not None:  # Allow setting to None to remove schema
                if not isinstance(schema, str) or not schema.strip():
                    return jsonify({'error': 'Schema must be a non-empty string'}), 400
                if len(schema) > 100:
                    return jsonify({'error': 'Schema name must be 100 characters or less'}), 400
                
                # Update schema in additional params
                current_params = database.additional_params or {}
                current_params['schema'] = schema.strip()
                database.additional_params = current_params
            else:
                # Remove schema from additional params
                current_params = database.additional_params or {}
                current_params.pop('schema', None)
                database.additional_params = current_params
        if 'additional_params' in data:
            # Merge with existing additional params
            current_params = database.additional_params or {}
            current_params.update(data['additional_params'])
            database.additional_params = current_params
        if 'is_active' in data:
            database.is_active = data['is_active']
        
        database.updated_at = datetime.utcnow()
        db.session.commit()
        
        # Extract schema from additional_params for response
        schema = None
        if database.additional_params:
            schema = database.additional_params.get('schema')
        
        return jsonify({
            'message': 'Database configuration updated successfully',
            'database': {
                'id': database.id,
                'name': database.name,
                'db_type': database.db_type,
                'host': database.host,
                'port': database.port,
                'database_name': database.database_name,
                'schema': schema,
                'is_active': database.is_active
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating database configuration: {str(e)}")
        return jsonify({'error': 'Failed to update database configuration'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>', methods=['DELETE'])
@login_required
@require_scope('DATABASE_ADMIN')
def delete_database(db_id):
    """Delete an external database configuration"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        
        # Check if database has active chat sessions
        if database.chat_sessions:
            active_sessions = [s for s in database.chat_sessions if s.is_active]
            if active_sessions:
                return jsonify({
                    'error': 'Cannot delete database with active chat sessions'
                }), 400
        
        db.session.delete(database)
        db.session.commit()
        
        return jsonify({
            'message': 'Database configuration deleted successfully'
        }), 200
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting database configuration: {str(e)}")
        return jsonify({'error': 'Failed to delete database configuration'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/test', methods=['POST'])
@login_required
@require_scope('DATABASE_ADMIN')
def test_database_connection(db_id):
    """Test connection to an external database"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        
        # Prepare database configuration for testing
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Test the connection
        success, error_message = db_connection_manager.test_connection(db_config)
        
        # Update test results
        database.last_tested_at = datetime.utcnow()
        database.test_status = 'success' if success else 'failed'
        database.test_error_message = error_message
        db.session.commit()
        
        return jsonify({
            'success': success,
            'message': 'Connection successful' if success else f'Connection failed: {error_message}',
            'tested_at': database.last_tested_at.isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error testing database connection: {str(e)}")
        return jsonify({'error': 'Failed to test database connection'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/schema', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_database_schema(db_id):
    """Get schema information for a database"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        data = request.args
        
        # Check if specific schema is requested
        schema_name = data.get('schema')
        
        # Prepare database configuration
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Add schema to additional params if specified
        if schema_name:
            db_config['additional_params']['schema'] = schema_name
        
        # Get schema information
        schema_result = db_connection_manager.get_database_schema(db_config)
        
        return jsonify(schema_result), 200
        
    except Exception as e:
        logger.error(f"Error getting database schema: {str(e)}")
        return jsonify({'error': 'Failed to get database schema'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/schemas', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_database_schemas(db_id):
    """Get list of available schemas in a database"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        
        # Prepare database configuration
        db_config = {
            'db_type': database.db_type,
            'host': database.host,
            'port': database.port,
            'database_name': database.database_name,
            'username': database.username,
            'password_encrypted': database.password_encrypted,
            'ssl_enabled': database.ssl_enabled,
            'ssl_cert_path': database.ssl_cert_path,
            'additional_params': database.additional_params or {}
        }
        
        # Get available schemas
        schemas_result = db_connection_manager.get_available_schemas(db_config)
        
        return jsonify(schemas_result), 200
        
    except Exception as e:
        logger.error(f"Error getting database schemas: {str(e)}")
        return jsonify({'error': 'Failed to get database schemas'}), 500

@database_admin_bp.route('/admin/databases/available-for-connections', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_databases_for_connections():
    """Get list of databases that can be used for connections (tested and active)"""
    try:
        # Get databases that are active and have been successfully tested
        databases = ExternalDatabase.query.filter_by(
            is_active=True,
            test_status='success'
        ).all()
        
        result = []
        for db_config in databases:
            # Get user count for this database
            user_count = db.session.query(user_database_permissions).filter_by(
                external_database_id=db_config.id
            ).count()
            
            # Extract schema from additional_params
            schema = None
            if db_config.additional_params:
                schema = db_config.additional_params.get('schema')
            
            result.append({
                'id': db_config.id,
                'name': db_config.name,
                'db_type': db_config.db_type,
                'database_name': db_config.database_name,
                'host': db_config.host,
                'port': db_config.port,
                'schema': schema,
                'last_tested_at': db_config.last_tested_at.isoformat() if db_config.last_tested_at else None,
                'authorized_users_count': user_count,
                'created_by': db_config.creator.user_name if db_config.creator else None,
                'created_at': db_config.created_at.isoformat()
            })
        
        return jsonify({
            'databases': result,
            'total': len(result)
        }), 200
        
    except Exception as e:
        logger.error(f"Error fetching databases for connections: {str(e)}")
        return jsonify({'error': 'Failed to fetch databases for connections'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/permissions', methods=['GET'])
@login_required
@require_scope('DATABASE_ADMIN')
def get_database_permissions(db_id):
    """Get user permissions for a database"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        
        # Get all users with permissions to this database
        permissions = db.session.query(user_database_permissions).filter_by(
            external_database_id=db_id
        ).all()
        
        result = []
        for perm in permissions:
            user = User.query.get(perm.user_id)
            granted_by_user = User.query.get(perm.granted_by) if perm.granted_by else None
            
            result.append({
                'user_id': perm.user_id,
                'user_name': user.user_name if user else 'Unknown',
                'user_email': user.email if user else 'Unknown',
                'permission_level': perm.permission_level,
                'allowed_schemas': perm.allowed_schemas,  # NULL means all schemas
                'granted_by': granted_by_user.user_name if granted_by_user else 'System',
                'created_at': perm.created_at.isoformat(),
                'expires_at': perm.expires_at.isoformat() if perm.expires_at else None
            })
        
        return jsonify({
            'database_name': database.name,
            'permissions': result,
            'total': len(result)
        }), 200

    except Exception as e:
        logger.error(f"Error getting database permissions: {str(e)}")
        return jsonify({'error': 'Failed to get database permissions'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/permissions', methods=['POST'])
@login_required
@require_scope('DATABASE_ADMIN')
def grant_database_permission(db_id):
    """Grant database access permission to a user"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        data = request.json

        # Validate required fields
        if not data.get('user_id'):
            return jsonify({'error': 'user_id is required'}), 400

        user = User.query.get(data['user_id'])
        if not user:
            return jsonify({'error': 'User not found'}), 404

        permission_level = data.get('permission_level', 'read')
        if permission_level not in ['read', 'admin']:
            return jsonify({'error': 'Invalid permission level. Must be read or admin'}), 400

        # Validate allowed schemas if provided
        allowed_schemas = data.get('allowed_schemas')
        if allowed_schemas is not None:
            if not isinstance(allowed_schemas, list):
                return jsonify({'error': 'allowed_schemas must be a list of schema names'}), 400
            
            # Validate schema names
            for schema in allowed_schemas:
                if not isinstance(schema, str) or not schema.strip():
                    return jsonify({'error': 'Schema names must be non-empty strings'}), 400
                if len(schema) > 100:  # Reasonable limit for schema name length
                    return jsonify({'error': 'Schema names must be 100 characters or less'}), 400

        # Check if permission already exists
        existing_permission = db.session.query(user_database_permissions).filter_by(
            user_id=data['user_id'],
            external_database_id=db_id
        ).first()

        if existing_permission:
            return jsonify({'error': 'User already has permission for this database'}), 400

        # Create new permission
        from datetime import datetime
        expires_at = None
        if data.get('expires_at'):
            try:
                expires_at = datetime.fromisoformat(data['expires_at'])
            except ValueError:
                return jsonify({'error': 'Invalid expires_at format. Use ISO format'}), 400

        # Insert permission
        permission_data = {
            'user_id': data['user_id'],
            'external_database_id': db_id,
            'permission_level': permission_level,
            'granted_by': request.user.id,
            'created_at': datetime.utcnow(),
            'expires_at': expires_at,
            'allowed_schemas': allowed_schemas
        }

        db.session.execute(user_database_permissions.insert().values(**permission_data))
        db.session.commit()

        return jsonify({
            'message': 'Database permission granted successfully',
            'permission': {
                'user_name': user.user_name,
                'user_email': user.email,
                'permission_level': permission_level,
                'database_name': database.name,
                'allowed_schemas': allowed_schemas,
                'expires_at': expires_at.isoformat() if expires_at else None
            }
        }), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error granting database permission: {str(e)}")
        return jsonify({'error': 'Failed to grant database permission'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/permissions/<int:user_id>', methods=['PUT'])
@login_required
@require_scope('DATABASE_ADMIN')
def update_database_permission(db_id, user_id):
    """Update database access permission for a user"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        user = User.query.get_or_404(user_id)
        data = request.json

        # Find existing permission
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=user_id,
            external_database_id=db_id
        ).first()

        if not permission:
            return jsonify({'error': 'Permission not found'}), 404

        # Update permission
        update_data = {}
        if 'permission_level' in data:
            if data['permission_level'] not in ['read', 'admin']:
                return jsonify({'error': 'Invalid permission level. Must be read or admin'}), 400
            update_data['permission_level'] = data['permission_level']

        if 'expires_at' in data:
            if data['expires_at']:
                try:
                    update_data['expires_at'] = datetime.fromisoformat(data['expires_at'])
                except ValueError:
                    return jsonify({'error': 'Invalid expires_at format'}), 400
            else:
                update_data['expires_at'] = None

        if 'allowed_schemas' in data:
            allowed_schemas = data['allowed_schemas']
            if allowed_schemas is not None:
                if not isinstance(allowed_schemas, list):
                    return jsonify({'error': 'allowed_schemas must be a list of schema names'}), 400
                
                # Validate schema names
                for schema in allowed_schemas:
                    if not isinstance(schema, str) or not schema.strip():
                        return jsonify({'error': 'Schema names must be non-empty strings'}), 400
                    if len(schema) > 100:  # Reasonable limit for schema name length
                        return jsonify({'error': 'Schema names must be 100 characters or less'}), 400
            
            update_data['allowed_schemas'] = allowed_schemas

        if update_data:
            db.session.execute(
                user_database_permissions.update()
                .where(user_database_permissions.c.user_id == user_id)
                .where(user_database_permissions.c.external_database_id == db_id)
                .values(**update_data)
            )
            db.session.commit()

        return jsonify({
            'message': 'Database permission updated successfully'
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating database permission: {str(e)}")
        return jsonify({'error': 'Failed to update database permission'}), 500

@database_admin_bp.route('/admin/databases/<int:db_id>/permissions/<int:user_id>', methods=['DELETE'])
@login_required
@require_scope('DATABASE_ADMIN')
def revoke_database_permission(db_id, user_id):
    """Revoke database access permission from a user"""
    try:
        database = ExternalDatabase.query.get_or_404(db_id)
        user = User.query.get_or_404(user_id)

        # Delete permission
        result = db.session.execute(
            user_database_permissions.delete()
            .where(user_database_permissions.c.user_id == user_id)
            .where(user_database_permissions.c.external_database_id == db_id)
        )

        if result.rowcount == 0:
            return jsonify({'error': 'Permission not found'}), 404

        db.session.commit()

        return jsonify({
            'message': 'Database permission revoked successfully'
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error revoking database permission: {str(e)}")
        return jsonify({'error': 'Failed to revoke database permission'}), 500
