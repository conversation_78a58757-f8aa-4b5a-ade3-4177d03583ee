from flask import Blueprint, jsonify, request
from models.models import db, Department, Position, Vertical, Scope, User, user_departments, ExternalDatabase, user_database_permissions
from utils.decorators import admin_required

master_bp = Blueprint('master', __name__)

# Department routes
@master_bp.route('/departments', methods=['GET'])
def get_departments():
    vertical_id = request.args.get('vertical_id', type=int)
    if not vertical_id:
        return jsonify({'error': 'vertical_id is required'}), 400
        
    departments = Department.query.filter_by(vertical_id=vertical_id).all()
    return jsonify([{
        'id': dept.id,
        'name': dept.name,
        'vertical_id': dept.vertical_id
    } for dept in departments])

@master_bp.route('/departments', methods=['POST'])
@admin_required
def create_department():
    data = request.json
    if not data.get('vertical_id'):
        return jsonify({'error': 'vertical_id is required'}), 400
        
    department = Department(
        name=data['name'],
        vertical_id=data['vertical_id']
    )
    db.session.add(department)
    db.session.commit()
    return jsonify({
        'message': 'Department added successfully',
        'department': {
            'id': department.id,
            'name': department.name,
            'vertical_id': department.vertical_id
        }
    })

# Vertical routes
@master_bp.route('/verticals', methods=['GET', 'POST'])
# @admin_required
def manage_verticals():
    if request.method == 'GET':
        verticals = Vertical.query.all()
        scopes = request.args.get('scopes', type=str)            
        return jsonify([{
            'id': vert.id,
            'name': vert.name
        } for vert in verticals])
    
    data = request.json
    vertical = Vertical(name=data['name'])
    db.session.add(vertical)
    db.session.commit()
    return jsonify({'message': 'Vertical added successfully'})

# Scope routes
@master_bp.route('/scopes', methods=['GET', 'POST'])
# @admin_required
def manage_scopes():
    if request.method == 'GET':
        scopes = Scope.query.all()
        return jsonify([{
            'id': scope.id,
            'name': scope.name
        } for scope in scopes])
    
    data = request.json
    scope = Scope(name=data['name'])
    db.session.add(scope)
    db.session.commit()
    return jsonify({'message': 'Scope added successfully'})

@master_bp.route('/users/<int:user_id>/verticals', methods=['GET', 'POST'])
# @admin_required
def manage_user_verticals(user_id):
    user = User.query.get_or_404(user_id)
    
    if request.method == 'GET':
        return jsonify([{
            'id': v.id,
            'name': v.name
        } for v in user.verticals])
    
    data = request.json
    vertical_ids = data.get('vertical_ids', [])
    verticals = Vertical.query.filter(Vertical.id.in_(vertical_ids)).all()
    user.verticals = verticals
    db.session.commit()
    return jsonify({'message': 'User verticals updated successfully'})

@master_bp.route('/users/<int:user_id>/departments', methods=['GET', 'POST'])
# @admin_required
def manage_user_departments(user_id):
    user = User.query.get_or_404(user_id)
    
    if request.method == 'GET':
        vertical_id = request.args.get('vertical_id', type=int)
        if vertical_id:
            departments = Department.query.join(user_departments).filter(
                user_departments.c.user_id == user_id,
                user_departments.c.vertical_id == vertical_id
            ).all()
        else:
            departments = user.departments
            
        return jsonify([{
            'id': d.id,
            'name': d.name,
            'vertical_id': d.vertical_id
        } for d in departments])
    
    data = request.json
    if not data.get('vertical_id'):
        return jsonify({'error': 'vertical_id is required'}), 400
        
    department_ids = data.get('department_ids', [])
    departments = Department.query.filter(
        Department.id.in_(department_ids),
        Department.vertical_id == data['vertical_id']
    ).all()
    
    # Update user departments for this vertical
    stmt = user_departments.delete().where(
        user_departments.c.user_id == user_id,
        user_departments.c.vertical_id == data['vertical_id']
    )
    db.session.execute(stmt)
    
    for dept in departments:
        stmt = user_departments.insert().values(
            user_id=user_id,
            department_id=dept.id,
            vertical_id=data['vertical_id']
        )
        db.session.execute(stmt)
    
    db.session.commit()
    return jsonify({'message': 'User departments updated successfully'})

@master_bp.route('/verticals/<int:vertical_id>', methods=['PUT', 'DELETE'])
@admin_required
def manage_vertical(vertical_id):
    vertical = Vertical.query.get_or_404(vertical_id)
    
    if request.method == 'DELETE':
        # Check if vertical has departments
        if vertical.departments:
            return jsonify({'error': 'Cannot delete vertical with existing departments'}), 400
        db.session.delete(vertical)
        db.session.commit()
        return jsonify({'message': 'Vertical deleted successfully'})
    
    # PUT method
    data = request.json
    vertical.name = data['name']
    db.session.commit()
    return jsonify({
        'message': 'Vertical updated successfully',
        'vertical': {'id': vertical.id, 'name': vertical.name}
    })

@master_bp.route('/departments/<int:dept_id>', methods=['PUT', 'DELETE'])
# @admin_required
def manage_department(dept_id):
    dept = Department.query.get_or_404(dept_id)
    
    if request.method == 'DELETE':
        # Check if department has users
        if dept.users:
            return jsonify({'error': 'Cannot delete department with assigned users'}), 400
        db.session.delete(dept)
        db.session.commit()
        return jsonify({'message': 'Department deleted successfully'})
    
    # PUT method
    data = request.json
    dept.name = data['name']
    if 'vertical_id' in data:
        dept.vertical_id = data['vertical_id']
    db.session.commit()
    return jsonify({
        'message': 'Department updated successfully',
        'department': {
            'id': dept.id,
            'name': dept.name,
            'vertical_id': dept.vertical_id
        }
    })

@master_bp.route('/users/<int:user_id>/master-data', methods=['GET'])
def get_user_master_data(user_id):
    user = User.query.get_or_404(user_id)
    
    return jsonify({
        'verticals': [{
            'id': v.id,
            'name': v.name,
            'departments': [{
                'id': d.id,
                'name': d.name
            } for d in Department.query.filter_by(vertical_id=v.id).all()]
        } for v in user.verticals],
        'scopes': [{
            'id': scope_id,
            'name': Scope.query.get(scope_id).name
        } for scope_id in (user.scopes or [])]
    })

@master_bp.route('/users/<int:user_id>/positions', methods=['GET'])
def get_user_positions(user_id):
    user = User.query.get_or_404(user_id)
    
    if not user.position_id:
        return jsonify([])
    
    # Get user's position level
    user_position = Position.query.get(user.position_id)
    if not user_position:
        return jsonify([])
    
    # Get all positions up to and including user's level
    positions = Position.query.filter(
        Position.level <= user_position.level
    ).order_by(Position.level).all()
    
    return jsonify([{
        'id': pos.id,
        'level': pos.level,
        'name': pos.name
    } for pos in positions])

@master_bp.route('/users/<int:user_id>/positions', methods=['PUT'])
# @admin_required
def update_user_position(user_id):  
    user = User.query.get_or_404(user_id)
    data = request.json
    
    position_id = data.get('position_id')
    if position_id:
        position = Position.query.get_or_404(position_id)
        user.position_id = position.id
        db.session.commit()
        return jsonify({
            'message': 'User position updated successfully',
            'position': {
                'id': position.id,
                'level': position.level,
                'name': position.name
            }
        })
    
    return jsonify({'error': 'position_id is required'}), 400

@master_bp.route('/users/<int:user_id>/scopes', methods=['GET'])
def get_user_scopes(user_id):
    user = User.query.get_or_404(user_id)
    
    if not user.scopes:
        return jsonify([])
    
    # Get all scopes that match the user's scope IDs
    scopes = Scope.query.filter(Scope.id.in_(user.scopes)).all()
    
    return jsonify([{
        'id': scope.id,
        'name': scope.name
    } for scope in scopes])

# Database access management endpoints
@master_bp.route('/users/<int:user_id>/databases', methods=['GET'])
@admin_required
def get_user_databases(user_id):
    """Get databases accessible to a user"""
    user = User.query.get_or_404(user_id)

    # Get user's database permissions
    permissions = db.session.query(user_database_permissions).filter_by(
        user_id=user_id
    ).all()

    result = []
    for perm in permissions:
        database = ExternalDatabase.query.get(perm.external_database_id)
        if database:
            result.append({
                'id': database.id,
                'name': database.name,
                'db_type': database.db_type,
                'permission_level': perm.permission_level,
                'expires_at': perm.expires_at.isoformat() if perm.expires_at else None,
                'created_at': perm.created_at.isoformat()
            })

    return jsonify(result)

@master_bp.route('/users/<int:user_id>/databases', methods=['POST'])
@admin_required
def assign_user_databases(user_id):
    """Assign databases to a user"""
    user = User.query.get_or_404(user_id)
    data = request.json

    try:
        from datetime import datetime

        # Remove existing database permissions
        stmt = user_database_permissions.delete().where(
            user_database_permissions.c.user_id == user_id
        )
        db.session.execute(stmt)

        # Add new database permissions
        if 'databases' in data:
            for db_data in data['databases']:
                database_id = db_data['database_id']
                permission_level = db_data.get('permission_level', 'read')
                expires_at = None

                if db_data.get('expires_at'):
                    try:
                        expires_at = datetime.fromisoformat(db_data['expires_at'])
                    except ValueError:
                        pass  # Skip invalid date format

                # Check if database exists
                database = ExternalDatabase.query.get(database_id)
                if database:
                    stmt = user_database_permissions.insert().values(
                        user_id=user_id,
                        external_database_id=database_id,
                        permission_level=permission_level,
                        granted_by=request.user.id,
                        created_at=datetime.utcnow(),
                        expires_at=expires_at
                    )
                    db.session.execute(stmt)

        db.session.commit()
        return jsonify({'message': 'User database permissions updated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@master_bp.route('/databases/available', methods=['GET'])
@admin_required
def get_available_databases():
    """Get all available databases for assignment"""
    databases = ExternalDatabase.query.filter_by(is_active=True).all()

    return jsonify([{
        'id': db.id,
        'name': db.name,
        'db_type': db.db_type,
        'host': db.host,
        'database_name': db.database_name,
        'created_at': db.created_at.isoformat()
    } for db in databases])

@master_bp.route('/users/<int:user_id>/databases/<int:database_id>', methods=['PUT'])
@admin_required
def update_user_database_permission(user_id, database_id):
    """Update a specific database permission for a user"""
    user = User.query.get_or_404(user_id)
    database = ExternalDatabase.query.get_or_404(database_id)
    data = request.json

    try:
        from datetime import datetime

        # Find existing permission
        permission = db.session.query(user_database_permissions).filter_by(
            user_id=user_id,
            external_database_id=database_id
        ).first()

        if not permission:
            return jsonify({'error': 'Permission not found'}), 404

        # Update permission
        update_data = {}
        if 'permission_level' in data:
            if data['permission_level'] not in ['read', 'admin']:
                return jsonify({'error': 'Invalid permission level. Must be read or admin'}), 400
            update_data['permission_level'] = data['permission_level']

        if 'expires_at' in data:
            if data['expires_at']:
                try:
                    update_data['expires_at'] = datetime.fromisoformat(data['expires_at'])
                except ValueError:
                    return jsonify({'error': 'Invalid expires_at format'}), 400
            else:
                update_data['expires_at'] = None

        if update_data:
            db.session.execute(
                user_database_permissions.update()
                .where(user_database_permissions.c.user_id == user_id)
                .where(user_database_permissions.c.external_database_id == database_id)
                .values(**update_data)
            )
            db.session.commit()

        return jsonify({'message': 'Database permission updated successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400

@master_bp.route('/users/<int:user_id>/databases/<int:database_id>', methods=['DELETE'])
@admin_required
def remove_user_database_permission(user_id, database_id):
    """Remove a database permission from a user"""
    user = User.query.get_or_404(user_id)
    database = ExternalDatabase.query.get_or_404(database_id)

    try:
        # Delete permission
        result = db.session.execute(
            user_database_permissions.delete()
            .where(user_database_permissions.c.user_id == user_id)
            .where(user_database_permissions.c.external_database_id == database_id)
        )

        if result.rowcount == 0:
            return jsonify({'error': 'Permission not found'}), 404

        db.session.commit()
        return jsonify({'message': 'Database permission removed successfully'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 400