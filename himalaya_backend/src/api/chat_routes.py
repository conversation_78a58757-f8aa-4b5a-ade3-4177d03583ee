from flask import Blueprint, request, jsonify, send_file
from flask_cors import CORS
from models.models import db, ChatSession, ChatMessage, User, File, Position, Department, CSVFile, UserNavigation, Vertical
from sqlalchemy import or_
from utils.decorators import login_required, require_scope
from utils.openai_utils import get_ai_response, rephrase_question
from datetime import datetime
import re
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from openai import AzureOpenAI
import traceback
from utils.blob_utils import generate_sas_url
import logging
from utils.visualization_utils import generate_chart, parse_chart_data
from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT, AZURE_SEARCH_INDEX_NAME, AZURE_SEARCH_ADMIN_KEY,
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME, A<PERSON><PERSON>E_OPENAI_EMBEDDING_DEPLOYMENT,
    A<PERSON><PERSON>E_SEARCH_ENHANCED_INDEX_NAME, VECTOR_SEARCH_TOP_K, MIN_VECTOR_SCORE_THRESHOLD,
    VECTOR_SEARCH_RERANK_TOP_K, SEMANTIC_SIMILARITY_THRESHOLD, ENHANCED_PROCESSING_ENABLED
)
from io import BytesIO
import csv
from io import StringIO
##### agent imports #########
# from langchain_core.prompts import ChatPromptTemplate
# from langchain_core.output_parsers import StrOutputParser
# from langchain_openai import ChatOpenAI
# from langchain_core.agents import AgentExecutor, create_react_agent
# from langchain_core.tools import Tool

from agents.planner import create_planner_agent, AgentType
from agents.web import create_web_search_agent
from agents.qa import create_qa_agent
from agents.rag.rag_agent import create_rag_agent
from agents.rag.rag_planner_agent import RAGPlannerAgent
from agents.gpt import create_gpt_agent
from agents.universal_new import create_universal_new_agent
from agents.deep_search_new.orchestrator import DeepSearchOrchestrator
from datetime import datetime
import time

#important code parameters
max_improvement_loops = 3
max_improvement_loops_web_search = 2
max_improvement_loops_deep_think = 4

##### agent imports ##########

# Setup logger
logger = logging.getLogger(__name__)

chat_bp = Blueprint('chat', __name__)

def generate_intelligent_session_name(question):
    """
    Generate an intelligent session name based on the first question using Azure OpenAI
    """
    try:
        # Create a prompt for generating a concise session name
        system_prompt = """You are a helpful assistant that creates concise, descriptive session names based on user questions.

Guidelines:
- Create a short, descriptive title (2-6 words maximum)
- Focus on the main topic or intent of the question
- Use title case formatting
- Be specific but concise
- Avoid generic names like "Chat" or "Question"
- Examples: "Sales Data Analysis", "HR Policy Query", "Product Launch Strategy"

Generate ONLY the session name, no additional text or punctuation."""

        user_prompt = f"Create a session name for this question: {question}"

        # Call Azure OpenAI to generate the session name
        response = openai_client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=50,  # Keep it short
            temperature=0.3  # Low temperature for more consistent naming
        )
        
        # Extract the generated name and clean it
        generated_name = response.choices[0].message.content.strip()
        
        # Fallback validation - ensure it's reasonable length
        if len(generated_name) > 50:
            generated_name = generated_name[:47] + "..."
        
        # If generation fails or returns empty, use a fallback
        if not generated_name or len(generated_name.strip()) == 0:
            generated_name = f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        logger.info(f"Generated intelligent session name: '{generated_name}' for question: '{question[:100]}...'")
        print(f"Generated intelligent session name: '{generated_name}' for question: '{question[:100]}...'")
        
        return generated_name
        
    except Exception as e:
        logger.error(f"Error generating intelligent session name: {str(e)}")
        print(f"Error generating intelligent session name: {str(e)}")
        # Fallback to timestamp-based name
        fallback_name = f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        return fallback_name

# Add CORS support for the chat blueprint
CORS(chat_bp, 
     supports_credentials=True,
     resources={
         r"/*": {
             "origins": [
                 "http://localhost:3000",
                 "https://higpt.himalayawellness.com",
                 "https://aivccontainerfrontendp2.salmonbay-6d5d2866.southindia.azurecontainerapps.io"
             ],
             "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
             "allow_headers": ["Content-Type", "Authorization"],
             "expose_headers": ["Content-Range", "X-Content-Range"],
             "supports_credentials": True
         }
     })

# Initialize Azure OpenAI
openai_client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

def get_search_client():
    try:
        # Use the enhanced processing index for chat searches
        index_name = AZURE_SEARCH_ENHANCED_INDEX_NAME
        print(f"🔍 CHAT SEARCH CLIENT: Using Azure Search index: {index_name}")
        print(f"🔍 CHAT SEARCH CLIENT: Endpoint: {AZURE_SEARCH_SERVICE_ENDPOINT}")
        
        credential = AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
        search_client = SearchClient(
            endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
            index_name=index_name,
            credential=credential
        )
        
        print(f"✅ CHAT SEARCH CLIENT: Successfully created search client for index: {index_name}")
        return search_client
    except Exception as e:
        print(f"❌ CHAT SEARCH CLIENT: Failed to create search client")
        print(traceback.format_exc())
        raise Exception(f"Failed to connect to Azure AI Search: {str(e)}")

# Add at the top of the file
SCOPE_CHAT = 1  # Add this constant

@chat_bp.route('/chat/sessions', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def get_sessions():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        vertical_id = request.args.get('vertical_id', type=int)
        department_id = request.args.get('department_id', type=int)

        # Start with base query filtered by user ID
        query = ChatSession.query.filter_by(
            user_id=request.user.id
        )
        
        # Apply vertical filter if provided
        if vertical_id:
            query = query.filter(ChatSession.vertical_id == vertical_id)
            print(f"Filtering sessions by vertical_id: {vertical_id}")
        
        # Apply department filter if provided
        if department_id:
            query = query.filter(ChatSession.department_id == department_id)
            print(f"Filtering sessions by department_id: {department_id}")
        
        # Apply date filters if provided
        if start_date:
            try:
                # Parse the date and explicitly handle timezone
                # Remove Z and add offset for proper ISO parsing
                if start_date.endswith('Z'):
                    start_date = start_date.replace('Z', '+00:00')
                
                # Convert to UTC datetime object
                start_date_obj = datetime.fromisoformat(start_date)
                print(f"Filtering sessions created on or after: {start_date_obj.isoformat()}")
                
                # Apply filter with exact datetime comparison
                query = query.filter(ChatSession.created_at >= start_date_obj)
            except ValueError as e:
                print(f"Error parsing start_date: {e}")
                return jsonify({'error': 'Invalid start_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS+00:00 or YYYY-MM-DDTHH:MM:SSZ)'}), 400
                
        if end_date:
            try:
                # Parse the date and explicitly handle timezone
                if end_date.endswith('Z'):
                    end_date = end_date.replace('Z', '+00:00')
                
                # Convert to UTC datetime object
                end_date_obj = datetime.fromisoformat(end_date)
                print(f"Filtering sessions created on or before: {end_date_obj.isoformat()}")
                
                # Apply filter with exact datetime comparison
                query = query.filter(ChatSession.created_at <= end_date_obj)
            except ValueError as e:
                print(f"Error parsing end_date: {e}")
                return jsonify({'error': 'Invalid end_date format. Use ISO format (YYYY-MM-DDTHH:MM:SS+00:00 or YYYY-MM-DDTHH:MM:SSZ)'}), 400

        # For debugging: print the SQL query
        print(f"SQL Query: {query}")

        # Join with vertical and department tables to get names
        query = query.outerjoin(Vertical, ChatSession.vertical_id == Vertical.id) \
                     .outerjoin(Department, ChatSession.department_id == Department.id)

        # Order by updated_at and paginate the results
        sessions = query.order_by(ChatSession.updated_at.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return jsonify({
            'items': [{
                'id': session.id,
                'session_name': session.session_name,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'is_active': session.is_active,
                'message_count': len(session.messages),
                'vertical_id': session.vertical_id,
                'department_id': session.department_id,
                'vertical_name': session.vertical.name if session.vertical else None,
                'department_name': session.department.name if session.department else None
            } for session in sessions.items],
            'pagination': {
                'total_items': sessions.total,
                'total_pages': sessions.pages,
                'current_page': sessions.page,
                'per_page': per_page,
                'has_next': sessions.has_next,
                'has_prev': sessions.has_prev
            }
        }), 200

    except Exception as e:
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/chat/sessions/<int:session_id>/messages', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def get_session_messages(session_id):
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)

        session = ChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()

        messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        processed_messages = []
        for msg in messages.items:
            processed_sources = []
            is_web_search = False

            # Check if this is a web search result by looking at sources and metadata
            if msg.search_metadata and msg.search_metadata.get('web_search'):
                is_web_search = True
            elif isinstance(msg.sources, dict) and any(isinstance(value, str) and 'http' in value for value in msg.sources.values()):
                is_web_search = True

            # Check if sources is a list (standard search) or dictionary (web search/structured format)
            if msg.sources:
                # Handle Universal Agent structured sources format (agent-specific keys)
                if isinstance(msg.sources, dict) and any(key in msg.sources for key in ['rag', 'gpt', 'web_search']):
                    # This is a Universal Agent structured sources format
                    all_sources = []
                    
                    # Process RAG sources
                    if 'rag' in msg.sources and isinstance(msg.sources['rag'], list):
                        for source in msg.sources['rag']:
                            if isinstance(source, dict):
                                source_copy = source.copy()
                                if 'file_info' in source_copy:
                                    file_info = source_copy['file_info']
                                    if isinstance(file_info, dict) and 'blob_url' in file_info:
                                        try:
                                            # Generate fresh SAS URL
                                            file_info['blob_url'] = generate_sas_url(file_info['blob_url'])
                                            source_copy['blob_url'] = file_info['blob_url']
                                            source_copy['type'] = 'file'
                                        except Exception as e:
                                            print(traceback.format_exc())
                                            logger.error(f"Error generating SAS URL: {str(e)}")
                                all_sources.append(source_copy)
                    
                    # Process GPT sources
                    if 'gpt' in msg.sources and isinstance(msg.sources['gpt'], list):
                        for source in msg.sources['gpt']:
                            if isinstance(source, dict):
                                all_sources.append(source.copy())
                    
                    # Process web search sources
                    if 'web_search' in msg.sources:
                        web_sources_data = msg.sources['web_search']
                        if isinstance(web_sources_data, dict):
                            for key, url in web_sources_data.items():
                                if isinstance(url, str) and url.startswith('http'):
                                    # Extract domain for display
                                    domain = url.split('//')[-1].split('/')[0]
                                    source_title = domain

                                    # Try to make the source title more readable
                                    if '.' in domain:
                                        parts = domain.split('.')
                                        if len(parts) >= 2:
                                            source_title = parts[-2].capitalize()  # e.g., "example.com" -> "Example"

                                    all_sources.append({
                                        'reference_id': key,
                                        'url': url,
                                        'domain': domain,
                                        'title': source_title,
                                        'type': 'web'
                                    })
                                    is_web_search = True
                    
                    processed_sources = all_sources
                
                # Handle legacy web search sources (dictionary format with URLs)
                elif isinstance(msg.sources, dict) and any(isinstance(value, str) and 'http' in value for value in msg.sources.values()):
                    # For web search results, format as a list of URLs with metadata
                    web_sources = []
                    for key, url in msg.sources.items():
                        if isinstance(url, str) and url.startswith('http'):
                            # Extract domain for display
                            domain = url.split('//')[-1].split('/')[0]
                            source_title = domain

                            # Try to make the source title more readable
                            if '.' in domain:
                                parts = domain.split('.')
                                if len(parts) >= 2:
                                    source_title = parts[-2].capitalize()  # e.g., "example.com" -> "Example"

                            web_sources.append({
                                'reference_id': key,
                                'url': url,
                                'domain': domain,
                                'title': source_title,
                                'type': 'web'
                            })
                    processed_sources = web_sources
                    is_web_search = True

                # Handle standard search sources (list format)
                elif isinstance(msg.sources, list):
                    for source in msg.sources:
                        source_copy = source.copy()
                        if isinstance(source_copy, dict) and 'file_info' in source_copy:
                            file_info = source_copy['file_info']
                            if isinstance(file_info, dict) and 'blob_url' in file_info:
                                try:
                                    # Generate fresh SAS URL
                                    file_info['blob_url'] = generate_sas_url(file_info['blob_url'])
                                    # Add source type for frontend
                                    source_copy['type'] = 'file'
                                except Exception as e:
                                    print(traceback.format_exc())
                                    logger.error(f"Error generating SAS URL: {str(e)}")
                        processed_sources.append(source_copy)
                # Handle any other unexpected format
                else:
                    # Convert primitive types to their appropriate format
                    processed_sources = msg.sources

            processed_messages.append({
                'id': msg.id,
                'question': msg.question,
                'answer': msg.answer,
                'sources': processed_sources,  # Use processed sources with SAS URLs or web references
                'created_at': msg.created_at.isoformat(),
                'file_ids': msg.search_metadata.get('selected_file_ids') if msg.search_metadata else None,
                'web_search': is_web_search  # Use the detected flag instead of just relying on metadata
            })

        return jsonify({
            'items': processed_messages,
            'pagination': {
                'total_items': messages.total,
                'total_pages': messages.pages,
                'current_page': messages.page,
                'per_page': per_page,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        }), 200

    except Exception as e:
        print(traceback.format_exc())
        logger.error(f"Error in get_session_messages: {str(e)}")
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/chat/sessions', methods=['POST'])
@login_required
@require_scope(SCOPE_CHAT)
def create_session():
    try:
        data = request.json
        session_name = data.get('session_name')
        if not session_name:
            session_name = f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        vertical_id = data.get('vertical_id')
        department_id = data.get('department_id')

        # Validate vertical_id if provided
        if vertical_id:
            vertical = Vertical.query.get(vertical_id)
            if not vertical:
                return jsonify({'error': 'Invalid vertical_id. Vertical not found.'}), 400

        # Validate department_id if provided
        if department_id:
            department = Department.query.get(department_id)
            if not department:
                return jsonify({'error': 'Invalid department_id. Department not found.'}), 400
            
            # If both vertical_id and department_id are provided, ensure department belongs to vertical
            if vertical_id and department.vertical_id != vertical_id:
                return jsonify({
                    'error': 'Department does not belong to the specified vertical.'
                }), 400
            
            # If only department_id is provided, automatically set vertical_id
            if not vertical_id:
                vertical_id = department.vertical_id

        session = ChatSession(
            user_id=request.user.id,
            session_name=session_name,
            vertical_id=vertical_id,
            department_id=department_id
        )
        db.session.add(session)
        db.session.commit()

        # Prepare response with additional information
        response_data = {
            'id': session.id,
            'session_name': session.session_name,
            'created_at': session.created_at.isoformat(),
            'vertical_id': session.vertical_id,
            'department_id': session.department_id
        }

        # Add vertical and department names if available
        if session.vertical:
            response_data['vertical_name'] = session.vertical.name
        if session.department:
            response_data['department_name'] = session.department.name

        return jsonify(response_data), 201

    except Exception as e:
        db.session.rollback()
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/chat/sessions/<int:session_id>/messages', methods=['POST'])
@login_required
@require_scope(SCOPE_CHAT)
def add_message(session_id):
    # Initialize comprehensive logging
    request_start_time = datetime.now()
    print(f"\n{'='*80}")
    print(f"🚀 CHAT REQUEST STARTED: {request_start_time.isoformat()}")
    print(f"📋 SESSION ID: {session_id}")
    print(f"👤 USER ID: {request.user.id}")
    print(f"👤 USER EMAIL: {getattr(request.user, 'email', 'N/A')}")
    print(f"{'='*80}")
    
    try:
        session = ChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()

        data = request.json
        if not data or 'question' not in data:
            return jsonify({'error': 'No question provided'}), 400

        # Log incoming request details
        print(f"   Question: {data.get('question', 'N/A')}")

        # Get agent flags from message metadata or defaults
        web_search = data.get('web_search', False)
        work = data.get('work', False)
        gpt = data.get('gpt', False)
        universal = data.get('universal', False)
        deep_search = data.get('deep_think', False)

        # Define the api_agent_type, which is the one which is true above
        if web_search:
            api_agent_type = "web_search"
        elif work:
            api_agent_type = "rag"
        elif gpt:
            api_agent_type = "gpt"
        elif universal:
            api_agent_type = "universal"
        elif deep_search:
            api_agent_type = "deep_search"
        else:
            api_agent_type = "rag"  # Default to RAG if no specific flag is set

        # Create metadata dictionary to store all incoming message information
        message_metadata = {
            'session_id': session_id,
            'question': data.get('question'),
            'query_type': data.get('query_type', 'all'),
            'selected_file_ids': data.get('selected_file_ids', []),
            'vertical_id': data.get('vertical_id') if data.get('query_type') == 'filter' else None,
            'department_id': data.get('department_id') if data.get('query_type') == 'filter' else None,
            'sensitivity_id': data.get('sensitivity_id') if data.get('query_type') == 'filter' else None,
            'user_verticals': [v.id for v in request.user.verticals],
            'user_departments': [d.id for d in request.user.departments],
            'web_search': data.get('web_search', False),  
            'api_agent_type': api_agent_type,
            'deep_search': data.get('deep_think', False),
            'tags': data.get('tags', {}) if data.get('query_type') == 'filter' else None,
            'start_time': datetime.now().isoformat()  # Track processing start time
        }
        
        
        query = File.query

        # Check query type
        query_type = data.get('query_type', 'all')
        selected_file_ids = data.get('selected_file_ids', [])

        if query_type == 'all':
            # Get user's assigned verticals and departments
            user_vertical_ids = [v.id for v in request.user.verticals]
            user_department_ids = [d.id for d in request.user.departments]

            # Filter files by user's assigned departments AND their corresponding verticals
            query = query.join(Department).filter(
                db.and_(
                    File.vertical_id.in_(user_vertical_ids),
                    File.department_id.in_(user_department_ids),
                    # Ensure department belongs to the correct vertical
                    Department.vertical_id.in_(user_vertical_ids)
                )
            )

        elif query_type == 'filter':
            # Vertical must be specified for department filtering
            if 'vertical_id' in data:
                query = query.filter(File.vertical_id == data['vertical_id'])

                # If department is specified, ensure it belongs to the selected vertical
                if 'department_id' in data:
                    # Verify the department belongs to the selected vertical
                    department = Department.query.filter_by(
                        id=data['department_id'],
                        vertical_id=data['vertical_id']
                    ).first()

                    if not department:
                        return jsonify({
                            'error': 'Selected department does not belong to the specified vertical'
                        }), 400

                    query = query.filter(File.department_id == data['department_id'])

            # Filter by sensitivity if provided
            if 'sensitivity_id' in data:
                query = query.filter(File.sensitivity_id == data['sensitivity_id'])

            if 'start_date' in data:
                query = query.filter(File.upload_time >= datetime.fromisoformat(data['start_date']))
            if 'end_date' in data:
                query = query.filter(File.upload_time <= datetime.fromisoformat(data['end_date']))

            # Filter by tags if provided (as dictionary like {"1":"age","2":"gender"})
            if 'tags' in data and data['tags']:

                tags_dict = data['tags']
                if isinstance(tags_dict, dict):
                    print(f"DEBUG - Chat message filtering by tags: {tags_dict}")
                    
                    # Create a list of OR conditions for each tag
                    tag_filters = []
                    for tag_id in tags_dict.keys():
                        tag_filters.append(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
                    
                    # Apply the OR filter if we have any tag filters
                    if tag_filters:
                        query = query.filter(or_(*tag_filters))

        elif query_type == 'selected':
            # Only filter by specific file IDs
            if not selected_file_ids:
                return jsonify({
                    'error': 'No files selected for search. Please select specific files.'
                }), 400
            # When using specific files, still respect position level restrictions
            query = query.filter(File.id.in_(selected_file_ids))

        else:
            return jsonify({'error': 'Invalid query_type. Must be "all", "filter", or "selected"'}), 400

        filtered_files = query.all()
        if not filtered_files and not message_metadata.get('web_search', False):
            return jsonify({
                'error': 'No files found matching the criteria'
            }), 400

        # Get the filtered file IDs
        filtered_file_ids = [file.id for file in filtered_files]

        # Before vector search, prepare the blob names using appropriate URLs
        blob_names = []
        # print(f"🔍 BLOB NAME EXTRACTION: Starting blob name extraction")
        
        if query_type == 'selected':
            # For selected files, use only the intersection of selected and filtered files
            allowed_file_ids = list(set(selected_file_ids) & set(filtered_file_ids))
            files = File.query.filter(File.id.in_(allowed_file_ids)).all()
            # print(f"🔍 BLOB NAME EXTRACTION: Selected files mode - {len(allowed_file_ids)} allowed file IDs")
        else:
            # For other query types, use all filtered files
            files = filtered_files
            # print(f"🔍 BLOB NAME EXTRACTION: All files mode - {len(files)} filtered files")
        
        for i, file in enumerate(files):
            
            if file.media_type in ['video', 'audio']:
                # For media files, use the transcription blob URL for search
                if hasattr(file, 'transcription_blob_url') and file.transcription_blob_url:
                    blob_name = file.transcription_blob_url.split('/')[-1]
                else:
                    blob_name = file.blob_url.split('/')[-1]
            else:
                # For other files, use the original blob URL
                blob_name = file.blob_url.split('/')[-1]
                
            
            blob_names.append(blob_name)

        if not blob_names and not message_metadata.get('web_search', False):
            # print(f"❌ BLOB NAME EXTRACTION: No blob names extracted!")
            return jsonify({
                'error': 'No accessible files found matching the criteria'
            }), 400

        # Add blob names to metadata
        message_metadata.update({
            'blob_names': blob_names
        })
        print(f"🔍 BLOB NAMES: {blob_names}")

        # Get last 5 messages from the session (simplified - no file context filtering)     
        relevant_previous_messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at.desc()).limit(5).all()
        
        print(f"🔍 CONTEXT: Retrieved {len(relevant_previous_messages)} recent messages for context")
        
        #comeback : this should be actually conversation history for session and its not getting used also below as passed balnk""
        # Format conversation history with only relevant messages
        conversation_history = "\n\n".join([
            f"[RECENT EXCHANGE #{i+1}]\nQuestion: {msg.question}\nAnswer: {msg.answer}"
            for i, msg in enumerate(relevant_previous_messages)
        ])

        # Add previous messages and conversation history to metadata
        message_metadata.update({
            'previous_messages': [{
                'question': msg.question,
                'answer': msg.answer,
                'created_at': msg.created_at.isoformat()
            } for msg in relevant_previous_messages],
            'conversation_history': conversation_history,
            'conversation_summary': " "
        })
        #comeback : we should apss conversation history and 1-2 pair of QnA

        # should_use_history = bool(relevant_previous_messages)
        
        # question_data = rephrase_question(
        #     data['question'],
        #     conversation_history if should_use_history else None
        # )
        # message_metadata['rephrased_question'] = question_data['rephrased_question']

        #comeback : rephrase question is from standalone and not from planner

        ############# DIRECT AGENT ROUTING (SIMPLIFIED) #####################
 
        print(f"PROCESSING: Query → Planner → Direct Agent → Response")
            
        try:
            # Create planner agent for routing decision
            from agents.planner import create_planner_agent, AgentType
            
            planner = create_planner_agent(verbose=True)
            
            # Plan the query to determine appropriate agent
            plan_result = planner.graph.invoke({
                "messages": [],
                "query": message_metadata.get('question', data['question']),
                "conversation_history": conversation_history if relevant_previous_messages else None,
                "conversation_summary": message_metadata.get('conversation_summary', ''),
                "web_search": message_metadata.get('web_search', False),
                "agent_type": AgentType.UNKNOWN,
                "api_agent_type": api_agent_type,
                "analysis": {},
                "require_table": False,
                "require_chart": False,
                "chart_type": None
            })
            logger.info(f"===== Planner query analysis - EXECUTED =====")
            print(f"===== Planner query analysis - EXECUTED =====")
            
            #comeback : planner agent should get raw question and not rephrased question. Corrected in planner invoke.
            
            # Extract planner results
            planned_agent_type = plan_result["agent_type"]
            # planned_agent_type = "universal"
            standalone_query = plan_result["query"]
            
            logger.info(f"===== Planner agent invoked ===== EXECUTED")
            logger.info(f"SELECTED AGENT: {planned_agent_type}")
            logger.info(f"STANDALONE QUERY: {standalone_query}")
            print(f"===== Planner agent invoked ===== EXECUTED")
            print(f"SELECTED AGENT: {planned_agent_type}")
            print(f"STANDALONE QUERY: {standalone_query}")
            print(f"{'='*80}")

            # No need to override the agent type since the planner already respects the API flags
            # The planner will only override to "conversation" if the query is conversational
            
            #route to conversation agent for general hi hello thank you etc.
            if planned_agent_type == AgentType.CONVERSATION:
                start_time = datetime.now()
                
                # Import the conversation agent from the agent.py file
                from agents.conversation.agent import create_conversation_agent
                
                # Create conversation agent
                conversation_agent = create_conversation_agent(verbose=True)
                
                # Process the query - let the agent determine the conversation type
                conversation_result = conversation_agent.process_query(
                    query=data['question'],
                    conversation_type="general",  # Just use general type, let the LLM handle the context
                    conversation_history=conversation_history if relevant_previous_messages else None,
                    conversation_summary=message_metadata.get('conversation_summary', '')
                )
                
                # Extract answer
                final_answer = conversation_result.get("answer", "I'm here to help you. How can I assist you today?")
                
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Save to database
                message = ChatMessage(
                    session_id=session_id,
                    question=data['question'],
                    answer=final_answer,
                    sources=[],  # No sources for conversation responses
                    token_usage={'note': 'Conversation agent - token usage not tracked'},
                    search_metadata={
                        'agent_type': 'conversation',
                        'processing_time': processing_time
                    },
                    web_search=False,
                    agent_type='conversation',
                    processing_time=processing_time
                )
                db.session.add(message)
                
                # Update session timestamp
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
                logger.info(f"=== Conversation agent processing complete ===")
                print(f"=== Conversation agent processing complete ===")
                logger.info(f"Processing time: {processing_time:.2f} seconds")
                print(f"Processing time: {processing_time:.2f} seconds")
                
                # Return response
                return jsonify({
                    'answer': message.answer,
                    'created_at': message.created_at.isoformat(),
                    'id': message.id,
                    'metadata': {
                        'agent_type': 'conversation',
                        'processing_time': processing_time
                    },
                    'question': message.question,
                    'sources': [],
                    'web_search': False
                }), 201
               
            # Route to GPT agent for general knowledge questions
            elif planned_agent_type == AgentType.GPT:
                start_time = datetime.now()
                
                # Create GPT agent
                gpt_agent = create_gpt_agent(verbose=True)
                
                # Process the query using the GPT agent
                gpt_result = gpt_agent.process_query(
                    query=data['question'],
                    conversation_history=conversation_history if relevant_previous_messages else None,
                    conversation_summary=message_metadata.get('conversation_summary', '')
                )
                
                # Extract answer and references returned by the GPT agent
                final_answer = gpt_result.get("answer", "I don't have enough information to answer that question.")
                gpt_sources = gpt_result.get("references", [])
                
                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Save to database
                message = ChatMessage(
                    session_id=session_id,
                    question=data['question'],
                    answer=final_answer,
                    sources=gpt_sources,  # Indicate GPT as the sole source
                    token_usage={'note': 'GPT agent - token usage not tracked'},
                    search_metadata={
                        'agent_type': 'gpt',
                        'processing_time': processing_time,
                        'knowledge_based': True
                    },
                    web_search=False,
                    agent_type='gpt',
                    processing_time=processing_time
                )
                db.session.add(message)
                
                # Update session timestamp
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
                logger.info(f"=== GPT agent processing complete ===")
                print(f"=== GPT agent processing complete ===")
                logger.info(f"Processing time: {processing_time:.2f} seconds")
                print(f"Processing time: {processing_time:.2f} seconds")
                
                # Return response
                return jsonify({
                    'answer': message.answer,
                    'created_at': message.created_at.isoformat(),
                    'id': message.id,
                    'metadata': {
                        'agent_type': 'gpt',
                        'processing_time': processing_time,
                        'knowledge_based': True
                    },
                    'question': message.question,
                    'sources': gpt_sources,
                    'web_search': False
                }), 201

            # DIRECT AGENT ROUTING WITH QA EVALUATION (exact himalaya_azure pattern) comeback :rephrase comment
            elif planned_agent_type == AgentType.WEB_SEARCH:
                start_time = datetime.now()
                # Initialize improvement loop variables
                improvement_count = 0
                final_answer = None
                final_references = {}
                
                while improvement_count < max_improvement_loops_web_search:

                    # Create web search agent
                    web_agent = create_web_search_agent(verbose=True)
                    
                    # If this is not the first attempt, use the planner agent again with QA observations
                    if improvement_count > 0 and 'qa_result' in locals() and "evaluation" in qa_result:
                        # Create planner agent with QA observations
                        enhanced_planner = create_planner_agent(verbose=True)
                        
                        # Plan the query with QA observations
                        enhanced_plan_result = enhanced_planner.graph.invoke({
                            "messages": [],
                            "query": data['question'],
                            "conversation_history": conversation_history,
                            "conversation_summary": message_metadata.get('conversation_summary', ''),
                            "web_search": message_metadata.get('web_search', False),
                            "agent_type": AgentType.UNKNOWN,
                            "api_agent_type": api_agent_type,
                            "analysis": {},
                            "qa_observations": qa_result.get("evaluation", {}),
                            "improvement_instructions": improvement_instructions,
                            "selected_files_context": {
                                "selected_file_ids": message_metadata.get('selected_file_ids', []),
                                "blob_names": message_metadata.get('blob_names', []),
                                "query_type": message_metadata.get('query_type', 'all')
                            },
                            "require_table": False,
                            "require_chart": False,
                            "chart_type": None
                        })
                        
                        # Get the updated standalone query from the plan
                        standalone_query = enhanced_plan_result["query"]

                        logger.info(f"Enhanced standalone query for improvement loop {improvement_count}: {standalone_query} =====")
                        print(f"Enhanced standalone query for improvement loop {improvement_count}: {standalone_query} =====")

                    # Prepare the query with improvement instructions if this is not the first attempt
                    enhanced_query = data['question']
                    if improvement_count > 0 and 'improvement_instructions' in locals():
                        enhanced_query = f"{data['question']}\n\nImprovement needed: {improvement_instructions}"
                        
                        logger.info(f"Enhanced query for improvement loop {improvement_count}: {enhanced_query}")
                        print(f"Enhanced query for improvement loop {improvement_count}: {enhanced_query}")

                    # Prepare previous content if this is not the first attempt
                    previous_content = {}
                    scraped_content = {}
                    if improvement_count > 0 and 'web_result' in locals() and "scraped_content" in web_result:
                        scraped_content = web_result["scraped_content"]
                        
                        logger.info(f"Reusing {len(scraped_content)} previously scraped content items")
                        print(f"Reusing {len(scraped_content)} previously scraped content items")
                        
                        # Also set previous_content for backward compatibility
                        previous_content = scraped_content
                    
                    # Execute web search with the standalone query
                    web_result = web_agent.graph.invoke({
                        "messages": [],
                        "query": standalone_query,  # Always use the standalone query from the planner
                        "search_results": [],
                        "scraped_content": scraped_content,  # Pass the existing scraped content
                        "previous_content": previous_content,  # Keep for backward compatibility
                        "response": {},
                        "conversation_id": session_id  # Pass the conversation ID
                    })
                    
                    # Extract answer and references
                    answer = web_result["response"]["answer"]
                    references = web_result["response"]["references"]
                    
                    # # If this is not the first attempt, combine with previous answer
                    # if improvement_count > 0 and final_answer:
                    #     # Check if final_answer already contains "Additional information:"
                    #     if "\n\nAdditional information:" in final_answer:
                    #         # Extract just the core answer without the additional information
                    #         core_answer = final_answer.split("\n\nAdditional information:")[0]
                    #         answer = f"{core_answer}\n\nAdditional information: {answer}"
                    # else:
                    #         answer = f"{final_answer}\n\nAdditional information: {answer}"
                    
                    # Create QA agent to evaluate the answer (exact himalaya_azure pattern)
                    from agents.qa import create_qa_agent
                    qa_agent = create_qa_agent(verbose=True, max_improvement_loops=max_improvement_loops)
                    
                    # Get the last QA pair if available
                    last_qa_pair = {}
                    if len(relevant_previous_messages) >= 2:
                        last_qa_pair = {
                            "question": relevant_previous_messages[-2].question if len(relevant_previous_messages) >= 2 else "",
                            "answer": relevant_previous_messages[-1].answer if len(relevant_previous_messages) >= 1 else ""
                        }
                    
                    print(f"🔍 QA EVALUATION: Evaluating web search answer (iteration {improvement_count + 1})")
                    
                    # Evaluate the answer (exact himalaya_azure QA invoke pattern)
                    qa_result = qa_agent.graph.invoke({
                        "messages": [],
                        "query": data['question'],
                        "conversation_history": conversation_history,
                        "conversation_summary": message_metadata.get('conversation_summary', ''),
                        "last_qa_pair": last_qa_pair,
                        "answer": answer,
                        "references": references,
                        "evaluation": {},
                        "needs_improvement": False,
                        "improvement_count": improvement_count,
                        "improvement_instructions": None,
                        "selected_files_context": {
                            "selected_file_ids": message_metadata.get('selected_file_ids', []),
                            "blob_names": message_metadata.get('blob_names', []),
                            "query_type": message_metadata.get('query_type', 'all')
                        }
                    })
                    
                    # Update final answer and references
                    final_answer = answer
                    final_references.update(references)
                    
                    # Check if improvement is needed
                    needs_improvement = qa_result["needs_improvement"]
                    improvement_instructions = qa_result["improvement_instructions"]
                    
                    if not needs_improvement:
                        logger.info(f"QA approved answer after {improvement_count + 1} iterations =====")
                        print(f"QA approved answer after {improvement_count + 1} iterations =====")
                        break
                    
                    # Increment improvement count
                    improvement_count += 1

                    logger.info(f"Improvement loop {improvement_count}/{max_improvement_loops}")
                    logger.info(f"Improvement instructions: {improvement_instructions}")
                    print(f"Improvement loop {improvement_count}/{max_improvement_loops}")
                    print(f"Improvement instructions: {improvement_instructions}")

                # Calculate processing time
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Clean final answer of any JSON content (himalaya_azure pattern)
                import re
                # Remove any JSON code blocks
                final_answer = re.sub(r'```json.*?```', '', final_answer, flags=re.DOTALL)
                # Remove any raw JSON objects that might have been included
                final_answer = re.sub(r'Additional information: \{.*?\}', '', final_answer, flags=re.DOTALL)
                # Clean up any double newlines created by the removal
                final_answer = re.sub(r'\n{3,}', '\n\n', final_answer)
                final_answer = final_answer.strip()
                
                # Convert final_references to sources format
                web_sources = {}
                for key, ref in final_references.items():
                    web_sources[str(key)] = ref
                
                agent_type = 'direct_web_search_qa'
                #comeback : what is the use of agent_type change here?
                #comeback : not writting converations back to DBs here. My be somewhere else?
                #comeback : we should generate the summary of this conversation for next chats.
                
            elif planned_agent_type == AgentType.RAG:              
                start_time = datetime.now()
                
                # Initialize improvement loop variables
                improvement_count = 0
                final_answer = None
                final_references = {}
                
                #check if deep search is needed
                deep_search = message_metadata.get('deep_search', False)
                
                # Use different max loops based on deep_search flag
                max_loops = max_improvement_loops_deep_think if deep_search else max_improvement_loops
                
                while improvement_count < max_loops:
                    
                    # Create the RAG agent
                    rag_agent = create_rag_agent(verbose=True)
                    
                    # If this is not the first attempt, use enhanced query with improvement instructions
                    current_query = data['question']
                    current_standalone_query = standalone_query
                    
                    #if its improvement loop then use planner agent to get enhanced query
                    #comeback: in future we may get agent_types and otherd as well from here
                    if improvement_count > 0 and 'improvement_instructions' in locals():
                        # Create enhanced query with improvement instructions
                        current_query = f"{data['question']}\n\nImprovement needed: {improvement_instructions}"
                        
                        logger.info(f"Enhanced query for improvement loop {improvement_count}: {current_query}")
                        print(f"Enhanced query for improvement loop {improvement_count}: {current_query}")
                        
                        # Create enhanced standalone query
                        enhanced_planner = create_planner_agent(verbose=True)
                        enhanced_plan_result = enhanced_planner.graph.invoke({
                            "messages": [],
                            "query": data['question'],
                            "conversation_history": conversation_history,
                            "conversation_summary": message_metadata.get('conversation_summary', ''),
                            "web_search": message_metadata.get('web_search', False),
                            "agent_type": AgentType.UNKNOWN,
                            "api_agent_type": api_agent_type,
                            "analysis": {},
                            "qa_observations": qa_result.get("evaluation", {}),
                            "improvement_instructions": improvement_instructions,
                            "selected_files_context": {
                                "selected_file_ids": message_metadata.get('selected_file_ids', []),
                                "blob_names": message_metadata.get('blob_names', []),
                                "query_type": message_metadata.get('query_type', 'all')
                            },
                            "require_table": False,
                            "require_chart": False,
                            "chart_type": None
                        })
                        current_standalone_query = enhanced_plan_result.get("query", standalone_query)
                    
                    # Process the query using the RAG agent
                    rag_result = rag_agent.process_query(
                        query=current_query,
                        standalone_query=current_standalone_query,
                        blob_names=blob_names,
                        deep_search=deep_search
                    )
                    
                    # Get the retrieved documents
                    csv_documents = rag_result.get("csv_documents", [])
                    text_documents = rag_result.get("text_documents", [])
                                        
                    logger.info(f"Retrieved {len(csv_documents)} CSV and {len(text_documents)} text documents")
                    print(f"Retrieved {len(csv_documents)} CSV and {len(text_documents)} text documents")
                    
                    # Check file coverage for selected files
                    # if blob_names and len(blob_names) > 1:
                    #     # User selected multiple files - check if we have content from all of them
                    #     covered_files = set()
                    #     all_documents = csv_documents + text_documents
                    #     for doc in all_documents:
                    #         if 'metadata' in doc and 'metadata_storage_name' in doc['metadata']:
                    #             covered_files.add(doc['metadata']['metadata_storage_name'])
                        
                    #     missing_files = len(blob_names) - len(covered_files)
                    #     if missing_files > 0:
                    #         print(f"⚠️ RAG: File coverage issue - missing content from {missing_files}/{len(blob_names)} selected files")
                    #         if improvement_count > 0:
                    #             # If we've already tried once and still have coverage issues, provide a helpful message
                    #             missing_file_names = []
                    #             for blob_name in blob_names:
                    #                 if blob_name not in covered_files:
                    #                     missing_file_names.append(blob_name.split('_')[-1])  # Extract readable name
                                
                    #             final_answer = f"I can provide information about some of the selected files, but I couldn't find enough relevant content in: {', '.join(missing_file_names)}. This might be because the query doesn't match the content semantically, or the files contain different types of information.\n\nHere's what I found from the available files:\n\n"
                    #             break
                    
                    if csv_documents or text_documents:
                        # Create the RAG Planner agent
                        rag_planner_agent = RAGPlannerAgent(verbose=True)
                        
                        # Process the query using the RAG Planner agent
                        planner_result = rag_planner_agent.process_query(
                            query=current_query,
                            standalone_query=current_standalone_query,
                            csv_documents=csv_documents,
                            text_documents=text_documents,
                            conversation_history=conversation_history if relevant_previous_messages else None,
                            conversation_summary=message_metadata.get('conversation_summary', '')
                        )
                        #comeback : looks good use of coberation history. Please see if its getting used
                        
                        # Get the answer and references
                        answer = planner_result.get("answer", "No answer found.")
                        references = planner_result.get("references", [])
                        
                        # Format references as a dictionary
                        references_dict = {}
                        for i, ref in enumerate(references, 1):
                            references_dict[f"ref_{i}"] = ref
                        
                        # FIXED: Replace answer instead of concatenating to prevent duplication
                        # The improved answer should replace the previous one, not be appended
                        # (Previous logic was concatenating answers causing duplication)
                        
                        # Create QA agent to evaluate the answer
                        from agents.qa import create_qa_agent
                        qa_agent = create_qa_agent(verbose=True, max_improvement_loops=4)  # Increased from default 3 to 4
                        
                        # Get the last QA pair if available
                        last_qa_pair = {}
                        if len(relevant_previous_messages) >= 2:
                            last_qa_pair = {
                                "question": relevant_previous_messages[-2].question if len(relevant_previous_messages) >= 2 else "",
                                "answer": relevant_previous_messages[-1].answer if len(relevant_previous_messages) >= 1 else ""
                            }
                                          
                        logger.info(f"Evaluating RAG answer (iteration {improvement_count + 1})")
                        print(f"Evaluating RAG answer (iteration {improvement_count + 1})")
                        
                        # Evaluate the answer
                        qa_result = qa_agent.graph.invoke({
                            "messages": [],
                            "query": data['question'],
                            "conversation_history": conversation_history,
                            "conversation_summary": message_metadata.get('conversation_summary', ''),
                            "last_qa_pair": last_qa_pair,
                            "answer": answer,
                            "references": references_dict,
                            "evaluation": {},
                            "needs_improvement": False,
                            "improvement_count": improvement_count,
                            "improvement_instructions": None,
                            "selected_files_context": {
                                "selected_file_ids": message_metadata.get('selected_file_ids', []),
                                "blob_names": message_metadata.get('blob_names', []),
                                "query_type": message_metadata.get('query_type', 'all')
                            }
                        })
                        
                        # Update final answer and references
                        final_answer = answer
                        final_references.update(references_dict)
                        
                        # Check if improvement is needed
                        needs_improvement = qa_result["needs_improvement"]
                        improvement_instructions = qa_result["improvement_instructions"]
                        
                        if improvement_instructions:
                            logger.info(f"=== Further improvements needed. Instructions: {improvement_instructions}")
                            print(f"=== Further improvements needed. Instructions: {improvement_instructions}")
                        
                        # Check for hallucinations in the final iteration
                        if not needs_improvement:
                            # Even if QA says no improvements needed, check for hallucinations
                            hallucinations = qa_result.get("evaluation", {}).get("hallucinations_detected", [])
                            source_adherence = qa_result.get("evaluation", {}).get("evaluation", {}).get("source_adherence", 5)
                            
                            if hallucinations and improvement_count >= 3:  # If we've already tried several times
                                # Add disclaimer to the answer about limitations
                                logger.warning(f"⚠️ Answer may still contain hallucinations after {improvement_count + 1} iterations")
                                disclaimer = "Based on the available references, I can only provide limited information on this topic. Some details you're asking about may not be covered in the documents I have access to."
                                final_answer = f"{disclaimer}\n\n{final_answer}"
                                logger.info(f"Added disclaimer to answer due to potential hallucinations")
                            
                            logger.info(f"QA approved answer after {improvement_count + 1} iterations")
                            print(f"QA approved answer after {improvement_count + 1} iterations")
                            break
                        
                        # Increment improvement count
                        improvement_count += 1
                        logger.info(f"Proceeding to improvement loop {improvement_count + 1}")
                        print(f"Proceeding to improvement loop {improvement_count + 1}")
                        
                    else:
                        # No documents found
                        final_answer = "I couldn't find relevant documents to answer your question."
                        break
                
                processing_time = (datetime.now() - start_time).total_seconds()
                
                if final_answer and final_answer != "I couldn't find relevant documents to answer your question.":
                    # Convert references to sources format
                    web_sources = []
                    for ref_key, ref_value in final_references.items():
                        web_sources.append({
                            'chunk_id': ref_value,
                            'content': 'Document content',
                            'metadata': {'source_type': 'document'}
                        })
                    
                    agent_type = 'direct_rag_planner_qa'
                else:
                    web_sources = []
                    agent_type = 'direct_rag_no_docs'
                
            # Add a condition for Universal NEW agent based on planned_agent_type
            elif planned_agent_type == "universal" or planned_agent_type == "universal_new":
                start_time = datetime.now()

                print("🚀 Using Universal NEW Agent (sophisticated multi-agent orchestration)")

                # Ensure blob_names is a list
                blob_names = message_metadata.get('blob_names', [])
                if isinstance(blob_names, bool) or blob_names is None:
                    blob_names = []

                # Log if files are attached
                if blob_names and len(blob_names) > 0:
                    print(f"Files attached for Universal agent: {len(blob_names)} files")
                    print(f"File names: {blob_names}")

                # Create universal NEW agent (enhanced workflow enabled - Deep Search method fixed)
                universal_new_agent = create_universal_new_agent(verbose=True, use_enhanced_workflow=True)

                # Get standalone query
                standalone_query = message_metadata.get('standalone_query', data['question'])

                # Log the start of processing
                logger.info(f"=== Universal NEW agent processing started ===")
                print(f"=== Universal NEW agent processing started ===")
                print(f"   Query: {data['question']}")
                print(f"   Standalone query: {standalone_query}")
                print(f"   Selected files: {len(message_metadata.get('selected_file_ids', []))}")

                try:
                    # Process the query using the sophisticated Universal NEW agent
                    import asyncio
                    result = asyncio.run(universal_new_agent.process_query(
                        query=data['question'],
                        standalone_query=standalone_query,
                        conversation_history=conversation_history if relevant_previous_messages else None,
                        conversation_summary=message_metadata.get('conversation_summary', ''),
                        selected_file_ids=message_metadata.get('selected_file_ids', []),
                        blob_names=blob_names,
                        user_context={
                            'query_type': message_metadata.get('query_type', 'all'),
                            'session_id': session_id,
                            'user_id': request.user.id
                        }
                    ))

                    # Extract answer and references from Universal NEW agent
                    final_answer = result["answer"]

                    # Get references in the new format that matches working systems
                    universal_references = result.get("references", {})
                    print("="*30)
                    print(f"chat_routes universal_new_agent result: {result}")
                    print("="*30)
                    print(f"chat_routes universal_new_agent final_answer: {final_answer}")
                    print("="*30)
                    print(f"chat_routes universal_new_agent references: {universal_references}")
                    print("="*30)

                    # Process the universal agent references and update them with proper blob URLs
                    print("\n===== CHAT_ROUTES: PROCESSING UNIVERSAL AGENT REFERENCES =====")
                    
                    # Import required modules in this scope
                    import re
                    import traceback as tb
                    
                    # Check if the references are already properly formatted
                    references_have_sas_urls = False
                    references_have_clean_names = False
                    
                    if isinstance(universal_references, dict) and 'rag' in universal_references:
                        rag_refs = universal_references['rag']
                        if rag_refs and isinstance(rag_refs, list):
                            # Check first reference for SAS URL and clean name
                            first_ref = rag_refs[0]
                            blob_url = first_ref.get('blob_url', '')
                            file_info = first_ref.get('file_info', {})
                            file_name = file_info.get('file_name', '')
                            
                            # Check if blob URL has SAS token
                            if blob_url and ('se=' in blob_url or 'sig=' in blob_url):
                                references_have_sas_urls = True
                                print("References already have SAS URLs")
                            
                            # Check if file name is clean (no UUID or timestamp)
                            if file_name and not re.match(r'^[0-9a-f]{8}-[0-9a-f]{4}', file_name) and not re.match(r'^[0-9]{8}_', file_name):
                                references_have_clean_names = True
                                print("References already have clean names")
                    
                    print(f"References before processing: {type(universal_references)}")
                    if isinstance(universal_references, dict):
                        for key, refs in universal_references.items():
                            if isinstance(refs, list):
                                print(f"Section '{key}' has {len(refs)} references")
                                if refs and isinstance(refs[0], dict):
                                    print(f"First reference keys: {list(refs[0].keys())}")
                                    if 'file_info' in refs[0]:
                                        print(f"file_info keys: {list(refs[0]['file_info'].keys())}")
                                        print(f"file_name: {refs[0]['file_info'].get('file_name', 'NOT_FOUND')}")
                                        print(f"blob_url: {refs[0]['file_info'].get('blob_url', 'NOT_FOUND')[:30]}...")
                    
                    # Skip additional processing if references already have SAS URLs and clean names
                    web_sources = universal_references.copy()
                    if references_have_sas_urls and references_have_clean_names:
                        print("\n===== CHAT_ROUTES: SKIPPING REFERENCE PROCESSING =====")
                        print("References already have proper SAS URLs and clean file names")
                    # Otherwise proceed with processing
                    elif 'rag' in web_sources:
                        print("\n===== CHAT_ROUTES: PROCESSING RAG REFERENCES =====")
                        print(f"Number of RAG references to process: {len(web_sources['rag'])}")
                        
                        updated_rag_refs = []
                        for i, rag_ref in enumerate(web_sources['rag']):
                            print(f"\nProcessing RAG reference {i+1}:")
                            file_info = rag_ref.get('file_info', {})
                            file_id = file_info.get('id', 0)
                            print(f"  File ID from reference: {file_id}")
                            print(f"  Original file_info: {file_info}")
                            print(f"  Original blob_url: {rag_ref.get('blob_url', 'NOT_FOUND')[:30]}...")

                            # Skip processing if blob_url is already set with a valid SAS token
                            existing_blob_url = rag_ref.get('blob_url', '')
                            if existing_blob_url and ('se=' in existing_blob_url) and ('sig=' in existing_blob_url):
                                print(f"  Reference already has valid SAS URL, skipping regeneration")
                                updated_rag_refs.append(rag_ref)
                                continue

                            # Get the file record by ID or blob name
                            file = None
                            try:
                                from utils.blob_utils import generate_sas_url
                                print(f"  Looking up file in database with ID: {file_id}")
                                file = File.query.get(file_id)
                                
                                # If file not found by ID, try by blob name
                                if not file and 'file_name' in file_info:
                                    file_name = file_info['file_name']
                                    print(f"  Looking up file by name: {file_name}")
                                    file = File.query.filter(File.file_name == file_name).first()
                                    
                                # If still not found, look for it by blob name pattern
                                if not file and 'blob_url' in file_info:
                                    blob_url = file_info['blob_url']
                                    if blob_url:
                                        print(f"  Looking up file by blob URL: {blob_url[:30]}...")
                                        file = File.query.filter(File.blob_url.contains(blob_url)).first()

                                if file and file.blob_url:
                                    print(f"  Found file in DB: {file.file_name}")
                                    sas_blob_url = generate_sas_url(file.blob_url)
                                    print(f"  Generated SAS URL: {sas_blob_url[:50]}...")

                                    # Update the reference with proper blob URL
                                    updated_ref = rag_ref.copy()
                                    updated_ref['blob_url'] = sas_blob_url

                                    # Extract just the filename without path
                                    clean_file_name = file.file_name
                                    if '/' in clean_file_name:
                                        clean_file_name = clean_file_name.split('/')[-1]
                                    if '\\' in clean_file_name:
                                        clean_file_name = clean_file_name.split('\\')[-1]
                                    
                                    # Remove UUID and timestamp prefixes
                                    # UUID prefix (8-4-4-4-12 format)
                                    uuid_pattern = r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}_'
                                    if re.match(uuid_pattern, clean_file_name):
                                        clean_file_name = re.sub(uuid_pattern, '', clean_file_name)
                                    
                                    # Remove timestamp prefix (YYYYMMDD_HHMMSS)
                                    timestamp_pattern = r'^[0-9]{8}_[0-9]{6}_'
                                    if re.match(timestamp_pattern, clean_file_name):
                                        clean_file_name = re.sub(timestamp_pattern, '', clean_file_name)
                                    
                                    print(f"  Clean file name: {clean_file_name}")
                                    
                                    # Update file_info with complete information from database
                                    updated_ref['file_info'] = {
                                        'id': file.id,
                                        'file_name': clean_file_name,  # Clean file name from database
                                        'file_type': getattr(file, 'file_type', 'document'),
                                        'file_size': getattr(file, 'file_size', 0),
                                        'upload_time': file.upload_time.isoformat() if file.upload_time else None,
                                        'media_type': getattr(file, 'media_type', 'document'),
                                        'blob_url': sas_blob_url,
                                        'department_id': file.department_id,
                                        'vertical_id': file.vertical_id
                                    }

                                    # Update the title to use clean file name
                                    updated_ref['title'] = f"Reference {clean_file_name}"

                                    updated_rag_refs.append(updated_ref)
                                    print(f"  Updated reference with clean file name and SAS URL")
                                else:
                                    print(f"  File not found in database or no blob_url for file_id: {file_id}")
                                    # Keep the original reference even if we can't update the blob URL
                                    updated_rag_refs.append(rag_ref)

                            except Exception as e:
                                print(f"  ERROR: Exception processing RAG reference: {e}")
                                print(tb.format_exc())
                                # Keep the original reference
                                updated_rag_refs.append(rag_ref)

                        web_sources['rag'] = updated_rag_refs
                        print(f"\nFinished processing RAG references, final count: {len(web_sources['rag'])}")

                    print("\n===== CHAT_ROUTES: FINAL WEB_SOURCES STRUCTURE =====")
                    if isinstance(web_sources, dict):
                        for key, refs in web_sources.items():
                            if isinstance(refs, list):
                                print(f"Section '{key}' has {len(refs)} references")
                                if refs and isinstance(refs[0], dict) and 'file_info' in refs[0]:
                                    print(f"  First reference file_name: {refs[0]['file_info'].get('file_name', 'NOT_FOUND')}")
                                    print(f"  First reference blob_url: {refs[0]['blob_url'][:30] if refs[0].get('blob_url') else 'MISSING'}...")
                    
                    # Function to flatten references for UI rendering
                    def flatten_references(hierarchical_refs):
                        """
                        Flatten the hierarchical references structure into a single list for UI rendering.
                        
                        Args:
                            hierarchical_refs: Dictionary with keys like 'rag', 'gpt', 'web_search'
                            
                        Returns:
                            List of reference objects with appropriate 'type' field added
                        """
                        flattened = []
                        
                        if not isinstance(hierarchical_refs, dict):
                            print(f"Warning: Expected dict for hierarchical_refs, got {type(hierarchical_refs)}")
                            return hierarchical_refs  # Return as-is if not a dict
                        
                        # Process RAG references
                        if 'rag' in hierarchical_refs and isinstance(hierarchical_refs['rag'], list):
                            for ref in hierarchical_refs['rag']:
                                # Add 'type': 'file' if not already present
                                ref_copy = ref.copy()
                                if 'type' not in ref_copy:
                                    ref_copy['type'] = 'file'
                                # Add title if missing
                                if 'title' not in ref_copy and 'file_info' in ref_copy:
                                    file_name = ref_copy['file_info'].get('file_name', 'Unknown')
                                    ref_copy['title'] = f"Reference {file_name}"
                                flattened.append(ref_copy)
                        
                        # Process GPT references
                        if 'gpt' in hierarchical_refs and isinstance(hierarchical_refs['gpt'], list):
                            for ref in hierarchical_refs['gpt']:
                                # GPT references should already have 'type': 'gpt'
                                flattened.append(ref)
                        
                        # Process Web Search references - convert from dict to list format
                        if 'web_search' in hierarchical_refs and isinstance(hierarchical_refs['web_search'], dict):
                            for ref_id, url in hierarchical_refs['web_search'].items():
                                # Extract domain for display
                                domain = url.split('//')[-1].split('/')[0] if '//' in url else url
                                source_title = domain
                                
                                # Try to make the source title more readable
                                if '.' in domain:
                                    parts = domain.split('.')
                                    if len(parts) >= 2:
                                        source_title = parts[-2].capitalize()  # e.g., "example.com" -> "Example"
                                
                                web_ref = {
                                    'reference_id': ref_id,
                                    'url': url,
                                    'domain': domain,
                                    'title': source_title,
                                    'type': 'web'
                                }
                                flattened.append(web_ref)
                        
                        return flattened
                    
                    # Flatten the hierarchical references structure for UI rendering
                    flattened_references = flatten_references(web_sources)
                    print("\n===== CHAT_ROUTES: FLATTENED REFERENCES =====")
                    print(f"Flattened {len(flattened_references)} references")
                    for i, ref in enumerate(flattened_references):
                        print(f"Ref {i}: type={ref.get('type', 'unknown')}")
                    
                    # Use the flattened references instead of the hierarchical structure
                    web_sources = flattened_references
                    
                    print(f"DEBUG: Final web_sources structure: {web_sources}")

                    print("="*30)
                    agent_type = "universal_new"
                    processing_time = result.get("processing_time", (datetime.now() - start_time).total_seconds())

                    # Log Universal NEW agent capabilities and results
                    metadata = result.get("metadata", {})
                    print(f"🎯 Execution Strategy: {metadata.get('execution_strategy', 'unknown')}")
                    print(f"🤖 Agents Used: {metadata.get('agents_used', [])}")
                    print(f"🔄 Iterations: {metadata.get('iterations_completed', 0)}")
                    print(f"✅ Validation Passed: {metadata.get('validation_passed', False)}")
                    print(f"📊 Confidence Score: {result.get('confidence_score', 0):.2f}")
                    print(f"⚡ Processing Time: {processing_time:.2f}s")

                    logger.info(f"=== Universal NEW agent processing complete ===")
                    print(f"=== Universal NEW agent processing complete ===")
                    logger.info(f"Processing time: {processing_time:.2f} seconds")
                    print(f"Processing time: {processing_time:.2f} seconds")

                except Exception as e:
                    logger.error(f"❌ Universal NEW agent error: {str(e)}")
                    print(f"❌ Universal NEW agent error: {str(e)}")
                    print(tb.format_exc())

                    # Fallback to simple response
                    final_answer = f"I encountered an issue while processing your query with the advanced multi-agent system: {str(e)}"
                    web_sources = {}
                    agent_type = "universal_new_error"
                    processing_time = (datetime.now() - start_time).total_seconds()
            
            # Add deep_search condition based on planned_agent_type or deep_search flag
            elif planned_agent_type == "deep_search" or deep_search:
                start_time = datetime.now()
                
                # Check if query type is 'all' or 'filter' which are not ideal for deep search
                query_type = message_metadata.get('query_type', 'all')
                selected_file_ids = message_metadata.get('selected_file_ids', [])
                blob_names = message_metadata.get('blob_names', [])
                
                # Define error conditions
                is_unsuitable_query_type = query_type == 'all' or query_type == 'filter'
                has_insufficient_files = query_type == 'selected' and (len(selected_file_ids) == 0 or len(blob_names) == 0)
                
                if is_unsuitable_query_type or has_insufficient_files:
                    # Create a friendly error message based on the condition
                    if query_type == 'all':
                        final_answer = "Deep search requires you to select specific files to analyze. Please use the 'Selected Files' option and choose the documents you want me to research in depth."
                    elif query_type == 'filter':
                        final_answer = "For the best deep search results, please select specific files rather than using filters. Deep search works best when analyzing a focused set of documents that you've specifically chosen."
                    elif has_insufficient_files:
                        final_answer = "Deep search requires at least one file to be selected. Please select one or more documents that you'd like me to analyze in depth."
                    
                    # Error type for metadata
                    error_type = 'unsupported_query_type' if is_unsuitable_query_type else 'no_files_selected'
                    
                    # Save to database with error information
                    message = ChatMessage(
                        session_id=session_id,
                        question=data['question'],
                        answer=final_answer,
                        sources=[],
                        token_usage={'note': 'Deep search error'},
                        search_metadata={
                            'agent_type': 'deep_search',
                            'error': error_type,
                            'query_type': query_type,
                            'selected_file_count': len(selected_file_ids),
                            'blob_name_count': len(blob_names)
                        },
                        web_search=False,
                        agent_type='deep_search',
                        processing_time=0.1
                    )
                    db.session.add(message)
                    
                    # Update session timestamp
                    session.updated_at = datetime.utcnow()
                    db.session.commit()
                    
                    # Return response with friendly error message
                    return jsonify({
                        'answer': final_answer,
                        'created_at': message.created_at.isoformat(),
                        'id': message.id,
                        'metadata': {
                            'agent_type': 'deep_search',
                            'error': error_type,
                            'query_type': query_type
                        },
                        'question': message.question,
                        'sources': [],
                        'web_search': False
                    }), 200  # Return 200 OK instead of 400 to display message in UI
                
                print("Using Deep Search agent")
                print(f"Files attached for Deep Search agent: {len(blob_names)} files")
                if blob_names:
                    print(f"File names: {blob_names[:5]}{'...' if len(blob_names) > 5 else ''}")
                
                # Ensure blob_names is a list
                blob_names = message_metadata.get('blob_names', [])
                if isinstance(blob_names, bool) or blob_names is None:
                    blob_names = []
                
                # Log if files are attached
                if blob_names and len(blob_names) > 0:
                    print(f"Files attached for Deep Search agent: {len(blob_names)} files")
                    print(f"File names: {blob_names}")
                    
                # Create deep search orchestrator (new file-centric system)
                print("🧠 Using NEW Deep Search File-Centric System")
                deep_search_orchestrator = DeepSearchOrchestrator(verbose=True)
                
                # Log the start of deep search processing
                logger.info(f"=== Deep Search agent processing started ===")
                print(f"=== Deep Search agent processing started ===")
                print(f"Query: {data['question']}")
                print(f"Standalone query: {standalone_query}")
                
                try:
                    # Process the query using new file-centric system
                    import asyncio
                    result = asyncio.run(deep_search_orchestrator.process_deep_search_query(
                        query=data['question'],
                        selected_file_ids=selected_file_ids,
                        conversation_history=conversation_history if relevant_previous_messages else None,
                        conversation_summary=message_metadata.get('conversation_summary', ''),
                        user_context={
                            'standalone_query': standalone_query,
                            'blob_names': blob_names,
                            'query_type': query_type
                        }
                    ))
                    
                    # Extract answer and processing time from new file-centric system
                    final_answer = result["answer"]

                    # Log new system capabilities
                    orchestrator_metadata = result.get("orchestrator_metadata", {})
                    planner_decision = result.get("planner_decision", {})
                    excel_results = result.get("excel_results", [])
                    non_excel_results = result.get("non_excel_results", [])

                    print(f"🎯 Query Type: {planner_decision.get('query_type', 'unknown')}")
                    print(f"⚙️ Processing Strategy: Excel={planner_decision.get('excel_strategy', 'unknown')}, "
                          f"Non-Excel={planner_decision.get('non_excel_strategy', 'unknown')}")
                    print(f"📊 Results: Excel={len(excel_results)}, Non-Excel={len(non_excel_results)}")
                    print(f"⚡ Performance: {orchestrator_metadata.get('total_processing_time', 0):.2f}s, "
                          f"Files: {orchestrator_metadata.get('files_processed', 0)}")
                    
                    # Clean up the answer by removing the references section if present
                    import re
                    # Remove the references section at the end of the answer
                    final_answer = re.sub(r'(\n+)?---\s*\n+### References:(\n|.)*$', '', final_answer)
                    # Remove any trailing whitespace or newlines
                    final_answer = final_answer.rstrip()
                    
                    print(f"chat_routes final_answer after cleaning: {final_answer}")
                    
                    # Get references from the result
                    raw_references = result.get("references", [])
                    
                    # Handle both direct references list and structured format with deep_search_references key
                    if isinstance(raw_references, dict) and 'deep_search_references' in raw_references:
                        # New format matching Universal Agent format
                        print(f"Found structured references with deep_search_references key: {len(raw_references['deep_search_references'])} references")
                        web_sources = raw_references['deep_search_references']
                    else:
                        # Legacy format with direct references list
                        web_sources = raw_references
                    
                    # Function to flatten references for UI rendering
                    def flatten_references(hierarchical_refs):
                        """
                        Flatten the hierarchical references structure into a single list for UI rendering.
                        
                        Args:
                            hierarchical_refs: Dictionary with keys like 'rag', 'gpt', 'web_search'
                            
                        Returns:
                            List of reference objects with appropriate 'type' field added
                        """
                        flattened = []
                        
                        if not isinstance(hierarchical_refs, dict):
                            print(f"Warning: Expected dict for hierarchical_refs, got {type(hierarchical_refs)}")
                            return hierarchical_refs  # Return as-is if not a dict
                        
                        # Process RAG references
                        if 'rag' in hierarchical_refs and isinstance(hierarchical_refs['rag'], list):
                            for ref in hierarchical_refs['rag']:
                                # Add 'type': 'file' if not already present
                                ref_copy = ref.copy()
                                if 'type' not in ref_copy:
                                    ref_copy['type'] = 'file'
                                # Add title if missing
                                if 'title' not in ref_copy and 'file_info' in ref_copy:
                                    file_name = ref_copy['file_info'].get('file_name', 'Unknown')
                                    ref_copy['title'] = f"Reference {file_name}"
                                flattened.append(ref_copy)
                        
                        # Process GPT references
                        if 'gpt' in hierarchical_refs and isinstance(hierarchical_refs['gpt'], list):
                            for ref in hierarchical_refs['gpt']:
                                # GPT references should already have 'type': 'gpt'
                                flattened.append(ref)
                        
                        # Process Web Search references - convert from dict to list format
                        if 'web_search' in hierarchical_refs and isinstance(hierarchical_refs['web_search'], dict):
                            for ref_id, url in hierarchical_refs['web_search'].items():
                                # Extract domain for display
                                domain = url.split('//')[-1].split('/')[0] if '//' in url else url
                                source_title = domain
                                
                                # Try to make the source title more readable
                                if '.' in domain:
                                    parts = domain.split('.')
                                    if len(parts) >= 2:
                                        source_title = parts[-2].capitalize()  # e.g., "example.com" -> "Example"
                                
                                web_ref = {
                                    'reference_id': ref_id,
                                    'url': url,
                                    'domain': domain,
                                    'title': source_title,
                                    'type': 'web'
                                }
                                flattened.append(web_ref)
                        
                        return flattened
                    
                    # Check if we need to flatten references structure
                    if isinstance(web_sources, dict) and ('rag' in web_sources or 'gpt' in web_sources or 'web_search' in web_sources):
                        # Flatten the hierarchical references structure for UI rendering
                        flattened_references = flatten_references(web_sources)
                        print("\n===== CHAT_ROUTES: FLATTENED DEEP SEARCH REFERENCES =====")
                        print(f"Flattened {len(flattened_references)} references")
                        web_sources = flattened_references
                    
                    reasoning_trace = planner_decision.get("reasoning", "")
                    verification_score = 0.8  # Default score for new system
                    iteration_count = 1
                    print("="*30)
                    print(f"chat_routes result: {result}")
                    print("="*30)
                    print(f"chat_routes final_answer: {final_answer}")
                    print("="*30)
                    print(f"chat_routes web_sources: {web_sources}")
                    print("="*30)
                    
                    # Process deep_search citations directly here
                    has_metadata_format = web_sources and isinstance(web_sources, list) and all(isinstance(source, dict) and 'metadata' in source and isinstance(source['metadata'], dict) and 'file_id' in source['metadata'] for source in web_sources)
                    has_structured_format = web_sources and isinstance(web_sources, list) and all(isinstance(source, dict) and ('file_name' in source or 'blob_name' in source) for source in web_sources)

                    if has_metadata_format or has_structured_format:
                        print(f"Processing deep_search citations directly: {len(web_sources)} sources found")
                        if has_metadata_format:
                            print(f"Format detected: metadata format")
                        else:
                            print(f"Format detected: structured format")
                            
                        processed_sources = []
                        
                        # Create a mapping of selected file IDs to their corresponding files
                        selected_files_map = {}
                        if selected_file_ids:
                            print(f"Using selected_file_ids for matching: {selected_file_ids}")
                            selected_files = File.query.filter(File.id.in_(selected_file_ids)).all()
                            print(f"Found {len(selected_files)} selected files in database:")
                            for file in selected_files:
                                print(f"  - ID: {file.id}, Name: {file.file_name}, Blob: {file.blob_url}")
                                # Store by both ID and blob name for easier lookup
                                selected_files_map[file.id] = file
                                blob_name = file.blob_url.split('/')[-1] if file.blob_url else None
                                if blob_name:
                                    selected_files_map[blob_name] = file
                                    
                        # Process each reference based on its format
                        for i, source in enumerate(web_sources):
                            file = None
                            
                            # Handle structured format (from new Deep Search format)
                            if has_structured_format:
                                file_name = source.get('file_name', '')
                                file_id = source.get('file_id', 0)
                                blob_name = source.get('blob_name', file_name)
                                
                                print(f"Processing structured reference {i+1}: file_name={file_name}, file_id={file_id}")
                                
                                # Try to get file by ID first
                                if file_id:
                                    try:
                                        file = File.query.get(file_id)
                                        if file:
                                            print(f"Found file by ID: id={file.id}, name={file.file_name}")
                                    except Exception as e:
                                        print(f"Error getting file by ID {file_id}: {str(e)}")
                                
                                # If not found by ID, try by blob name
                                if not file and blob_name:
                                    try:
                                        file = File.query.filter(File.blob_url.contains(blob_name)).first()
                                        if file:
                                            print(f"Found file by blob name: id={file.id}, name={file.file_name}")
                                    except Exception as e:
                                        print(f"Error finding file by blob name {blob_name}: {str(e)}")
                                
                                # If still not found, try by file name
                                if not file and file_name:
                                    try:
                                        file = File.query.filter(File.file_name == file_name).first()
                                        if file:
                                            print(f"Found file by name: id={file.id}, name={file.file_name}")
                                    except Exception as e:
                                        print(f"Error finding file by name {file_name}: {str(e)}")
                                
                                # If still not found, try position-based matching with selected files
                                if not file and selected_file_ids:
                                    if len(selected_file_ids) == 1:
                                        # If only one file is selected, use it
                                        file_id = selected_file_ids[0]
                                        file = File.query.get(file_id)
                                        print(f"Using single selected file: id={file_id}")
                                    elif len(selected_file_ids) > 0:
                                        # If multiple files are selected, use position-based matching
                                        file_id = selected_file_ids[i % len(selected_file_ids)]
                                        file = File.query.get(file_id)
                                        print(f"Using position-based matching: id={file_id}")
                            
                            # Handle metadata format
                            elif has_metadata_format:
                                file_id = source.get('metadata', {}).get('file_id', 0)
                                if file_id:
                                    try:
                                        file = File.query.get(file_id)
                                        if file:
                                            print(f"Found file by metadata file_id: id={file.id}, name={file.file_name}")
                                    except Exception as e:
                                        print(f"Error getting file by metadata file_id {file_id}: {str(e)}")
                            
                            # Process the file if found
                            if file:
                                from utils.blob_utils import generate_sas_url
                                sas_blob_url = generate_sas_url(file.blob_url)
                                
                                # Extract clean filename without path
                                clean_file_name = file.file_name
                                if '/' in clean_file_name:
                                    clean_file_name = clean_file_name.split('/')[-1]
                                if '\\' in clean_file_name:
                                    clean_file_name = clean_file_name.split('\\')[-1]
                                
                                # Create reference in format compatible with UI
                                processed_sources.append({
                                    'chunk_id': f"file_{file.id}",
                                    'content': source.get('content', f"Document: {clean_file_name}"),
                                    'score': source.get('confidence_score', 0.9),
                                    'blob_url': sas_blob_url,
                                    'file_info': {
                                        'id': file.id,
                                        'file_name': clean_file_name,
                                        'file_type': getattr(file, 'file_type', 'document'),
                                        'file_size': getattr(file, 'file_size', 0),
                                        'upload_time': file.upload_time.isoformat() if file.upload_time else None,
                                        'media_type': getattr(file, 'media_type', 'document'),
                                        'blob_url': sas_blob_url,
                                        'department_id': file.department_id,
                                        'vertical_id': file.vertical_id,
                                        'page': 1
                                    },
                                    'type': 'file'  # Add type field for UI
                                })
                            else:
                                # Fallback if file not found
                                display_name = source.get('file_name', f"Document_{i+1}")
                                processed_sources.append({
                                    'chunk_id': f"doc_{i}",
                                    'content': f"Document: {display_name}",
                                    'score': 0.7,
                                    'type': 'file'  # Add type field for UI
                                })
                        
                        # Replace web_sources with processed_sources
                        print(f"Processed {len(processed_sources)} deep_search references")
                        web_sources = processed_sources
                    
                    # Log successful processing
                    logger.info(f"Deep Search completed successfully with verification score: {verification_score}")
                    logger.info(f"Required {iteration_count} iteration(s) to generate answer")
                    print(f"Deep Search completed successfully with verification score: {verification_score}")
                    print(f"Required {iteration_count} iteration(s) to generate answer")
                    
                except Exception as e:
                    # Log the error
                    error_message = str(e)
                    logger.error(f"Deep Search agent error: {error_message}")
                    print(f"Error in Deep Search agent: {error_message}")
                    print(traceback.format_exc())
                    
                    # Create a friendly error message
                    final_answer = "I encountered an error while performing deep search analysis. This might be due to the complexity of the documents or technical issues. Please try again with different files or a more specific query."
                    web_sources = []
                    reasoning_trace = f"Error occurred: {error_message}"
                    verification_score = 0.0
                    iteration_count = 0
                
                # Ensure agent_type is consistently set as "deep_search" for source processing
                agent_type = "deep_search"
                processing_time = (datetime.now() - start_time).total_seconds()
                
                # Create metadata for database
                deep_search_metadata = {
                    'agent_type': agent_type,
                    'processing_time': processing_time,
                    'verification_score': verification_score,
                    'iteration_count': iteration_count,
                    'query_type': query_type,
                    'selected_file_count': len(selected_file_ids),
                    'blob_name_count': len(blob_names),
                    'reasoning_trace_length': len(reasoning_trace) if reasoning_trace else 0
                }
                
                logger.info(f"=== Deep Search agent processing complete ===")
                print(f"=== Deep Search agent processing complete ===")
                logger.info(f"Processing time: {processing_time:.2f} seconds")
                print(f"Processing time: {processing_time:.2f} seconds")
                
                # Save to database
                message = ChatMessage(
                    session_id=session_id,
                    question=data['question'],
                    answer=final_answer,
                    sources=web_sources,
                    token_usage={'note': 'Deep search agent - token usage not tracked'},
                    search_metadata=deep_search_metadata,
                    web_search=False,
                    agent_type=agent_type,
                    processing_time=processing_time
                )
                db.session.add(message)
                
                # Update session timestamp
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
                # Return response
                return jsonify({
                    'answer': message.answer,
                    'created_at': message.created_at.isoformat(),
                    'id': message.id,
                    'metadata': deep_search_metadata,
                    'question': message.question,
                    'sources': message.sources,
                    'web_search': False
                }), 201
            
            else:
                # Fallback for unknown agent types
                logger.info(f"�� CODE_USAGE: Direct fallback processing - EXECUTED")
                final_answer = "I'm not sure how to process that query. Please try rephrasing."
                web_sources = []
                agent_type = 'direct_fallback'
                processing_time = 0.1
            
            logger.info(f"=== Direct processing with QA evaluation successful completion ===")
            print(f"=== Direct processing with QA evaluation successful completion ===")
            logger.info(f"Processing time: {processing_time:.2f} seconds")
            print(f"Processing time: {processing_time:.2f} seconds")
            logger.info(f"Answer length: {len(final_answer)} characters")
            print(f"Answer length: {len(final_answer)} characters")

            if 'improvement_count' in locals():
                logger.info(f"QA iterations: {improvement_count + 1} iteration(s)")
                print(f"QA iterations: {improvement_count + 1} iteration(s)")
            if 'needs_improvement' in locals():
                logger.info(f"QA final status: {'Needs Improvement' if needs_improvement else '✅ Approved'}")
                print(f"QA final status: {'Needs Improvement' if needs_improvement else '✅ Approved'}")
            
            # Process sources for database storage
            # Skip processing for Universal NEW agent as it creates sources in correct format
            if web_sources and isinstance(web_sources, list) and agent_type != "universal_new":
                # Debug logging to check agent_type and web_sources format
                print(f"DEBUG - Processing sources for agent_type: {agent_type}")
                print(f"DEBUG - web_sources type: {type(web_sources)}")
                print(f"DEBUG - web_sources first item type: {type(web_sources[0]) if web_sources else 'None'}")
                print(f"DEBUG - web_sources first item keys: {web_sources[0].keys() if web_sources and isinstance(web_sources[0], dict) else 'None'}")
                
                # Process regular document sources
                for i, source in enumerate(web_sources):
                    source_chunk_id = source.get('chunk_id')
                    if source_chunk_id and source_chunk_id.startswith('chunk_'):
                        try:
                            chunk_parts = source_chunk_id.split('_')
                            if len(chunk_parts) >= 2:
                                file_id = int(chunk_parts[1])
                                file = File.query.get(file_id)
                                if file:
                                    from utils.blob_utils import generate_sas_url
                                    sas_blob_url = generate_sas_url(file.blob_url)
                                    
                                    # Extract clean filename without path
                                    clean_file_name = file.file_name
                                    if '/' in clean_file_name:
                                        clean_file_name = clean_file_name.split('/')[-1]
                                    if '\\' in clean_file_name:
                                        clean_file_name = clean_file_name.split('\\')[-1]
                                    
                                    source.update({
                                        'blob_url': sas_blob_url,
                                        'score': source.get('score', 0.8),
                                        'file_info': {
                                            'id': file.id,
                                            'file_name': clean_file_name,
                                            'file_type': getattr(file, 'file_type', 'document'),
                                            'file_size': getattr(file, 'file_size', 0),
                                            'upload_time': file.upload_time.isoformat() if file.upload_time else None,
                                            'media_type': getattr(file, 'media_type', 'document'),
                                            'blob_url': sas_blob_url,
                                            'department_id': file.department_id,
                                            'vertical_id': file.vertical_id
                                        }
                                    })
                        except (ValueError, IndexError):
                            pass
            
            # Create metadata for the message with enhanced tracking for Universal NEW agent
            direct_metadata = {
                'web_search': message_metadata.get('web_search', False),
                'agent_type': agent_type,
                'processing_time': processing_time,
                'direct_routing': True,  # Flag to indicate direct routing was used
                'qa_evaluation': True,   # Flag to indicate QA evaluation was used
                'qa_evaluation_pattern': 'himalaya_azure',  # Track the pattern used
                'improvement_count': locals().get('improvement_count', 0) if 'improvement_count' in locals() else 0,
                'qa_approved': not locals().get('needs_improvement', True) if 'needs_improvement' in locals() else True,
                'question_rephrasing': {
                    'original_question': data['question'],
                    'rephrased_question': message_metadata.get('rephrased_question', data['question'])
                },
                'selected_file_ids': message_metadata.get('selected_file_ids', []),
                'tags': message_metadata.get('tags', {})
            }

            # Add Universal NEW agent specific metadata if applicable
            if agent_type == "universal_new" and 'result' in locals():
                universal_metadata = result.get("metadata", {})
                direct_metadata.update({
                    'universal_new_agent': True,
                    'execution_strategy': universal_metadata.get('execution_strategy', 'unknown'),
                    'agents_used': universal_metadata.get('agents_used', []),
                    'iterations_completed': universal_metadata.get('iterations_completed', 0),
                    'validation_passed': universal_metadata.get('validation_passed', False),
                    'confidence_score': result.get('confidence_score', 0.0),
                    'planner_reasoning': universal_metadata.get('planner_reasoning', ''),
                    'workflow_completed': universal_metadata.get('workflow_completed', False),
                    'reference_stats': universal_metadata.get('reference_stats', {}),
                    'context_summary': universal_metadata.get('context_summary', {})
                })
            
            # Save to database
            message = ChatMessage(
                session_id=session_id,
                question=data['question'],
                answer=final_answer,
                sources=web_sources,
                token_usage={'note': 'Direct routing - token usage not tracked'},
                search_metadata=direct_metadata,
                web_search=message_metadata.get('web_search', False),
                agent_type=agent_type,
                processing_time=processing_time
            )
            db.session.add(message)

            # Update session timestamp
            session.updated_at = datetime.utcnow()
            db.session.commit()
            
            logger.info(f"=== Direct processing database save ===")
            print(f"=== Direct processing database save ===")

            # Debug: Check what's being saved and returned
            print("="*50)
            print(f"DEBUG: web_sources before saving: {web_sources}")
            print(f"DEBUG: message.sources after saving: {message.sources}")
            print(f"DEBUG: type of message.sources: {type(message.sources)}")
            print("="*50)

            # Prepare response metadata
            response_metadata = {
                'direct_routing': True,
                'qa_evaluation': True,  # Flag to indicate QA evaluation was used
                'qa_evaluation_pattern': 'himalaya_azure',
                'agent_type': agent_type,
                'processing_time': processing_time,
                'improvement_count': locals().get('improvement_count', 0) if 'improvement_count' in locals() else 0,
                'qa_approved': not locals().get('needs_improvement', True) if 'needs_improvement' in locals() else True,
                'question_rephrasing': {
                    'original_question': data['question'],
                    'rephrased_question': message_metadata.get('rephrased_question', data['question'])
                },
                'selected_file_ids': message_metadata.get('selected_file_ids', []),
                'tags': message_metadata.get('tags'),
                'web_search': message_metadata.get('web_search', False)
            }

            # Add Universal NEW agent specific metadata to response if applicable
            if agent_type == "universal_new" and 'result' in locals():
                universal_metadata = result.get("metadata", {})
                response_metadata.update({
                    'universal_new_agent': True,
                    'execution_strategy': universal_metadata.get('execution_strategy', 'unknown'),
                    'agents_used': universal_metadata.get('agents_used', []),
                    'iterations_completed': universal_metadata.get('iterations_completed', 0),
                    'validation_passed': universal_metadata.get('validation_passed', False),
                    'confidence_score': result.get('confidence_score', 0.0),
                    'planner_reasoning': universal_metadata.get('planner_reasoning', ''),
                    'workflow_completed': universal_metadata.get('workflow_completed', False)
                })

            # Return response
            return jsonify({
                'answer': message.answer,
                'created_at': message.created_at.isoformat(),
                'id': message.id,
                'metadata': response_metadata,
                'question': message.question,
                'sources': message.sources,
                'web_search': message_metadata.get('web_search', False),
                'direct_routing': True,  # Flag to indicate direct routing was used
                'qa_evaluation': True   # Flag to indicate QA evaluation was used
            }), 201
                
        except Exception as direct_error:
            logger.error(f"=== Direct processing exception ===")
            print(f"=== Direct processing exception ===")
            logger.error(f"Error: {str(direct_error)}")
            print(f"Error: {str(direct_error)}")
            import traceback as tb
            print(tb.format_exc())
            
            # Simple error response
            return jsonify({
                'error': f'Processing failed: {str(direct_error)}',
                'agent_type': 'direct_error'
            }), 500

        # Check agentic processing status for routing decisions
        agentic_complete = locals().get('agentic_processing_complete', False)
        
 
    except Exception as e:
        logger.error(f"=== Main exception handler ===")
        print(f"=== Main exception handler ===")
        logger.error(f"Error: {str(e)}")
        print(f"   Error: {str(e)}")
        print(f"   Session ID: {session_id}")
        print(f"   Processing Time: {(datetime.now() - request_start_time).total_seconds():.2f} seconds")
        print(f"{'='*80}")
        import traceback as tb
        print(tb.format_exc())
        return jsonify({'error': str(e)}), 500

# Add this new route for updating session name
@chat_bp.route('/chat/sessions/<int:session_id>', methods=['PUT'])
@login_required
@require_scope(SCOPE_CHAT)
def update_session(session_id):
    try:
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        session = ChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()

        # Handle session_name updates
        if 'session_name' in data:
            session.session_name = data['session_name']
        else:
            # If no session_name provided and current name starts with "Untitled", generate intelligent name
            if session.session_name.startswith("Untitled"):
                # Check if session has messages to generate intelligent name from
                messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.created_at).all()
                if messages:
                    try:
                        # Get the first message's question
                        first_question = messages[0].question
                        
                        # Generate intelligent session name
                        intelligent_name = generate_intelligent_session_name(first_question)
                        original_name = session.session_name
                        session.session_name = intelligent_name
                        
                        logger.info(f"Generated intelligent name for session {session_id}: '{original_name}' -> '{intelligent_name}'")
                        print(f"Generated intelligent name for session {session_id}: '{original_name}' -> '{intelligent_name}'")
                        
                    except Exception as naming_error:
                        logger.error(f"Failed to generate intelligent session name for session {session_id}: {str(naming_error)}")
                        print(f"Failed to generate intelligent session name for session {session_id}: {str(naming_error)}")
                        # Keep the original name if intelligent naming fails

        # Update vertical_id if provided
        if 'vertical_id' in data:
            vertical_id = data['vertical_id']
            if vertical_id is not None:
                vertical = Vertical.query.get(vertical_id)
                if not vertical:
                    return jsonify({'error': 'Invalid vertical_id. Vertical not found.'}), 400
            session.vertical_id = vertical_id

        # Update department_id if provided
        if 'department_id' in data:
            department_id = data['department_id']
            if department_id is not None:
                department = Department.query.get(department_id)
                if not department:
                    return jsonify({'error': 'Invalid department_id. Department not found.'}), 400
                
                # If vertical_id is also being updated, use the new value, otherwise use existing
                current_vertical_id = data.get('vertical_id', session.vertical_id)
                if current_vertical_id and department.vertical_id != current_vertical_id:
                    return jsonify({
                        'error': 'Department does not belong to the specified vertical.'
                    }), 400
                
                # If only department_id is provided and no current vertical, set vertical_id
                if not current_vertical_id:
                    session.vertical_id = department.vertical_id
            
            session.department_id = department_id

        session.updated_at = datetime.utcnow()
        db.session.commit()

        # Prepare response with all session information
        response_data = {
            'id': session.id,
            'session_name': session.session_name,
            'updated_at': session.updated_at.isoformat(),
            'vertical_id': session.vertical_id,
            'department_id': session.department_id
        }

        # Add vertical and department names if available
        if session.vertical:
            response_data['vertical_name'] = session.vertical.name
        if session.department:
            response_data['department_name'] = session.department.name

        return jsonify(response_data), 200

    except Exception as e:
        db.session.rollback()
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

# Add this new route for deleting a chat session
@chat_bp.route('/chat/sessions/<int:session_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_CHAT)
def delete_session(session_id):
    try:
        session = ChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()

        # Delete all messages associated with the session
        ChatMessage.query.filter_by(session_id=session_id).delete()

        # Delete the session itself
        db.session.delete(session)
        db.session.commit()

        return jsonify({
            'message': 'Chat session deleted successfully',
            'id': session_id
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error deleting chat session: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

# Add this new route for exporting chat history
@chat_bp.route('/chat/sessions/<int:session_id>/export', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def export_chat_history(session_id):
    try:
        # Get the chat session and verify ownership
        session = ChatSession.query.filter_by(
            id=session_id,
            user_id=request.user.id
        ).first_or_404()

        # Get all messages for this session
        messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at).all()

        # Determine the export format (default to CSV)
        export_format = request.args.get('format', 'csv').lower()

        if export_format == 'csv':
            # Create a string buffer to write CSV data
            output = StringIO()
            writer = csv.writer(output)

            # Write headers
            writer.writerow(['Timestamp', 'Question', 'Answer', 'Sources'])

            # Write message data
            for message in messages:
                # Format sources as a readable string
                sources_str = ''
                if message.sources:
                    source_files = set()
                    for source in message.sources:
                        if isinstance(source, dict) and 'file_info' in source:
                            file_info = source.get('file_info', {})
                            file_name = file_info.get('file_name', 'Unknown File')
                            source_files.add(file_name)
                    sources_str = '; '.join(source_files)

                writer.writerow([
                    message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    message.question,
                    message.answer,
                    sources_str
                ])

            # Convert to bytes
            output_str = output.getvalue()
            bytes_output = BytesIO(output_str.encode('utf-8-sig'))  # Use UTF-8 with BOM for Excel compatibility

            # Generate filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"chat_history_{session.session_name}_{timestamp}.csv"

            return send_file(
                bytes_output,
                mimetype='text/csv',
                as_attachment=True,
                download_name=filename
            )

        else:
            return jsonify({
                'error': 'Unsupported export format. Currently only CSV is supported.'
            }), 400

    except Exception as e:
        logger.error(f"Error exporting chat history: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

# Add this new route for code usage analysis
@chat_bp.route('/chat/debug/code-usage-help', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def get_code_usage_help():
    """Debug endpoint to help analyze code usage patterns"""
    try:
        help_info = {
            'message': 'Code usage tracking is active. Look for logs with "🔍 CODE_USAGE:" prefix.',
            'major_code_sections': [
                'add_message function ENTRY',
                'Session validation - EXECUTED',
                'Request data validation - EXECUTED',
                'Message metadata creation - EXECUTED',
                'Web/Deep search parameter parsing - EXECUTED',
                'Agent routing decision point - EXECUTED',
                'File access verification section - EXECUTED/SKIPPED',
                'Previous messages retrieval - EXECUTED',
                'Conversation history filtering - EXECUTED',
                'Question rephrasing - EXECUTED/SKIPPED',
                'Agentic processing section - ENTERED',
                'Agentic chat handler execution - EXECUTED',
                'Agentic processing completion/fallback - EXECUTED',
                'Traditional web search section - ENTERED/SKIPPED',
                'Traditional RAG section - ENTERED/SKIPPED',
                'Source processing - EXECUTED/SKIPPED',
                'Database save operations - EXECUTED',
                'Exception handlers - EXECUTED'
            ],
            'how_to_use': [
                '1. Make a chat request to /chat/sessions/{session_id}/messages',
                '2. Check the application logs for "🔍 CODE_USAGE:" entries',
                '3. Analyze which sections show EXECUTED vs SKIPPED',
                '4. Identify unused code paths that can potentially be removed',
                '5. Look for sections that are always EXECUTED vs conditionally used'
            ],
            'log_format': '🔍 CODE_USAGE: [section_name] - [EXECUTED/SKIPPED/ENTERED]'
        }
        
        return jsonify(help_info), 200
        
    except Exception as e:
        logger.error(f"Error in code usage help endpoint: {str(e)}")
        return jsonify({'error': str(e)}), 500

# Add this new route for testing SAS URL generation
@chat_bp.route('/chat/test-sas/<int:file_id>', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def test_sas_url(file_id):
    """Test endpoint to verify SAS URL generation for a specific file"""
    try:
        file = File.query.get(file_id)
        if not file:
            return jsonify({'error': 'File not found'}), 404
        
        # Generate SAS URL
        sas_url = generate_sas_url(file.blob_url)
        
        return jsonify({
            'file_id': file.id,
            'file_name': file.file_name,
            'original_blob_url': file.blob_url,
            'sas_url': sas_url,
            'sas_url_length': len(sas_url),
            'has_sas_token': '?' in sas_url and 'sig=' in sas_url,
            'media_type': getattr(file, 'media_type', 'document')
        }), 200
        
    except Exception as e:
        print(f"Error testing SAS URL: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

# ============================================================================
# USER NAVIGATION APIs
# ============================================================================

@chat_bp.route('/user-navigation', methods=['POST'])
@login_required
@require_scope(SCOPE_CHAT)
def create_user_navigation():
    """Create a new user navigation entry"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate required fields
        required_fields = ['vertical_id', 'department_id']
        missing_fields = [field for field in required_fields if field not in data]
        if missing_fields:
            return jsonify({'error': f'Missing required fields: {", ".join(missing_fields)}'}), 400
        
        # Get user_id from authenticated request
        user_id = request.user.id
        vertical_id = data['vertical_id']
        department_id = data['department_id']
        
        # Validate that the vertical exists
        vertical = Vertical.query.get(vertical_id)
        if not vertical:
            return jsonify({'error': 'Vertical not found'}), 404
        
        # Validate that the department exists and belongs to the vertical
        department = Department.query.filter_by(id=department_id, vertical_id=vertical_id).first()
        if not department:
            return jsonify({'error': 'Department not found or does not belong to the specified vertical'}), 404
        
        # Check if this combination already exists
        existing_navigation = UserNavigation.query.filter_by(
            user_id=user_id,
            vertical_id=vertical_id,
            department_id=department_id
        ).first()
        
        if existing_navigation:
            return jsonify({'error': 'This user navigation combination already exists'}), 409
        
        # Create new user navigation entry
        user_navigation = UserNavigation(
            user_id=user_id,
            vertical_id=vertical_id,
            department_id=department_id
        )
        
        db.session.add(user_navigation)
        db.session.commit()
        
        return jsonify({
            'message': f'User navigation created successfully : {data}',
            'data': user_navigation.to_dict()
        }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error creating user navigation: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500

@chat_bp.route('/user-navigation', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT)
def get_user_navigation():
    """Get user navigation entries - defaults to current user's navigation unless user_id filter is provided"""
    try:
        # Get query parameters for filtering
        user_id = request.args.get('user_id', type=int)
        vertical_id = request.args.get('vertical_id', type=int)
        department_id = request.args.get('department_id', type=int)
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # Build query
        query = UserNavigation.query
        
        # If no user_id filter provided, default to current user
        if not user_id:
            user_id = request.user.id
        
        # Apply filters
        query = query.filter(UserNavigation.user_id == user_id)
        if vertical_id:
            query = query.filter(UserNavigation.vertical_id == vertical_id)
        if department_id:
            query = query.filter(UserNavigation.department_id == department_id)
        
        # Join with related tables to get names
        query = query.join(User).join(Vertical).join(Department)
        
        # Order by created_at desc and paginate
        user_navigations = query.order_by(UserNavigation.created_at.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        # Convert to dictionary format
        navigation_list = []
        for navigation in user_navigations.items:
            nav_dict = navigation.to_dict()
            nav_dict['user_name'] = navigation.user.user_name
            nav_dict['user_email'] = navigation.user.email
            navigation_list.append(nav_dict)
        
        return jsonify({
            'items': navigation_list,
            'pagination': {
                'total_items': user_navigations.total,
                'total_pages': user_navigations.pages,
                'current_page': user_navigations.page,
                'per_page': per_page,
                'has_next': user_navigations.has_next,
                'has_prev': user_navigations.has_prev
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting user navigation: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500



@chat_bp.route('/user-navigation', methods=['PUT'])
@login_required
@require_scope(SCOPE_CHAT)
def update_user_navigation():
    """Update or create user navigation entry for the current user"""
    try:
        data = request.json
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Validate that at least one field is provided
        if 'vertical_id' not in data and 'department_id' not in data:
            return jsonify({'error': 'At least one field (vertical_id or department_id) must be provided'}), 400
        
        # Get user_id from authenticated request
        user_id = request.user.id
        
        vertical_id = data.get('vertical_id')
        department_id = data.get('department_id')
        
        # Validate vertical_id if provided
        if vertical_id:
            vertical = Vertical.query.get(vertical_id)
            if not vertical:
                return jsonify({'error': 'Vertical not found'}), 404
        
        # Validate department_id if provided and ensure it belongs to the vertical
        if department_id:
            if vertical_id:
                # If both are provided, check that department belongs to vertical
                department = Department.query.filter_by(id=department_id, vertical_id=vertical_id).first()
                if not department:
                    return jsonify({'error': 'Department not found or does not belong to the specified vertical'}), 404
            else:
                # If only department_id is provided, get the department and its vertical
                department = Department.query.get(department_id)
                if not department:
                    return jsonify({'error': 'Department not found'}), 404
                vertical_id = department.vertical_id  # Use the department's vertical
        
        # Get existing navigation entry for this user
        existing_navigation = UserNavigation.query.filter_by(user_id=user_id).first()
        
        if existing_navigation:
            # Update existing record
            if 'vertical_id' in data:
                existing_navigation.vertical_id = vertical_id
            if 'department_id' in data:
                existing_navigation.department_id = department_id
            
            # Check for duplicate combinations after update
            duplicate_check = UserNavigation.query.filter(
                UserNavigation.user_id == user_id,
                UserNavigation.vertical_id == existing_navigation.vertical_id,
                UserNavigation.department_id == existing_navigation.department_id,
                UserNavigation.id != existing_navigation.id
            ).first()
            
            if duplicate_check:
                return jsonify({'error': 'This user navigation combination already exists'}), 409
            
            existing_navigation.updated_at = datetime.utcnow()
            db.session.commit()
            
            # Reload the object to get updated relationships
            updated_navigation = UserNavigation.query.options(
                db.joinedload(UserNavigation.user),
                db.joinedload(UserNavigation.vertical),
                db.joinedload(UserNavigation.department)
            ).get(existing_navigation.id)
            
            nav_dict = updated_navigation.to_dict()
            nav_dict['user_name'] = updated_navigation.user.user_name
            nav_dict['user_email'] = updated_navigation.user.email
            
            return jsonify({
                'message': 'User navigation updated successfully',
                'data': nav_dict
            }), 200
            
        else:
            # Create new record if it doesn't exist
            # For new records, both vertical_id and department_id are required
            if not vertical_id or not department_id:
                return jsonify({'error': 'Both vertical_id and department_id are required for creating new navigation entry'}), 400
            
            # Check if this combination already exists for the user
            duplicate_check = UserNavigation.query.filter_by(
                user_id=user_id,
                vertical_id=vertical_id,
                department_id=department_id
            ).first()
            
            if duplicate_check:
                return jsonify({'error': 'This user navigation combination already exists'}), 409
            
            # Create new navigation entry
            new_navigation = UserNavigation(
                user_id=user_id,
                vertical_id=vertical_id,
                department_id=department_id
            )
            
            db.session.add(new_navigation)
            db.session.commit()
            
            # Reload the object to get relationships
            created_navigation = UserNavigation.query.options(
                db.joinedload(UserNavigation.user),
                db.joinedload(UserNavigation.vertical),
                db.joinedload(UserNavigation.department)
            ).get(new_navigation.id)
            
            nav_dict = created_navigation.to_dict()
            nav_dict['user_name'] = created_navigation.user.user_name
            nav_dict['user_email'] = created_navigation.user.email
            
            return jsonify({
                'message': 'User navigation created successfully',
                'data': nav_dict
            }), 201
        
    except Exception as e:
        db.session.rollback()
        logger.error(f"Error updating user navigation by user ID: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'error': str(e)}), 500


