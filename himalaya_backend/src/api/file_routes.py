from flask import Blueprint, request, jsonify
from models.models import (
    db, File, FileFormat, FileType, User, Position, Sensitivity, Tag,
    FileProcessingMetadata, DocumentChunk, DocumentTable,
    ExcelFile, CSVFile, CSVEmbedding
)
from sqlalchemy.orm import joinedload
from utils.decorators import login_required
from werkzeug.utils import secure_filename
from azure.storage.blob import BlobServiceClient
import os
from datetime import datetime
import uuid
from functools import wraps
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from openai import AzureOpenAI
from azure.core.exceptions import ResourceNotFoundError

from utils.openai_utils import get_ai_response, rephrase_question
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_CONTAINER_NAME,
    AZURE_SEARCH_SERVICE_ENDPOINT, AZURE_SEARCH_INDEX_NAME, AZURE_SEARCH_ADMIN_KEY,
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    ENHANCED_PROCESSING_ENABLED  # Add enhanced processing setting
)

# Import enhanced processing service
try:
    from services.enhanced_processing_service import enhanced_processing_service
    ENHANCED_PROCESSING_AVAILABLE = True
    print("✅ Enhanced Processing Service imported successfully")
except ImportError as e:
    ENHANCED_PROCESSING_AVAILABLE = False
    print(f"⚠️ Enhanced Processing Service not available: {e}")

file_bp = Blueprint('file', __name__)

# Azure Blob Storage configuration
CONNECTION_STRING = AZURE_STORAGE_CONNECTION_STRING
CONTAINER_NAME = AZURE_STORAGE_CONTAINER_NAME

# Azure AI Search configuration
SEARCH_SERVICE_ENDPOINT = AZURE_SEARCH_SERVICE_ENDPOINT
SEARCH_INDEX_NAME = AZURE_SEARCH_INDEX_NAME
SEARCH_ADMIN_KEY = AZURE_SEARCH_ADMIN_KEY

# Initialize Azure OpenAI
openai_client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

# Scope constants
SCOPE_VIEW_FILES = 4  # VIEW_FILE
SCOPE_UPLOAD_FILE = 2  # UPLOAD_FILE
SCOPE_REMOVE_FILE = 3  # REMOVE_FILE

def require_scope(scope_id):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = User.query.get(request.user.id)
            if not user or not user.scopes or scope_id not in user.scopes:
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'You do not have the required permissions for this operation'
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def get_blob_service_client():
    try:
        return BlobServiceClient.from_connection_string(CONNECTION_STRING)
    except Exception as e:
        raise Exception(f"Failed to connect to Azure Blob Storage: {str(e)}")

def get_search_client():
    try:
        credential = AzureKeyCredential(SEARCH_ADMIN_KEY)
        return SearchClient(
            endpoint=SEARCH_SERVICE_ENDPOINT,
            index_name=SEARCH_INDEX_NAME,
            credential=credential
        )
    except Exception as e:
        raise Exception(f"Failed to connect to Azure AI Search: {str(e)}")

def trigger_enhanced_processing(file_id, file_name):
    """
    Trigger enhanced document processing for uploaded file
    """
    if not ENHANCED_PROCESSING_ENABLED or not ENHANCED_PROCESSING_AVAILABLE:
        print(f"⚠️ Enhanced processing skipped for file {file_id} - not enabled or available")
        return None
    
    try:
        print(f"🚀 Starting enhanced processing for file {file_id}: {file_name}")
        
        # Trigger enhanced processing
        result = enhanced_processing_service.process_file(file_id, force_reprocess=False)
        
        print(f"✅ Enhanced processing completed for file {file_id}: {result.get('status', 'unknown')}")
        return result
        
    except Exception as e:
        print(f"❌ Enhanced processing failed for file {file_id}: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            'status': 'failed',
            'error': str(e)
        }

def _comprehensive_file_cleanup(file, blob_service_client, container_client, search_client):
    """
    Comprehensive cleanup of all file-related data across all systems.
    
    Args:
        file: File database record
        blob_service_client: Azure Blob Service client
        container_client: Azure Blob Container client
        search_client: Azure AI Search client
        
    Returns:
        Dict with cleanup results
    """
    cleanup_details = []
    partial_cleanup = []
    
    try:
        # Models are now imported at the top of the file
        
        # 1. Delete from Azure AI Search indexes
        try:
            # Delete chunks from search index
            chunks = DocumentChunk.query.filter_by(file_id=file.id).all()
            for chunk in chunks:
                if chunk.azure_search_doc_id:
                    try:
                        search_client.delete_documents([{"id": chunk.azure_search_doc_id}])
                        cleanup_details.append(f"Deleted search document: {chunk.azure_search_doc_id}")
                    except Exception as e:
                        partial_cleanup.append(f"Failed to delete search document {chunk.azure_search_doc_id}: {str(e)}")
            
            # Delete tables from search index
            tables = DocumentTable.query.filter_by(file_id=file.id).all()
            for table in tables:
                if table.azure_search_doc_id:
                    try:
                        search_client.delete_documents([{"id": table.azure_search_doc_id}])
                        cleanup_details.append(f"Deleted search table: {table.azure_search_doc_id}")
                    except Exception as e:
                        partial_cleanup.append(f"Failed to delete search table {table.azure_search_doc_id}: {str(e)}")
                        
        except Exception as e:
            partial_cleanup.append(f"Azure AI Search cleanup failed: {str(e)}")
        
        # 2. Delete related blob files (CSV exports, etc.)
        try:
            # Delete CSV files from agentic system
            csv_files = CSVFile.query.filter_by(original_file_id=file.id).all()
            for csv_file in csv_files:
                if csv_file.azure_url:
                    try:
                        csv_blob_name = csv_file.azure_url.split('/')[-1]
                        csv_blob_client = container_client.get_blob_client(csv_blob_name)
                        csv_blob_client.delete_blob()
                        cleanup_details.append(f"Deleted CSV blob: {csv_blob_name}")
                    except Exception as e:
                        partial_cleanup.append(f"Failed to delete CSV blob {csv_blob_name}: {str(e)}")
            
            # Delete Excel files from agentic system
            excel_files = ExcelFile.query.filter_by(original_file_id=file.id).all()
            for excel_file in excel_files:
                if excel_file.azure_url:
                    try:
                        excel_blob_name = excel_file.azure_url.split('/')[-1]
                        excel_blob_client = container_client.get_blob_client(excel_blob_name)
                        excel_blob_client.delete_blob()
                        cleanup_details.append(f"Deleted Excel blob: {excel_blob_name}")
                    except Exception as e:
                        partial_cleanup.append(f"Failed to delete Excel blob {excel_blob_name}: {str(e)}")
                        
        except Exception as e:
            partial_cleanup.append(f"Agentic blob cleanup failed: {str(e)}")
        
        # 3. Delete main file blob
        try:
            blob_name = file.blob_url.split('/')[-1]
            blob_client = container_client.get_blob_client(blob_name)
            blob_client.delete_blob()
            cleanup_details.append(f"Deleted main file blob: {blob_name}")
        except ResourceNotFoundError:
            cleanup_details.append(f"Main file blob not found (already deleted): {blob_name}")
        except Exception as e:
            partial_cleanup.append(f"Failed to delete main file blob: {str(e)}")
        
        # 4. Delete database records (in correct order due to foreign key constraints)
        try:
            # Delete CSV embeddings first (they reference csv_files.csv_id)
            csv_files_for_embeddings = CSVFile.query.filter_by(original_file_id=file.id).all()
            csv_embeddings_count = 0
            for csv_file in csv_files_for_embeddings:
                embeddings = CSVEmbedding.query.filter_by(csv_id=csv_file.csv_id).all()
                for embedding in embeddings:
                    db.session.delete(embedding)
                    csv_embeddings_count += 1
            cleanup_details.append(f"Deleted {csv_embeddings_count} CSV embeddings")
            
            # Delete CSV files
            csv_files = CSVFile.query.filter_by(original_file_id=file.id).all()
            for csv_file in csv_files:
                db.session.delete(csv_file)
            cleanup_details.append(f"Deleted {len(csv_files)} CSV file records")
            
            # Delete Excel files
            excel_files = ExcelFile.query.filter_by(original_file_id=file.id).all()
            for excel_file in excel_files:
                db.session.delete(excel_file)
            cleanup_details.append(f"Deleted {len(excel_files)} Excel file records")
            
            # Delete document chunks
            chunks = DocumentChunk.query.filter_by(file_id=file.id).all()
            for chunk in chunks:
                db.session.delete(chunk)
            cleanup_details.append(f"Deleted {len(chunks)} document chunks")
            
            # Delete document tables
            tables = DocumentTable.query.filter_by(file_id=file.id).all()
            for table in tables:
                db.session.delete(table)
            cleanup_details.append(f"Deleted {len(tables)} document tables")
            
            # Delete processing metadata
            metadata = FileProcessingMetadata.query.filter_by(file_id=file.id).first()
            if metadata:
                db.session.delete(metadata)
                cleanup_details.append("Deleted file processing metadata")
            
            # Finally, delete the main file record
            db.session.delete(file)
            cleanup_details.append("Deleted main file record")
            
        except Exception as e:
            partial_cleanup.append(f"Database cleanup failed: {str(e)}")
            raise  # Re-raise to trigger rollback
        
        return {
            'success': True,
            'details': cleanup_details,
            'partial_cleanup': partial_cleanup
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'partial_cleanup': partial_cleanup,
            'details': cleanup_details
        }

@file_bp.route('/files', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def upload_files():
    try:
        if 'files[]' not in request.files:
            return jsonify({'error': 'No files provided'}), 400

        print("DEBUG START - File Upload Process")
        # Print all form data
        print("Form data keys:", list(request.form.keys()))
        for key, value in request.form.items():
            print(f"Form field '{key}': {value}")

        files = request.files.getlist('files[]')
        file_format_id = request.form.get('file_format_id')
        file_type_id = request.form.get('file_type_id')
        department_id = request.form.get('department_id')
        vertical_id = request.form.get('vertical_id')
        
        # Handle both 'comments' and 'comment' fields
        comments = request.form.get('comments', '')
        if not comments:
            comments = request.form.get('comment', '')
            
        # Check if user wants to force upload (bypass duplicate check)
        force_upload = request.form.get('force_upload', 'false').lower() == 'true'
        
        # Handle tags data
        tags_str = request.form.get('tags', '')
        print(f"DEBUG - Raw tags string: '{tags_str}'")
        
        # Initialize file_tags
        file_tags_json = None
        
        if tags_str:
            try:
                import json
                tags_data = json.loads(tags_str)
                print(f"DEBUG - Tags data parsed successfully")
                print(f"DEBUG - Tags data: {tags_data}")
                print(f"DEBUG - Tags data type: {type(tags_data)}")
                
                # Store the entire tags dictionary directly
                if isinstance(tags_data, dict):
                    file_tags_json = tags_data
                    print(f"DEBUG - Using tags dictionary: {file_tags_json}")
                # If it's a list
                elif isinstance(tags_data, list):
                    file_tags_json = {"list": tags_data}
                # If it's a single value
                else:
                    file_tags_json = {"single": tags_data}
            except Exception as e:
                print(f"DEBUG - Error parsing tags JSON: {str(e)}")
        
        print(f"DEBUG - Final file_tags_json: {file_tags_json}")

        # NEW: Check for duplicate files before processing (unless force_upload is true)
        if not force_upload:
            print("DEBUG - Checking for duplicate files...")
            duplicate_files = []
            user_id = request.user.id
            
            for file in files:
                if file.filename == '':
                    continue
                    
                secure_name = secure_filename(file.filename)
                # Check if file with same name already exists for the same user in the same vertical and department
                existing_file = File.query.filter_by(
                    file_name=secure_name,
                    uploaded_by=user_id,
                    vertical_id=vertical_id,
                    department_id=department_id
                ).first()
                
                if existing_file:
                    duplicate_files.append({
                        'file_name': secure_name,
                        'existing_file_id': existing_file.id,
                        'existing_upload_time': existing_file.upload_time.isoformat(),
                        'existing_blob_url': existing_file.blob_url,
                        'uploaded_by_user': existing_file.uploader.user_name if existing_file.uploader else 'Unknown',
                        'uploaded_by_user_id': existing_file.uploaded_by
                    })
                    print(f"DEBUG - Duplicate file found: {secure_name} by user {user_id} in vertical {vertical_id}, department {department_id}")
            
            # If duplicates found, return response with options
            if duplicate_files:
                print(f"DEBUG - Found {len(duplicate_files)} duplicate files")
                return jsonify({
                    'duplicate_files_detected': True,
                    'message': f'Found {len(duplicate_files)} file(s) with the same name(s) that you have already uploaded in this department and vertical.',
                    'duplicate_files': duplicate_files,
                    'total_files_to_upload': len([f for f in files if f.filename != '']),
                    'conflict_scope': {
                        'user_id': user_id,
                        'vertical_id': vertical_id,
                        'department_id': department_id,
                        'description': 'Duplicate check is performed for the same user within the same vertical and department'
                    },
                    'options': {
                        'continue_upload': {
                            'description': 'Upload anyway and create new versions of these files',
                            'action': 'Resend the request with force_upload=true parameter'
                        },
                        'cancel_upload': {
                            'description': 'Cancel the upload process',
                            'action': 'Do not resend the request'
                        }
                    },
                    'instruction': 'To proceed with upload despite duplicates, resend this request with force_upload=true in the form data'
                }), 409  # 409 Conflict status code for duplicate resource
        
        # Continue with normal upload process
        uploaded_files = []
        enhanced_processing_results = []
        blob_service_client = get_blob_service_client()
        container_client = blob_service_client.get_container_client(CONTAINER_NAME)

        for file in files:
            if file.filename == '':
                continue

            # Generate unique file name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())
            secure_name = secure_filename(file.filename)
            blob_name = f"{unique_id}_{timestamp}_{secure_name}"

            # Upload to blob storage
            blob_client = container_client.get_blob_client(blob_name)
            file_contents = file.read()
            blob_client.upload_blob(file_contents, overwrite=True)

            # Create database record
            new_file = File(
                file_name=secure_name,
                file_format_id=file_format_id,
                file_type_id=file_type_id,
                department_id=department_id,
                vertical_id=vertical_id,
                blob_url=blob_client.url,
                uploaded_by=request.user.id,
                sensitivity_id=request.form.get('sensitivity_id', 4),
                comments=comments,
                file_tags=file_tags_json
            )
            db.session.add(new_file)
            db.session.flush()  # Get ID without committing
            
            file_id = new_file.id
            print(f"DEBUG - File record created with ID: {file_id}")
            print(f"DEBUG - Assigned file_tags: {new_file.file_tags}")
            
            # If ORM approach doesn't work, try direct SQL for setting file_tags
            if file_tags_json is not None and file_id is not None:
                try:
                    import json
                    # Get the raw JSON string
                    tags_json_str = json.dumps(file_tags_json) 
                    print(f"DEBUG - Executing direct SQL update with JSON: {tags_json_str}")
                    
                    # Execute raw SQL to update the file_tags
                    sql = f"UPDATE files SET file_tags = '{tags_json_str}'::jsonb WHERE id = {file_id}"
                    db.session.execute(sql)
                    print(f"DEBUG - Direct SQL update executed")
                except Exception as e:
                    print(f"DEBUG - Error in direct SQL update: {str(e)}")

            uploaded_files.append({
                'id': new_file.id,
                'original_name': secure_name,
                'blob_name': blob_name,
                'blob_url': blob_client.url
            })

        db.session.commit()
        print(f"DEBUG - Transaction committed")
        
        # Verify after commit
        if len(uploaded_files) > 0:
            file_id = uploaded_files[0]['id']
            try:
                file = File.query.get(file_id)
                print(f"DEBUG - After commit - file_tags for file {file_id}: {file.file_tags}")
            except Exception as e:
                print(f"DEBUG - Error verifying file_tags after commit: {str(e)}")

        # 🚀 NEW: Trigger Enhanced Document Processing for each uploaded file
        print(f"🔄 Starting enhanced processing for {len(uploaded_files)} uploaded files...")
        
        for uploaded_file in uploaded_files:
            file_id = uploaded_file['id']
            file_name = uploaded_file['original_name']
            
            # Trigger enhanced processing
            processing_result = trigger_enhanced_processing(file_id, file_name)
            
            # Add processing result to the uploaded file info
            uploaded_file['enhanced_processing'] = {
                'enabled': ENHANCED_PROCESSING_ENABLED and ENHANCED_PROCESSING_AVAILABLE,
                'status': processing_result.get('status') if processing_result else 'skipped',
                'message': processing_result.get('message') if processing_result else 'Enhanced processing not available'
            }
            
            enhanced_processing_results.append({
                'file_id': file_id,
                'file_name': file_name,
                'processing_result': processing_result
            })

        print(f"✅ Enhanced processing completed for all files")
        
        # Determine success message based on whether force_upload was used
        base_message = f'Successfully uploaded {len(uploaded_files)} files'
        if force_upload:
            base_message += ' (duplicates were overridden as requested)'
                
        return jsonify({
            'message': base_message,
            'files': uploaded_files,
            'force_upload_used': force_upload,
            'enhanced_processing': {
                'enabled': ENHANCED_PROCESSING_ENABLED and ENHANCED_PROCESSING_AVAILABLE,
                'processed_files': len([r for r in enhanced_processing_results if r['processing_result'] and r['processing_result'].get('status') == 'completed']),
                'failed_files': len([r for r in enhanced_processing_results if r['processing_result'] and r['processing_result'].get('status') == 'failed']),
                'skipped_files': len([r for r in enhanced_processing_results if not r['processing_result'] or r['processing_result'].get('status') == 'skipped']),
                'results': enhanced_processing_results
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        print(f"DEBUG - Error during file upload: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_files():
    try:
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        user_id = request.user.id
        print(f"user_id: {user_id}")
        # Get filter parameters from request
        search_term = request.args.get('search', '').strip()
        vertical_id = request.args.get('vertical_id')
        department_id = request.args.get('department_id')
        sensitivity_id = request.args.get('sensitivity_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        tag_id = request.args.get('tag_id')  # We'll use this to search in file_tags JSON
        file_tag = request.args.get('file_tag')
        tags_param = request.args.get('tags')
        my_files = request.args.get('my_files', 'false').lower() == 'true'  # New parameter for filtering by user ID
        
        # Start with base query
        query = File.query

        # Filter by the authenticated user if my_files is true
        if my_files:
            query = query.filter(File.uploaded_by == user_id)

        # Apply search filter if provided
        if search_term:
            query = query.filter(File.file_name.ilike(f'%{search_term}%'))

        # Filter by vertical if provided
        if vertical_id and vertical_id != 'all':
            query = query.filter(File.vertical_id == vertical_id)

        # Filter by department if provided
        if department_id and department_id != 'all':
            query = query.filter(File.department_id == department_id)
            
        # Filter by sensitivity if provided
        if sensitivity_id and sensitivity_id != 'all':
            query = query.filter(File.sensitivity_id == sensitivity_id)
            
        # Filter by tag_id if provided (search in file_tags JSON)
        if tag_id and tag_id != 'all':
            # Search for the tag_id as a key in the file_tags JSON object
            query = query.filter(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
            
        # Filter by file_tag if provided (search in file_tags JSON values)
        if file_tag:
            # Search for the file_tag as a value in the file_tags JSON object
            query = query.filter(File.file_tags.cast(db.String).like(f'%"{file_tag}"%'))
            
        # Filter by tags if provided (from tags parameter - JSON object like {"1":"age","2":"gender"})
        if tags_param:
            try:
                import json
                from sqlalchemy import or_
                
                tags_dict = json.loads(tags_param)
                if tags_dict and isinstance(tags_dict, dict):
                    print(f"DEBUG - Filtering by tags: {tags_dict}")
                    
                    # Create a list of OR conditions for each tag
                    tag_filters = []
                    for tag_id, tag_value in tags_dict.items():
                        tag_filters.append(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
                    
                    # Apply the OR filter if we have any tag filters
                    if tag_filters:
                        query = query.filter(or_(*tag_filters))
                        
            except json.JSONDecodeError as e:
                print(f"DEBUG - Error parsing tags JSON: {str(e)}")
            
        # Apply date range filter if provided
        if start_date:
            try:
                # Handle ISO format date string
                start_datetime = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                query = query.filter(File.upload_time >= start_datetime)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format. Use ISO format'}), 400

        if end_date:
            try:
                # Handle ISO format date string
                end_datetime = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                query = query.filter(File.upload_time <= end_datetime)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format. Use ISO format'}), 400
        
        # Execute paginated query
        files = query.order_by(File.upload_time.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )
        
        return jsonify({
            'items': [{
                'id': file.id,
                'file_name': file.file_name,
                'format': file.format.name if file.format else None,
                'type': file.type.name if file.type else None,
                'department': file.department.name if file.department else None,
                'vertical': file.vertical.name if file.vertical else None,
                'sensitivity': file.sensitivity.name if file.sensitivity else None,
                'sensitivity_id': file.sensitivity_id,
                'blob_url': file.blob_url,
                'upload_time': file.upload_time.isoformat(),
                'updated_time': file.updated_time.isoformat(),
                'comments': file.comments,
                'file_tags': file.file_tags
            } for file in files.items],
            'pagination': {
                'total_items': files.total,
                'total_pages': files.pages,
                'current_page': files.page,
                'per_page': per_page,
                'has_next': files.has_next,
                'has_prev': files.has_prev
            },
            'filters_applied': {
                'search': search_term,
                'vertical_id': vertical_id,
                'department_id': department_id,
                'sensitivity_id': sensitivity_id,
                'start_date': start_date,
                'end_date': end_date,
                'tag_id': tag_id,
                'file_tag': file_tag,
                'tags': tags_param,
                'my_files': my_files
            }
        }), 200
    except Exception as e:
        print(f"Error in get_files: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/permissions', methods=['GET'])
@login_required
def get_file_permissions():
    try:
        user = User.query.get(request.user.id)
        permissions = {
            'can_view': SCOPE_VIEW_FILES in (user.scopes or []),
            'can_upload': SCOPE_UPLOAD_FILE in (user.scopes or [])
        }
        return jsonify(permissions), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/search', methods=['POST'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def search_files():
    try:
        data = request.json
        if not data or 'question' not in data:
            return jsonify({'error': 'No question provided'}), 400

        # Rephrase question if there's conversation history
        conversation_history = data.get('conversation_history')
        question_data = rephrase_question(
            data['question'],
            conversation_history
        )

        # First, get filtered files from database based on metadata
        # remove the uploaded_by condition
        query = File.query
        
        # Check if we should use specific files or apply filters
        is_filter_all = data.get('is_filter_all', True)  # Default to True for backward compatibility
        selected_file_ids = data.get('selected_file_ids', [])

        if not is_filter_all:
            # Only filter by specific file IDs when is_filter_all is False
            if not selected_file_ids:
                return jsonify({
                    'error': 'No files selected for search. Please select specific files or use filters.'
                }), 400
            query = query.filter(File.id.in_(selected_file_ids))
        else:
            # Apply metadata filters only when is_filter_all is True
            # Ignore selected_file_ids in this case
            if 'department_id' in data:
                query = query.filter(File.department_id == data['department_id'])
            if 'vertical_id' in data:
                query = query.filter(File.vertical_id == data['vertical_id'])
            if 'sensitivity_id' in data:
                query = query.filter(File.sensitivity_id == data['sensitivity_id'])
            if 'start_date' in data:
                query = query.filter(File.upload_time >= datetime.fromisoformat(data['start_date']))
            if 'end_date' in data:
                query = query.filter(File.upload_time <= datetime.fromisoformat(data['end_date']))
                
            # Filter by tags if provided (as dictionary like {"1":"age","2":"gender"})
            if 'tags' in data and data['tags']:
                from sqlalchemy import or_
                
                tags_dict = data['tags']
                if isinstance(tags_dict, dict):
                    print(f"DEBUG - Search files filtering by tags: {tags_dict}")
                    
                    # Create a list of OR conditions for each tag
                    tag_filters = []
                    for tag_id in tags_dict.keys():
                        tag_filters.append(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
                    
                    # Apply the OR filter if we have any tag filters
                    if tag_filters:
                        query = query.filter(or_(*tag_filters))

        filtered_files = query.all()
        if not filtered_files:
            return jsonify({
                'message': 'No files found matching the criteria',
                'results': []
            }), 200

        # Get blob names for filtering in Azure Search
        blob_names = [f.blob_url.split('/')[-1] for f in filtered_files]
        
        # Get embeddings for the rephrased question
        response = openai_client.embeddings.create(
            input=question_data['rephrased_question'],
            model=AZURE_OPENAI_EMBEDDING_DEPLOYMENT
        )
        vector = response.data[0].embedding

        # Perform vector search
        search_client = get_search_client()
        
        # Create vector query
        vector_query = {
            "vector": vector,
            "fields": "text_vector",
            "k": 10,
            "kind": "vector"
        }

        # Create filter string for blob names
        blob_filter = " or ".join([f"metadata_storage_name eq '{name}'" for name in blob_names])
        
        # Search with blob name filter
        results = search_client.search(
            search_text=None,
            vector_queries=[vector_query],
            filter=blob_filter,
            select=["chunk", "chunk_id", "metadata_storage_name", "title"],
            top=10
        )

        # Format results
        search_results = []
        for result in results:
            blob_name = result['metadata_storage_name']
            file = next((f for f in filtered_files if f.blob_url.endswith(blob_name)), None)
            
            if file:
                search_results.append({
                    'chunk': result['chunk'],
                    'chunk_id': result['chunk_id'],
                    'score': result['@search.score'],
                    'file_info': {
                        'id': file.id,
                        'file_name': file.file_name,
                        'format': file.format.name if file.format else None,
                        'type': file.type.name if file.type else None,
                        'department': file.department.name if file.department else None,
                        'vertical': file.vertical.name if file.vertical else None,
                        'sensitivity': file.sensitivity.name if file.sensitivity else None,
                        'sensitivity_id': file.sensitivity_id,
                        'blob_url': file.blob_url
                    }
                })

        ai_response = get_ai_response(
            data['question'],  # Original question
            search_results,
            conversation_history,
            rephrased_question=question_data['rephrased_question']
        )

        # Include question rephrasing metadata in response
        ai_response['metadata'] = {
            **ai_response.get('metadata', {}),
            'question_rephrasing': {
                'original_question': question_data['original_question'],
                'rephrased_question': question_data['rephrased_question'],
                'token_usage': question_data['token_usage']
            }
        }

        return jsonify(ai_response), 200

    except Exception as e:
        print(f"Search error: {str(e)}")
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/batch', methods=['POST'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_files_by_ids():
    try:
        data = request.json
        if not data or 'file_ids' not in data:
            return jsonify({'error': 'No file IDs provided'}), 400

        file_ids = data['file_ids']
        if not isinstance(file_ids, list):
            return jsonify({'error': 'file_ids must be a list'}), 400

        # Query files that belong to the user and are in the provided list
        files = File.query.filter(
            File.id.in_(file_ids),
            File.uploaded_by == request.user.id
        ).all()

        # Create a map of found files
        found_file_ids = {file.id for file in files}
        
        # Check for any file IDs that weren't found
        missing_file_ids = [id for id in file_ids if id not in found_file_ids]

        response = {
            'files': [{
                'id': file.id,
                'file_name': file.file_name,
                'format': file.format.name if file.format else None,
                'type': file.type.name if file.type else None,
                'department': file.department.name if file.department else None,
                'vertical': file.vertical.name if file.vertical else None,
                'sensitivity': file.sensitivity.name if file.sensitivity else None,
                'sensitivity_id': file.sensitivity_id,
                'blob_url': file.blob_url,
                'upload_time': file.upload_time.isoformat(),
                'updated_time': file.updated_time.isoformat(),
                'comments': file.comments,
                'file_tags': file.file_tags
            } for file in files],
            'total_requested': len(file_ids),
            'total_found': len(files)
        }

        # Only include missing_file_ids in response if there are any
        if missing_file_ids:
            response['missing_file_ids'] = missing_file_ids

        return jsonify(response), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/autocomplete', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def autocomplete_files():
    try:
        # Get search term from query parameters
        search_term = request.args.get('q', '').strip()
        if not search_term:
            return jsonify({'error': 'No search term provided'}), 400

        # Get limit from query parameters (default to 10)
        limit = request.args.get('limit', 10, type=int)
        
        # Query files that belong to the user and match the search term
        files = File.query.filter(
            # File.uploaded_by == request.user.id,
            File.file_name.ilike(f'%{search_term}%')  # Case-insensitive partial match
        ).order_by(
            File.file_name  # Order alphabetically
        ).limit(limit).all()

        response = {
            'files': [{
                'id': file.id,
                'file_name': file.file_name,
                'format': file.format.name if file.format else None,
                'type': file.type.name if file.type else None,
                'department': file.department.name if file.department else None,
                'vertical': file.vertical.name if file.vertical else None,
                'sensitivity': file.sensitivity.name if file.sensitivity else None,
                'sensitivity_id': file.sensitivity_id,
                'blob_url': file.blob_url,
                'upload_time': file.upload_time.isoformat(),
                'updated_time': file.updated_time.isoformat(),
                'comments': file.comments,
                'file_tags': file.file_tags
            } for file in files],
            'total_requested': limit,
            'total_found': len(files)
        }

        return jsonify(response), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/filter', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def filter_files():
    try:
        # Get search keyword (now optional)
        search_term = request.args.get('search', '').strip()
        
        # Get filter parameters from query string
        vertical_id = request.args.get('vertical_id')
        department_id = request.args.get('department_id')
        sensitivity_id = request.args.get('sensitivity_id')
        from_date = request.args.get('from_date')
        to_date = request.args.get('to_date')
        tag_id = request.args.get('tag_id')  # We'll use this to search in file_tags JSON
        file_tag = request.args.get('file_tag')
        tags_param = request.args.get('tags')
        
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)

        # Start with base query and include uploader information
        query = File.query.options(joinedload(File.uploader))

        # Apply search term filter only if provided
        if search_term:
            query = query.filter(File.file_name.ilike(f'%{search_term}%'))

        # Apply filters if they are provided
        if vertical_id and vertical_id != 'all':
            query = query.filter(File.vertical_id == vertical_id)
            
        if department_id and department_id != 'all':
            query = query.filter(File.department_id == department_id)
            
        if sensitivity_id and sensitivity_id != 'all':
            query = query.filter(File.sensitivity_id == sensitivity_id)
            
        # Filter by tag_id if provided (search in file_tags JSON)
        if tag_id and tag_id != 'all':
            # Search for the tag_id as a key in the file_tags JSON object
            query = query.filter(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
            
        # Filter by file_tag if provided (search in file_tags JSON values)
        if file_tag:
            # Search for the file_tag as a value in the file_tags JSON object
            query = query.filter(File.file_tags.cast(db.String).like(f'%"{file_tag}"%'))
            
        # Filter by tags if provided (from tags parameter - JSON object like {"1":"age","2":"gender"})
        if tags_param:
            try:
                import json
                from sqlalchemy import or_
                
                tags_dict = json.loads(tags_param)
                if tags_dict and isinstance(tags_dict, dict):
                    print(f"DEBUG - Filtering by tags: {tags_dict}")
                    
                    # Create a list of OR conditions for each tag
                    tag_filters = []
                    for tag_id, tag_value in tags_dict.items():
                        tag_filters.append(File.file_tags.cast(db.String).like(f'%"{tag_id}":%'))
                    
                    # Apply the OR filter if we have any tag filters
                    if tag_filters:
                        query = query.filter(or_(*tag_filters))
                        
            except json.JSONDecodeError as e:
                print(f"DEBUG - Error parsing tags JSON: {str(e)}")
            
        # Apply date range filter if provided
        if from_date:
            try:
                from_datetime = datetime.strptime(from_date, '%Y-%m-%d')
                query = query.filter(File.upload_time >= from_datetime)
            except ValueError:
                return jsonify({'error': 'Invalid from_date format. Use YYYY-MM-DD'}), 400

        if to_date:
            try:
                to_datetime = datetime.strptime(to_date, '%Y-%m-%d')
                # Add one day to include the entire end date
                to_datetime = to_datetime.replace(hour=23, minute=59, second=59)
                query = query.filter(File.upload_time <= to_datetime)
            except ValueError:
                return jsonify({'error': 'Invalid to_date format. Use YYYY-MM-DD'}), 400

        # Execute paginated query
        paginated_files = query.order_by(File.upload_time.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Format response
        response = {
            'items': [{
                'id': file.id,
                'file_name': file.file_name,
                'format': file.format.name if file.format else None,
                'type': file.type.name if file.type else None,
                'department': file.department.name if file.department else None,
                'vertical': file.vertical.name if file.vertical else None,
                'sensitivity': file.sensitivity.name if file.sensitivity else None,
                'sensitivity_id': file.sensitivity_id,
                'blob_url': file.blob_url,
                'upload_time': file.upload_time.isoformat(),
                'updated_time': file.updated_time.isoformat(),
                'uploaded_by': file.uploader.user_name if file.uploader else None,
                'comments': file.comments,
                'file_tags': file.file_tags
            } for file in paginated_files.items],
            'pagination': {
                'total_items': paginated_files.total,
                'total_pages': paginated_files.pages,
                'current_page': paginated_files.page,
                'per_page': per_page,
                'has_next': paginated_files.has_next,
                'has_prev': paginated_files.has_prev
            },
            'filters_applied': {
                'search': search_term,
                'vertical_id': vertical_id,
                'department_id': department_id,
                'sensitivity_id': sensitivity_id,
                'from_date': from_date,
                'to_date': to_date,
                'tag_id': tag_id,
                'file_tag': file_tag,
                'tags': tags_param
            }
        }

        return jsonify(response), 200

    except Exception as e:
        print(f"Error in filter_files: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/delete', methods=['POST'])
@login_required
@require_scope(SCOPE_REMOVE_FILE)
def delete_files():
    try:
        data = request.json
        if not data or 'file_ids' not in data:
            return jsonify({'error': 'No file IDs provided'}), 400

        file_ids = data['file_ids']
        if not isinstance(file_ids, list):
            return jsonify({'error': 'file_ids must be a list'}), 400

        # Query files that belong to the user and are in the provided list
        files = File.query.filter(
            File.id.in_(file_ids)
            #File.uploaded_by == request.user.id
        ).all()

        if not files:
            return jsonify({'error': 'No matching files found'}), 404

        # Initialize services
        blob_service_client = get_blob_service_client()
        container_client = blob_service_client.get_container_client(CONTAINER_NAME)
        search_client = get_search_client()

        deleted_files = []
        failed_files = []

        for file in files:
            try:
                cleanup_result = _comprehensive_file_cleanup(
                    file, blob_service_client, container_client, search_client
                )
                
                if cleanup_result['success']:
                    deleted_files.append({
                        'id': file.id,
                        'file_name': file.file_name,
                        'cleanup_details': cleanup_result['details']
                    })
                else:
                    failed_files.append({
                        'id': file.id,
                        'file_name': file.file_name,
                        'error': cleanup_result['error'],
                        'partial_cleanup': cleanup_result.get('partial_cleanup', [])
                    })

            except Exception as e:
                # If deletion fails, add to failed files list
                failed_files.append({
                    'id': file.id,
                    'file_name': file.file_name,
                    'error': str(e)
                })

        # Commit database changes
        db.session.commit()

        response = {
            'message': f'Successfully processed {len(deleted_files)} out of {len(file_ids)} files',
            'deleted_files': deleted_files,
        }

        if failed_files:
            response['failed_files'] = failed_files
            return jsonify(response), 207  # Return 207 Multi-Status if some deletions failed

        return jsonify(response), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@file_bp.route('/files/sensitivity', methods=['GET'])
@login_required
def get_sensitivity_levels():
    try:
        sensitivities = Sensitivity.query.order_by(Sensitivity.id).all()
        return jsonify([{
            'id': s.id,
            'name': s.name
        } for s in sensitivities]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500