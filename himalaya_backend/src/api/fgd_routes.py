from flask import Blueprint, request, jsonify, current_app
from models.models import db, FGD, Participant, Video, Theme, Language, User, Presentation, ThemeDiscussionGuide
from utils.decorators import login_required, require_scope, SCOPE_CREATE_FGD
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from azure.core.exceptions import ResourceNotFoundError
from datetime import datetime, timedelta
from utils.video_processor import process_video_async
import logging
from .ppt_routes import generate_ppt
import json
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    AZURE_STORAGE_CONTAINER_NAME,
    FGD_VIDEOS_CONTAINER
)
import uuid

# Setup logger
logger = logging.getLogger(__name__)

fgd_bp = Blueprint('fgd', __name__)

def get_blob_service_client():
    try:
        return BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    except Exception as e:
        raise Exception(f"Failed to connect to Azure Blob Storage: {str(e)}")

@fgd_bp.route('/themes/<int:theme_id>/languages', methods=['GET'])
@login_required
def get_theme_languages(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)
        languages = theme.languages
        return jsonify([{
            'id': lang.id,
            'name': lang.name,
            'code': lang.code
        } for lang in languages]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/fgds', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def create_fgd(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)

        # Check if user is one of the owners of the theme OR the creator of the theme
        is_theme_owner = request.user.id in [owner.id for owner in theme.owners]
        is_theme_creator = theme.created_by == request.user.id
        
        if not (is_theme_owner or is_theme_creator):
            return jsonify({
                'error': 'Unauthorized to create FGD for this theme',
                'details': f'You must be either an owner or the creator of theme "{theme.name}" to create FGDs'
            }), 403

        data = request.json
        language_id = data.get('language_id')
        group_size = data.get('group_size')
        conductor_name = data.get('conductor_name')
        discussion_date = data.get('discussion_date')
        country = data.get('country')
        participants_data = data.get('participants')

        if not all([language_id, group_size, conductor_name, discussion_date, participants_data]):
            return jsonify({'error': 'Missing required fields'}), 400

        # Create FGD
        fgd = FGD(
            theme_id=theme_id,
            language_id=language_id,
            group_size=group_size,
            conductor_name=conductor_name,
            discussion_date=datetime.strptime(discussion_date, '%Y-%m-%d'),
            country=country,
            created_by=request.user.id  # Set the FGD creator
        )
        db.session.add(fgd)
        db.session.flush()  # Get FGD ID

        # Add participants
        for participant in participants_data:
            participant_obj = Participant(
                fgd_id=fgd.id,
                gender=participant.get('gender'),
                age=participant.get('age'),
                nationality=participant.get('nationality'),
                marital_status=participant.get('marital_status'),
                has_children=participant.get('has_children'),
                age_range=participant.get('age_range')
            )
            db.session.add(participant_obj)

        db.session.commit()

        return jsonify({'message': 'FGD created successfully', 'fgd_id': fgd.id}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/fgds', methods=['GET'])
@login_required
def get_fgds_by_theme(theme_id):
    try:
        fgds = FGD.query.filter_by(theme_id=theme_id).all()
        return jsonify([{
            'id': fgd.id,
            'language_id': fgd.language_id,
            'group_size': fgd.group_size,
            'conductor_name': fgd.conductor_name,
            'discussion_date': fgd.discussion_date.isoformat(),
            'country': fgd.country,
            'status': fgd.status,
            'participants': [{
                'gender': p.gender,
                'age': p.age,
                'nationality': p.nationality,
                'marital_status': p.marital_status,
                'has_children': p.has_children
            } for p in fgd.participants],
            'videos': [{
                'id': video.id,
                'url': video.blob_url,
                'uploaded_at': video.uploaded_at.isoformat(),
                'transcription_url': video.transcription_blob_url,
                'transcription_json_url': video.transcription_json_url,
                'processing_status': video.processing_status,
                'file_name': video.file_name
            } for video in fgd.videos]
        } for fgd in fgds]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/fgds/<int:fgd_id>/videos', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def upload_video(fgd_id):
    try:
        fgd = FGD.query.get_or_404(fgd_id)

        # Check if user is one of the owners of the theme OR the creator of the theme
        is_theme_owner = request.user.id in [owner.id for owner in fgd.theme.owners]
        is_theme_creator = fgd.theme.created_by == request.user.id
        
        if not (is_theme_owner or is_theme_creator):
            return jsonify({
                'error': 'Unauthorized to upload video for this FGD',
                'details': f'You must be either an owner or the creator of theme "{fgd.theme.name}" to upload videos'
            }), 403

        # Get metadata and chunk data
        file_name = request.form.get('file_name')
        total_chunks = int(request.form.get('total_chunks'))
        current_chunk = int(request.form.get('current_chunk'))
        file_chunk = request.files['file_chunk']

        if not file_name or total_chunks is None or current_chunk is None or not file_chunk:
            return jsonify({'error': 'Missing required fields'}), 400

        # Create a unique blob name
        unique_id = str(uuid.uuid4())
        blob_name = f"{unique_id}_{file_name}"

        # Upload chunk to Azure Blob Storage
        blob_service_client = get_blob_service_client()
        container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
        blob_client = container_client.get_blob_client(blob_name)

        # Append the chunk to the blob
        blob_client.upload_blob(file_chunk.read(), blob_type="AppendBlob", overwrite=(current_chunk == 0))

        # If this is the last chunk, finalize the upload
        if current_chunk == total_chunks - 1:
            # Create video entry
            video = Video(
                fgd_id=fgd.id,
                blob_url=blob_client.url
            )
            db.session.add(video)

            # Update FGD status
            fgd.status = 'Video Uploaded'
            db.session.commit()

            return jsonify({'message': 'Video uploaded successfully', 'video_url': video.blob_url}), 201

        return jsonify({'message': 'Chunk uploaded successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/generate-sas-token', methods=['GET'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def generate_sas_token():
    try:
        blob_service_client = get_blob_service_client()
        # container_client = blob_service_client.get_container_client(CONTAINER_NAME)

        # Generate a unique blob name
        file_name = request.args.get('file_name')
        unique_id = str(uuid.uuid4())
        blob_name = f"fgd_videos/{unique_id}_{file_name}"

        # Generate SAS token
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=FGD_VIDEOS_CONTAINER,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(write=True),
            expiry=datetime.utcnow() + timedelta(hours=1)
        )

        return jsonify({
            'sas_token': sas_token,
            'blob_url': f"https://{blob_service_client.account_name}.blob.core.windows.net/{FGD_VIDEOS_CONTAINER}/{blob_name}"
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/fgds/<int:fgd_id>/notify-upload', methods=['POST'])
@login_required
def notify_upload(fgd_id):
    try:
        logger.info(f"Received upload notification for FGD {fgd_id}")
        fgd = FGD.query.get_or_404(fgd_id)

        # Check if user is one of the owners of the theme OR the creator of the theme
        is_theme_owner = request.user.id in [owner.id for owner in fgd.theme.owners]
        is_theme_creator = fgd.theme.created_by == request.user.id
        
        if not (is_theme_owner or is_theme_creator):
            logger.warning(f"Unauthorized upload attempt for FGD {fgd_id} by user {request.user.id}")
            return jsonify({
                'error': 'Unauthorized to notify upload for this FGD',
                'details': f'You must be either an owner or the creator of theme "{fgd.theme.name}" to notify uploads'
            }), 403

        data = request.json
        blob_url = data.get('blob_url')
        original_file_name = data.get('file_name')  # Get original file name from request
        logger.info(f"Blob URL received: {blob_url}, Original filename: {original_file_name}")

        if not blob_url:
            return jsonify({'error': 'Missing blob URL'}), 400

        # Create video entry with original file name
        video = Video(
            fgd_id=fgd.id,
            blob_url=blob_url,
            processing_status='pending',
            file_name=original_file_name  # Store the original file name
        )
        db.session.add(video)

        # Update FGD status to Video Uploaded initially
        fgd.status = 'Video Uploaded'
        db.session.commit()
        logger.info(f"Created video entry with ID {video.id}")

        # Start background processing with app context
        process_video_async(video.id, current_app._get_current_object())
        logger.info(f"Started background processing for video {video.id}")

        return jsonify({
            'message': 'Upload notification received successfully',
            'video_id': video.id,
            'status': 'processing'
        }), 200

    except Exception as e:
        logger.error(f"Error in notify_upload: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Add a new endpoint to check processing status
@fgd_bp.route('/videos/<int:video_id>/status', methods=['GET'])
@login_required
def get_video_status(video_id):
    try:
        video = Video.query.get_or_404(video_id)
        
        return jsonify({
            'video_id': video.id,
            'status': video.processing_status,
            'error_message': video.error_message,
            'transcription': video.transcription_text if video.processing_status == 'completed' else None,
            'audio_url': video.audio_blob_url if video.processing_status == 'completed' else None,
            'transcription_url': video.transcription_blob_url if video.processing_status == 'completed' else None,
            'transcription_json_url': video.transcription_json_url if video.processing_status == 'completed' else None
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentation_eligible', methods=['GET'])
@login_required
def get_presentation_eligible_videos(theme_id):
    try:
        # Query FGDs associated with the theme
        fgds = FGD.query.filter_by(theme_id=theme_id).all()
        
        # Collect videos with completed transcriptions and include FGD information
        eligible_videos = []
        for fgd in fgds:
            for video in fgd.videos:
                if video.processing_status == 'completed':
                    eligible_videos.append({
                        'video_id': video.id,
                        'blob_url': video.blob_url,
                        'uploaded_at': video.uploaded_at.isoformat(),
                        'processed_at': video.processed_at.isoformat() if video.processed_at else None,
                        'original_file_name': video.file_name,  # Assuming file_name is stored in the Video model
                        'fgd': {
                            'id': fgd.id,
                            'language_id': fgd.language_id,
                            'group_size': fgd.group_size,
                            'conductor_name': fgd.conductor_name,
                            'discussion_date': fgd.discussion_date.isoformat(),
                            'country': fgd.country,
                            'status': fgd.status
                        }
                    })
        
        return jsonify(eligible_videos), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentations', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def create_presentation(theme_id):
    try:
        logger.info(f"Starting presentation creation for theme {theme_id}")
        
        # Get theme and validate it exists
        theme = Theme.query.get_or_404(theme_id)
        
        # Get video IDs from request
        data = request.json
        video_ids = data.get('video_ids', [])
        if not video_ids:
            return jsonify({'error': 'No video IDs provided'}), 400

        logger.info(f"Processing videos: {video_ids}")

        # Get videos and validate they exist and belong to the theme's FGDs
        videos = Video.query.filter(Video.id.in_(video_ids)).all()
        if len(videos) != len(video_ids):
            return jsonify({'error': 'One or more videos not found'}), 404

        # Validate all videos belong to the theme's FGDs
        theme_fgd_ids = [fgd.id for fgd in theme.fgds]
        for video in videos:
            if video.fgd_id not in theme_fgd_ids:
                return jsonify({'error': f'Video {video.id} does not belong to theme {theme_id}'}), 400

        # Get discussion guide for the theme
        base_guide = ThemeDiscussionGuide.query.filter_by(
            theme_id=theme_id,
            is_base_language=True
        ).first()
        
        if not base_guide:
            return jsonify({'error': 'No base discussion guide found for theme'}), 400

        # Get blob service client for generating SAS tokens
        blob_service_client = get_blob_service_client()

        # Generate SAS token for discussion guide
        guide_url = base_guide.guide_url
        guide_container = 'generalaisearch'
        guide_blob_name = guide_url.split(f'{guide_container}/')[-1]
        
        guide_sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=guide_container,
            blob_name=guide_blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(hours=1)
        )
        guide_url_with_sas = f"{guide_url}?{guide_sas_token}"

        # Prepare data for PPT generation
        discussion_transcripts = []
        languages = set()
        
        for video in videos:
            if not video.transcription_blob_url:
                return jsonify({'error': f'Video {video.id} has no transcription URL'}), 400
                
            fgd = video.fgd
            languages.add(fgd.language.code)
            
            # Generate SAS token for transcription
            trans_url = video.transcription_blob_url
            trans_container = 'fgd-videos'
            trans_blob_name = trans_url.split(f'{trans_container}/')[-1]
            
            trans_sas_token = generate_blob_sas(
                account_name=blob_service_client.account_name,
                container_name=trans_container,
                blob_name=trans_blob_name,
                account_key=blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(hours=1)
            )
            trans_url_with_sas = f"{trans_url}?{trans_sas_token}"
            
            # Just append the URL string instead of a dictionary
            discussion_transcripts.append(trans_url_with_sas)

        logger.info(f"Calling PPT generation with {len(discussion_transcripts)} transcripts")

        # Call PPT generation with the URLs
        from utils.ppt_utils import process_presentation_request
        success, message, result = process_presentation_request(
            discussion_guide_url=guide_url_with_sas,
            transcript_urls=discussion_transcripts,  # Now it's a list of URL strings
            theme_name=theme.name,
            languages=list(languages)
        )

        if not success:
            logger.error(f"PPT generation failed: {message}")
            return jsonify({'error': 'Failed to generate presentation', 'details': message}), 500

        # Store presentation in database
        presentation = Presentation(
            theme_id=theme_id,
            ppt_url=result['sharepoint']['file_url']
        )
        db.session.add(presentation)
        db.session.commit()

        logger.info(f"Successfully created presentation with ID {presentation.id}")

        return jsonify({
            'message': 'Presentation created successfully',
            'presentation_id': presentation.id,
            'presentation_url': presentation.ppt_url
        }), 201

    except Exception as e:
        logger.error(f"Error in create_presentation: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentations', methods=['GET'])
@login_required
def get_theme_presentations(theme_id):
    try:
        logger.info(f"Fetching presentations for theme {theme_id}")
        
        # Get theme and validate it exists
        theme = Theme.query.get_or_404(theme_id)
        
        # Query presentations for the theme
        presentations = Presentation.query.filter_by(theme_id=theme_id).order_by(Presentation.created_at.desc()).all()
        
        # Format the response
        presentation_list = [{
            'presentation_id': presentation.id,
            'presentation_url': presentation.ppt_url,
            'presentation_name': presentation.ppt_url.split('/')[-1].split('?')[0],  # Extract filename from URL
            'created_at': presentation.created_at.isoformat(),
            'theme': {
                'id': theme.id,
                'name': theme.name
            }
        } for presentation in presentations]
        
        return jsonify({
            'theme_id': theme_id,
            'presentations': presentation_list
        }), 200

    except Exception as e:
        logger.error(f"Error in get_theme_presentations: {str(e)}")
        return jsonify({'error': str(e)}), 500

def _comprehensive_fgd_cleanup(fgd, blob_service_client):
    """
    Comprehensive cleanup of FGD-related data across all systems.
    
    Args:
        fgd: FGD database record
        blob_service_client: Azure Blob Service client
        
    Returns:
        Dict with cleanup results
    """
    cleanup_details = []
    partial_cleanup = []
    
    try:
        # Get all videos for this FGD
        videos = Video.query.filter_by(fgd_id=fgd.id).all()
        cleanup_details.append(f"Found {len(videos)} videos in FGD {fgd.id}")
        
        # Delete video blobs from storage
        for video in videos:
            try:
                # Delete main video blob
                if video.blob_url:
                    video_blob_name = video.blob_url.split('/')[-1]
                    container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                    blob_client = container_client.get_blob_client(video_blob_name)
                    blob_client.delete_blob()
                    cleanup_details.append(f"Deleted video blob: {video_blob_name}")
                
                # Delete audio blob if exists
                if video.audio_blob_url:
                    audio_blob_name = video.audio_blob_url.split('/')[-1]
                    container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                    blob_client = container_client.get_blob_client(audio_blob_name)
                    blob_client.delete_blob()
                    cleanup_details.append(f"Deleted audio blob: {audio_blob_name}")
                
                # Delete transcription blob if exists
                if video.transcription_blob_url:
                    trans_blob_name = video.transcription_blob_url.split('/')[-1]
                    container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                    blob_client = container_client.get_blob_client(trans_blob_name)
                    blob_client.delete_blob()
                    cleanup_details.append(f"Deleted transcription blob: {trans_blob_name}")
                
                # Delete transcription JSON blob if exists
                if video.transcription_json_url:
                    json_blob_name = video.transcription_json_url.split('/')[-1]
                    container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                    blob_client = container_client.get_blob_client(json_blob_name)
                    blob_client.delete_blob()
                    cleanup_details.append(f"Deleted transcription JSON blob: {json_blob_name}")
                    
            except ResourceNotFoundError:
                cleanup_details.append(f"Video blob not found (already deleted): {video.id}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete video blob for video {video.id}: {str(e)}")
        
        # Delete videos from database
        for video in videos:
            db.session.delete(video)
        cleanup_details.append(f"Deleted {len(videos)} video records from FGD {fgd.id}")
        
        # Delete participants
        participants = Participant.query.filter_by(fgd_id=fgd.id).all()
        for participant in participants:
            db.session.delete(participant)
        cleanup_details.append(f"Deleted {len(participants)} participants from FGD {fgd.id}")
        
        # Delete the FGD
        db.session.delete(fgd)
        cleanup_details.append(f"Deleted FGD {fgd.id}")
        
        return {
            'success': True,
            'details': cleanup_details,
            'partial_cleanup': partial_cleanup
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'partial_cleanup': partial_cleanup,
            'details': cleanup_details
        }

@fgd_bp.route('/fgds/<int:fgd_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def delete_fgd(fgd_id):
    try:
        fgd = FGD.query.get_or_404(fgd_id)

        # Check if user has permission to delete this FGD
        is_theme_owner = request.user.id in [owner.id for owner in fgd.theme.owners]
        is_theme_creator = fgd.theme.created_by == request.user.id
        is_fgd_creator = fgd.created_by == request.user.id
        
        if not (is_theme_owner or is_theme_creator or is_fgd_creator):
            # Debug logging to help troubleshoot authorization issues
            logger.info(f"Authorization failed for user {request.user.id} ('{request.user.user_name}') to delete FGD {fgd_id}")
            logger.info(f"Theme ID: {fgd.theme.id}, Theme name: '{fgd.theme.name}'")
            logger.info(f"Theme creator ID: {fgd.theme.created_by}")
            logger.info(f"Theme owner IDs: {[owner.id for owner in fgd.theme.owners]}")
            logger.info(f"FGD creator ID: {fgd.created_by}")
            
            return jsonify({
                'error': 'Unauthorized to delete this FGD',
                'details': f'You must be either a theme owner, theme creator, or FGD creator to delete this FGD'
            }), 403

        # Get blob service client
        blob_service_client = get_blob_service_client()
        
        # Perform comprehensive cleanup
        cleanup_result = _comprehensive_fgd_cleanup(fgd, blob_service_client)
        
        if cleanup_result['success']:
            # Commit database changes
            db.session.commit()
            
            response = {
                'message': f'FGD {fgd_id} and all associated data deleted successfully',
                'cleanup_summary': {
                    'total_operations': len(cleanup_result['details']),
                    'successful_operations': cleanup_result['details'],
                }
            }
            
            # Include partial cleanup warnings if any
            if cleanup_result['partial_cleanup']:
                response['warnings'] = {
                    'partial_cleanup_issues': cleanup_result['partial_cleanup'],
                    'message': 'Some files could not be deleted from blob storage, but database cleanup was successful'
                }
                return jsonify(response), 207  # 207 Multi-Status
            
            return jsonify(response), 200
        else:
            # Cleanup failed, rollback database changes
            db.session.rollback()
            return jsonify({
                'error': f'Failed to delete FGD {fgd_id} completely',
                'details': cleanup_result['error'],
                'partial_cleanup': cleanup_result.get('partial_cleanup', []),
                'completed_operations': cleanup_result.get('details', [])
            }), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in delete_fgd: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/fgds/<int:fgd_id>/debug-auth', methods=['GET'])
@login_required
def debug_fgd_authorization(fgd_id):
    """
    Debug endpoint to check FGD authorization details.
    This endpoint helps troubleshoot authorization issues.
    """
    try:
        fgd = FGD.query.get_or_404(fgd_id)
        
        # Get current user info
        current_user = request.user
        
        # Get theme info
        theme = fgd.theme
        
        # Check authorization conditions
        is_theme_owner = current_user.id in [owner.id for owner in theme.owners]
        is_theme_creator = theme.created_by == current_user.id
        is_fgd_creator = fgd.created_by == current_user.id
        
        debug_info = {
            'fgd_info': {
                'id': fgd.id,
                'theme_id': fgd.theme_id,
                'status': fgd.status,
                'conductor_name': fgd.conductor_name,
                'discussion_date': fgd.discussion_date.isoformat(),
                'created_by': fgd.created_by,
                'creator_name': fgd.creator.user_name if fgd.creator else None
            },
            'theme_info': {
                'id': theme.id,
                'name': theme.name,
                'created_by': theme.created_by,
                'creator_name': theme.creator.user_name if theme.creator else None,
                'owners': [{
                    'id': owner.id,
                    'user_name': owner.user_name,
                    'email': owner.email
                } for owner in theme.owners]
            },
            'current_user': {
                'id': current_user.id,
                'user_name': current_user.user_name,
                'email': current_user.email,
                'scopes': current_user.scopes
            },
            'authorization_check': {
                'is_theme_owner': is_theme_owner,
                'is_theme_creator': is_theme_creator,
                'is_fgd_creator': is_fgd_creator,
                'can_delete_fgd': is_theme_owner or is_theme_creator or is_fgd_creator,
                'has_create_fgd_scope': SCOPE_CREATE_FGD in (current_user.scopes or [])
            }
        }
        
        return jsonify(debug_info), 200
        
    except Exception as e:
        logger.error(f"Error in debug_fgd_authorization: {str(e)}")
        return jsonify({'error': str(e)}), 500

def _comprehensive_video_cleanup(video, blob_service_client):
    """
    Comprehensive cleanup of video-related data from blob storage.
    
    Args:
        video: Video database record
        blob_service_client: Azure Blob Service client
        
    Returns:
        Dict with cleanup results
    """
    cleanup_details = []
    partial_cleanup = []
    
    try:
        # Delete main video blob
        if video.blob_url:
            try:
                video_blob_name = video.blob_url.split('/')[-1]
                container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                blob_client = container_client.get_blob_client(video_blob_name)
                blob_client.delete_blob()
                cleanup_details.append(f"Deleted video blob: {video_blob_name}")
            except ResourceNotFoundError:
                cleanup_details.append(f"Video blob not found (already deleted): {video_blob_name}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete video blob: {str(e)}")
        
        # Delete audio blob if exists
        if video.audio_blob_url:
            try:
                audio_blob_name = video.audio_blob_url.split('/')[-1]
                container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                blob_client = container_client.get_blob_client(audio_blob_name)
                blob_client.delete_blob()
                cleanup_details.append(f"Deleted audio blob: {audio_blob_name}")
            except ResourceNotFoundError:
                cleanup_details.append(f"Audio blob not found (already deleted): {audio_blob_name}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete audio blob: {str(e)}")
        
        # Delete transcription blob if exists
        if video.transcription_blob_url:
            try:
                trans_blob_name = video.transcription_blob_url.split('/')[-1]
                container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                blob_client = container_client.get_blob_client(trans_blob_name)
                blob_client.delete_blob()
                cleanup_details.append(f"Deleted transcription blob: {trans_blob_name}")
            except ResourceNotFoundError:
                cleanup_details.append(f"Transcription blob not found (already deleted): {trans_blob_name}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete transcription blob: {str(e)}")
        
        # Delete transcription JSON blob if exists
        if video.transcription_json_url:
            try:
                json_blob_name = video.transcription_json_url.split('/')[-1]
                container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                blob_client = container_client.get_blob_client(json_blob_name)
                blob_client.delete_blob()
                cleanup_details.append(f"Deleted transcription JSON blob: {json_blob_name}")
            except ResourceNotFoundError:
                cleanup_details.append(f"Transcription JSON blob not found (already deleted): {json_blob_name}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete transcription JSON blob: {str(e)}")
        
        # Delete vector blob if exists
        if video.vector_blob_url:
            try:
                vector_blob_name = video.vector_blob_url.split('/')[-1]
                container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
                blob_client = container_client.get_blob_client(vector_blob_name)
                blob_client.delete_blob()
                cleanup_details.append(f"Deleted vector blob: {vector_blob_name}")
            except ResourceNotFoundError:
                cleanup_details.append(f"Vector blob not found (already deleted): {vector_blob_name}")
            except Exception as e:
                partial_cleanup.append(f"Failed to delete vector blob: {str(e)}")
        
        return {
            'success': True,
            'details': cleanup_details,
            'partial_cleanup': partial_cleanup
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'partial_cleanup': partial_cleanup,
            'details': cleanup_details
        }

@fgd_bp.route('/videos/<int:video_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def delete_video(video_id):
    """
    Delete a specific video from an FGD.
    This includes removing the video file and all associated files from blob storage.
    """
    try:
        video = Video.query.get_or_404(video_id)
        fgd = video.fgd

        # Check if user has permission to delete this video
        is_theme_owner = request.user.id in [owner.id for owner in fgd.theme.owners]
        is_theme_creator = fgd.theme.created_by == request.user.id
        is_fgd_creator = fgd.created_by == request.user.id
        
        if not (is_theme_owner or is_theme_creator or is_fgd_creator):
            # Debug logging to help troubleshoot authorization issues
            logger.info(f"Authorization failed for user {request.user.id} ('{request.user.user_name}') to delete video {video_id}")
            logger.info(f"FGD ID: {fgd.id}, Theme ID: {fgd.theme.id}, Theme name: '{fgd.theme.name}'")
            logger.info(f"Theme creator ID: {fgd.theme.created_by}")
            logger.info(f"Theme owner IDs: {[owner.id for owner in fgd.theme.owners]}")
            logger.info(f"FGD creator ID: {fgd.created_by}")
            
            return jsonify({
                'error': 'Unauthorized to delete this video',
                'details': f'You must be either a theme owner, theme creator, or FGD creator to delete videos from this FGD'
            }), 403

        # Store video info for response
        video_info = {
            'id': video.id,
            'file_name': video.file_name,
            'fgd_id': fgd.id,
            'fgd_conductor': fgd.conductor_name,
            'theme_name': fgd.theme.name
        }

        # Get blob service client
        blob_service_client = get_blob_service_client()
        
        # Perform comprehensive video cleanup
        cleanup_result = _comprehensive_video_cleanup(video, blob_service_client)
        
        if cleanup_result['success']:
            # Delete video from database
            db.session.delete(video)
            
            # Update FGD status if this was the last video
            remaining_videos = Video.query.filter_by(fgd_id=fgd.id).filter(Video.id != video_id).count()
            if remaining_videos == 0:
                fgd.status = 'No Videos'
            
            # Commit database changes
            db.session.commit()
            
            response = {
                'message': f'Video {video_id} deleted successfully',
                'video_info': video_info,
                'cleanup_summary': {
                    'total_operations': len(cleanup_result['details']),
                    'successful_operations': cleanup_result['details'],
                },
                'fgd_status_updated': remaining_videos == 0
            }
            
            # Include partial cleanup warnings if any
            if cleanup_result['partial_cleanup']:
                response['warnings'] = {
                    'partial_cleanup_issues': cleanup_result['partial_cleanup'],
                    'message': 'Some files could not be deleted from blob storage, but database cleanup was successful'
                }
                return jsonify(response), 207  # 207 Multi-Status
            
            return jsonify(response), 200
        else:
            # Cleanup failed, rollback database changes
            db.session.rollback()
            return jsonify({
                'error': f'Failed to delete video {video_id} completely',
                'video_info': video_info,
                'details': cleanup_result['error'],
                'partial_cleanup': cleanup_result.get('partial_cleanup', []),
                'completed_operations': cleanup_result.get('details', [])
            }), 500

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in delete_video: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/videos/<int:video_id>/debug-auth', methods=['GET'])
@login_required
def debug_video_authorization(video_id):
    """
    Debug endpoint to check video deletion authorization details.
    This endpoint helps troubleshoot authorization issues for video operations.
    """
    try:
        video = Video.query.get_or_404(video_id)
        fgd = video.fgd
        theme = fgd.theme
        
        # Get current user info
        current_user = request.user
        
        # Check authorization conditions
        is_theme_owner = current_user.id in [owner.id for owner in theme.owners]
        is_theme_creator = theme.created_by == current_user.id
        is_fgd_creator = fgd.created_by == current_user.id
        
        debug_info = {
            'video_info': {
                'id': video.id,
                'file_name': video.file_name,
                'blob_url': video.blob_url,
                'processing_status': video.processing_status,
                'uploaded_at': video.uploaded_at.isoformat(),
                'processed_at': video.processed_at.isoformat() if video.processed_at else None
            },
            'fgd_info': {
                'id': fgd.id,
                'theme_id': fgd.theme_id,
                'status': fgd.status,
                'conductor_name': fgd.conductor_name,
                'discussion_date': fgd.discussion_date.isoformat(),
                'created_by': fgd.created_by,
                'creator_name': fgd.creator.user_name if fgd.creator else None
            },
            'theme_info': {
                'id': theme.id,
                'name': theme.name,
                'created_by': theme.created_by,
                'creator_name': theme.creator.user_name if theme.creator else None,
                'owners': [{
                    'id': owner.id,
                    'user_name': owner.user_name,
                    'email': owner.email
                } for owner in theme.owners]
            },
            'current_user': {
                'id': current_user.id,
                'user_name': current_user.user_name,
                'email': current_user.email,
                'scopes': current_user.scopes
            },
            'authorization_check': {
                'is_theme_owner': is_theme_owner,
                'is_theme_creator': is_theme_creator,
                'is_fgd_creator': is_fgd_creator,
                'can_delete_video': is_theme_owner or is_theme_creator or is_fgd_creator,
                'has_create_fgd_scope': SCOPE_CREATE_FGD in (current_user.scopes or [])
            }
        }
        
        return jsonify(debug_info), 200
        
    except Exception as e:
        logger.error(f"Error in debug_video_authorization: {str(e)}")
        return jsonify({'error': str(e)}), 500

 