from flask import Blueprint, request, jsonify
from models.models import (
    db, Theme, Language, User, ThemeDiscussionGuide, 
    theme_owners
)
from utils.decorators import login_required, require_scope, SCOPE_CREATE_THEME
from werkzeug.utils import secure_filename
from azure.storage.blob import BlobServiceClient
from datetime import datetime
import uuid
import requests
import logging
from utils.blob_utils import generate_sas_url
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    AZURE_STORAGE_CONTAINER_NAME,
    BACKEND_URL
)

import os

theme_bp = Blueprint('theme', __name__)

# Constants
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_blob_service_client():
    try:
        return BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    except Exception as e:
        raise Exception(f"Failed to connect to Azure Blob Storage: {str(e)}")

@theme_bp.route('/themes', methods=['GET'])
@login_required
@require_scope(SCOPE_CREATE_THEME)
def get_themes():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        query = Theme.query.order_by(Theme.updated_at.desc())
        
        # Filter by owner if specified
        owner_id = request.args.get('owner_id', type=int)
        if owner_id:
            query = query.join(theme_owners).filter(theme_owners.c.user_id == owner_id)
            
        # Filter by active status if specified
        #is_active = request.args.get('is_active', type=bool)
        is_active=True
        if is_active is not None:
            query = query.filter_by(is_active=is_active)

        themes = query.paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return jsonify({
            'items': [{
                'id': theme.id,
                'name': theme.name,
                'objective': theme.objective,
                'owners': [{
                    'id': owner.id,
                    'name': owner.user_name
                } for owner in theme.owners],
                'languages': [{
                    'id': lang.id,
                    'name': lang.name,
                    'code': lang.code
                } for lang in theme.languages],
                'discussion_guides': [{
                    'language': guide.language.code,
                    'url': guide.guide_url,
                    'is_base_language': guide.is_base_language
                } for guide in theme.discussion_guides],
                'interview_guide_language': {
                    'id': theme.interview_guide_language.id,
                    'name': theme.interview_guide_language.name,
                    'code': theme.interview_guide_language.code
                } if theme.interview_guide_language else None,
                'fgds': [{
                    'id': fgd.id,
                    'language_id': fgd.language_id,
                    'group_size': fgd.group_size,
                    'conductor_name': fgd.conductor_name,
                    'discussion_date': fgd.discussion_date.isoformat(),
                    'country': fgd.country,
                    'status': fgd.status,
                    'videos': [{
                        'id': video.id,
                        'url': video.blob_url,
                        'uploaded_at': video.uploaded_at.isoformat()
                    } for video in fgd.videos]
                } for fgd in theme.fgds],
                'is_active': theme.is_active,
                'created_at': theme.created_at.isoformat(),
                'updated_at': theme.updated_at.isoformat()
            } for theme in themes.items],
            'pagination': {
                'total_items': themes.total,
                'total_pages': themes.pages,
                'current_page': themes.page,
                'per_page': per_page,
                'has_next': themes.has_next,
                'has_prev': themes.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@theme_bp.route('/themes', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_THEME)
def create_theme():
    try:
        # Validate file
        if 'interview_guide' not in request.files:
            return jsonify({'error': 'No interview guide file provided'}), 400
            
        file = request.files['interview_guide']
        if file.filename == '' or not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file format'}), 400

        # Get and validate form data
        name = request.form.get('name')
        objective = request.form.get('objective')
        owner_ids = request.form.getlist('owner_ids')
        language_ids = request.form.getlist('language_ids')
        interview_guide_language_id = request.form.get('interview_guide_language_id')

        if not all([name, objective, owner_ids, language_ids, interview_guide_language_id]):
            return jsonify({'error': 'Missing required fields'}), 400

        # Start database transaction
        db.session.begin_nested()

        try:
            # Upload base language file
            blob_service_client = get_blob_service_client()
            container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())
            base_name = secure_filename(os.path.splitext(file.filename)[0])
            file_extension = os.path.splitext(file.filename)[1].lower()
            blob_name = f"interview_guides/{base_name}_{timestamp}_{unique_id}{file_extension}"

            blob_client = container_client.get_blob_client(blob_name)
            file_contents = file.read()
            blob_client.upload_blob(file_contents, overwrite=True)
            base_guide_url = blob_client.url

            # Create theme
            theme = Theme(
                name=name,
                objective=objective,
                interview_guide_language_id=interview_guide_language_id
            )

            # Add owners
            owners = User.query.filter(
                User.id.in_(owner_ids),
                User.scopes.contains([SCOPE_CREATE_THEME])
            ).all()
            if not owners:
                return jsonify({'error': 'No valid theme owners selected'}), 400
            theme.owners = owners

            # Add languages
            languages = Language.query.filter(Language.id.in_(language_ids)).all()
            theme.languages = languages

            db.session.add(theme)
            db.session.flush()  # Get theme ID

            # Create base language discussion guide entry
            base_guide = ThemeDiscussionGuide(
                theme_id=theme.id,
                language_id=interview_guide_language_id,
                guide_url=base_guide_url,
                is_base_language=True
            )
            db.session.add(base_guide)

            # Request translations for other languages
            target_languages = [
                {'code': lang.code, 'name': lang.name} for lang in languages 
                if str(lang.id) != interview_guide_language_id
            ]

    
            if target_languages:
                base_language = Language.query.get(interview_guide_language_id)
                print(f"Base language: {base_language.name}")  # Debug log
                
                translation_request = {
                    'file_url': base_guide_url,
                    'base_language': base_language.name,
                    'target_languages': target_languages
                }
 # Debug log
                
                translation_response = requests.post(
                    f"{BACKEND_URL}/api/translation/translate",
                    json=translation_request,
                    headers={'Authorization': request.headers.get('Authorization')}
                )
                
                if translation_response.status_code == 200:
                    translated_files = translation_response.json().get('translated_files', [])
                    
                    # Create entries for translated guides
                    for trans_file in translated_files:
                        lang_code = trans_file['translated_language']
                        lang = next(l for l in languages if l.code.lower() == lang_code)
                        
                        guide = ThemeDiscussionGuide(
                            theme_id=theme.id,
                            language_id=lang.id,
                            guide_url=trans_file['blob_storage_link'],
                            is_base_language=False
                        )
                        db.session.add(guide)
                else:
                    print(f"Translation failed: {translation_response.text}")  # Debug log

            db.session.commit()

            return jsonify({
                'message': 'Theme created successfully',
                'theme': {
                    'id': theme.id,
                    'name': theme.name,
                    'objective': theme.objective,
                    'discussion_guides': [{
                        'language': guide.language.code,
                        'url': guide.guide_url,
                        'is_base_language': guide.is_base_language
                    } for guide in theme.discussion_guides],
                    'owners': [{
                        'id': owner.id,
                        'name': owner.user_name
                    } for owner in theme.owners],
                    'languages': [{
                        'id': lang.id,
                        'name': lang.name,
                        'code': lang.code
                    } for lang in theme.languages],
                    'created_at': theme.created_at.isoformat()
                }
            }), 201

        except Exception as e:
            db.session.rollback()
            raise e

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@theme_bp.route('/themes/<int:theme_id>', methods=['PUT'])
@login_required
@require_scope(SCOPE_CREATE_THEME)
def update_theme(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)

        # Check if user is one of the owners
        if request.user.id not in [owner.id for owner in theme.owners]:
            return jsonify({'error': 'Unauthorized to modify this theme'}), 403

        # Update file if provided
        if 'interview_guide' in request.files:
            file = request.files['interview_guide']
            if file.filename != '' and allowed_file(file.filename):
                blob_service_client = get_blob_service_client()
                container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = str(uuid.uuid4())
                secure_name = secure_filename(file.filename)
                blob_name = f"interview_guides/{unique_id}_{timestamp}_{secure_name}"

                blob_client = container_client.get_blob_client(blob_name)
                file_contents = file.read()
                blob_client.upload_blob(file_contents, overwrite=True)
                
                # Update the base language discussion guide
                base_guide = ThemeDiscussionGuide.query.filter_by(
                    theme_id=theme.id,
                    is_base_language=True
                ).first()
                
                if base_guide:
                    base_guide.guide_url = blob_client.url
                else:
                    base_guide = ThemeDiscussionGuide(
                        theme_id=theme.id,
                        language_id=theme.interview_guide_language_id,
                        guide_url=blob_client.url,
                        is_base_language=True
                    )
                    db.session.add(base_guide)

        # Update other fields
        if 'name' in request.form:
            theme.name = request.form['name']
        if 'objective' in request.form:
            theme.objective = request.form['objective']
        if 'owner_ids' in request.form:
            owner_ids = request.form.getlist('owner_ids')  # Get all owner_ids as a list
            owners = User.query.filter(
                User.id.in_(owner_ids),
                User.scopes.contains([SCOPE_CREATE_THEME])
            ).all()
            if owners:
                theme.owners = owners  # Replace existing owners
        if 'language_ids' in request.form:
            language_ids = request.form.getlist('language_ids')
            languages = Language.query.filter(Language.id.in_(language_ids)).all()
            theme.languages = languages
        if 'interview_guide_language_id' in request.form:
            theme.interview_guide_language_id = request.form['interview_guide_language_id']
        if 'is_active' in request.form:
            theme.is_active = request.form['is_active'].lower() == 'true'

        db.session.commit()

        return jsonify({
            'message': 'Theme updated successfully',
            'theme': {
                'id': theme.id,
                'name': theme.name,
                'objective': theme.objective,
                'discussion_guides': [{
                    'language': guide.language.code,
                    'url': guide.guide_url,
                    'is_base_language': guide.is_base_language
                } for guide in theme.discussion_guides],
                'interview_guide_language': {
                    'id': theme.interview_guide_language.id,
                    'name': theme.interview_guide_language.name,
                    'code': theme.interview_guide_language.code
                },
                'owners': [{
                    'id': owner.id,
                    'name': owner.user_name
                } for owner in theme.owners],
                'languages': [{
                    'id': lang.id,
                    'name': lang.name,
                    'code': lang.code
                } for lang in theme.languages],
                'updated_at': theme.updated_at.isoformat()
            }
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@theme_bp.route('/themes/<int:theme_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_CREATE_THEME)
def delete_theme(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)

        # Check if user is one of the owners
        if request.user.id not in [owner.id for owner in theme.owners]:
            return jsonify({'error': 'Unauthorized to delete this theme'}), 403

        # Get all discussion guide URLs before deleting the theme
        discussion_guides = ThemeDiscussionGuide.query.filter_by(theme_id=theme_id).all()
        blob_urls = [guide.guide_url for guide in discussion_guides]

        # Delete theme from database (this will cascade delete discussion guides)
        db.session.delete(theme)
        db.session.commit()

        # Delete blobs from storage
        try:
            blob_service_client = get_blob_service_client()
            for url in blob_urls:
                try:
                    # Extract container and blob name from URL
                    blob_path = '/'.join(url.split('/')[3:])  # Remove the storage account part
                    container_name = blob_path.split('/')[0]
                    blob_name = '/'.join(blob_path.split('/')[1:])

                    # Get container client and delete blob
                    container_client = blob_service_client.get_container_client(container_name)
                    blob_client = container_client.get_blob_client(blob_name)
                    blob_client.delete_blob()
                except Exception as e:
                    # Log error but continue with other deletions
                    logging.error(f"Failed to delete blob {url}: {str(e)}")
        except Exception as e:
            # Log error but don't fail the request since database deletion was successful
            logging.error(f"Error deleting blobs: {str(e)}")

        return jsonify({
            'message': 'Theme and associated files deleted successfully'
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@theme_bp.route('/languages', methods=['GET'])
@login_required
def get_languages():
    try:
        languages = Language.query.all()
        return jsonify([{
            'id': lang.id,
            'name': lang.name,
            'code': lang.code
        } for lang in languages]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Add a new endpoint to get available theme owners
@theme_bp.route('/theme-owners', methods=['GET'])
@login_required
def get_theme_owners():
    try:
        theme_owners = User.query.filter(User.scopes.contains([SCOPE_CREATE_THEME])).all()
        return jsonify([{
            'id': user.id,
            'name': user.user_name,
            'email': user.email
        } for user in theme_owners]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@theme_bp.route('/themes/<int:theme_id>', methods=['GET'])
@login_required
@require_scope(SCOPE_CREATE_THEME)
def get_theme(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)
        
        return jsonify({
            'id': theme.id,
            'name': theme.name,
            'objective': theme.objective,
            'owners': [{
                'id': owner.id,
                'name': owner.user_name,
                'email': owner.email
            } for owner in theme.owners],
            'languages': [{
                'id': lang.id,
                'name': lang.name,
                'code': lang.code
            } for lang in theme.languages],
            'discussion_guides': [{
                'id': guide.id,
                'language': guide.language.code,
                'url': generate_sas_url(guide.guide_url),
                'is_base_language': guide.is_base_language
            } for guide in theme.discussion_guides],
            'interview_guide_language': {
                'id': theme.interview_guide_language.id,
                'name': theme.interview_guide_language.name,
                'code': theme.interview_guide_language.code
            } if theme.interview_guide_language else None,
            'fgds': [{
                'id': fgd.id,
                'language_id': fgd.language_id,
                'language': {
                    'id': fgd.language.id,
                    'name': fgd.language.name,
                    'code': fgd.language.code
                },
                'group_size': fgd.group_size,
                'conductor_name': fgd.conductor_name,
                'discussion_date': fgd.discussion_date.isoformat(),
                'country': fgd.country,
                'status': fgd.status,
                'participants': [{
                    'id': participant.id,
                    'gender': participant.gender,
                    'age': participant.age,
                    'nationality': participant.nationality,
                    'marital_status': participant.marital_status,
                    'has_children': participant.has_children
                } for participant in fgd.participants],
                'videos': [{
                    'id': video.id,
                    'url': generate_sas_url(video.blob_url),
                    'uploaded_at': video.uploaded_at.isoformat()
                } for video in fgd.videos],
                'created_at': fgd.created_at.isoformat(),
                'updated_at': fgd.updated_at.isoformat()
            } for fgd in theme.fgds],
            'is_active': theme.is_active,
            'created_at': theme.created_at.isoformat(),
            'updated_at': theme.updated_at.isoformat()
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500