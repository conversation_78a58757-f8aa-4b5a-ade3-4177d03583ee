from flask import Flask, jsonify, render_template, session, redirect, url_for
from flask_cors import CORS
from config.settings import FLASK_CONFIG
from config.logging_config import setup_logging, performance, agentic
from models.models import db
from auth.routes import auth_bp
from api.user_routes import user_bp
from api.master_routes import master_bp
from api.file_routes import file_bp
from api.document_routes_v2 import document_v2_bp  # Enhanced document processing API
from api.translation_routes import translation_bp
from api.ppt_routes import ppt_bp
from api.chat_routes import chat_bp
from api.theme_routes import theme_bp
from api.fgd_routes import fgd_bp
from api.fgd_chat_routes import fgd_chat_bp
from api.video_routes import video_bp
from api.media_routes import media_bp
from api.tag_routes import tag_bp
from utils.decorators import login_required

def create_app():
    app = Flask(__name__, template_folder='templates')
    app.url_map.strict_slashes = False
    
    # Configure CORS for the entire app
    CORS(app, 
         supports_credentials=True,
         resources={
             r"/*": {
                #  "origins": ["http://localhost:3000","https://aivccontainerfrontend.mangodesert-321e63c0.southindia.azurecontainerapps.io"],  # Replace with your frontend URL
                 "origins": [
                     "http://localhost:3000",
                     "https://higpt.himalayawellness.com",
                     "https://aivccontainerfrontendp2.salmonbay-6d5d2866.southindia.azurecontainerapps.io"
                 ],  # Added UAT frontend URL
                 "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
                 "allow_headers": ["Content-Type", "Authorization"],
                 "expose_headers": ["Content-Range", "X-Content-Range"],
                 "supports_credentials": True
             }
         })
    
    app.config.update(FLASK_CONFIG)
    
    # Initialize extensions
    db.init_app(app)
    
    # Register blueprints
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(user_bp, url_prefix='/api')
    app.register_blueprint(master_bp, url_prefix='/api')
    app.register_blueprint(file_bp, url_prefix='/api')
    app.register_blueprint(document_v2_bp, url_prefix='/api')  # Enhanced document processing API
    app.register_blueprint(translation_bp, url_prefix='/api/translation')
    app.register_blueprint(ppt_bp, url_prefix='/api/ppt')
    app.register_blueprint(chat_bp, url_prefix='/api')
    app.register_blueprint(theme_bp, url_prefix='/api')
    app.register_blueprint(fgd_bp, url_prefix='/api')
    app.register_blueprint(fgd_chat_bp, url_prefix='/api')
    app.register_blueprint(video_bp, url_prefix='/api')
    app.register_blueprint(media_bp, url_prefix='/api/media')
    app.register_blueprint(tag_bp, url_prefix='/api')
    
    # Health check endpoint
    @app.route('/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': 'Service is running'
        }), 200
    
    # Enhanced processing health check
    @app.route('/health/enhanced-processing')
    def enhanced_processing_health():
        try:
            from config.settings import ENHANCED_PROCESSING_ENABLED, AZURE_SEARCH_ENHANCED_INDEX_NAME
            return jsonify({
                'status': 'healthy',
                'enhanced_processing_enabled': ENHANCED_PROCESSING_ENABLED,
                'azure_search_index': AZURE_SEARCH_ENHANCED_INDEX_NAME,
                'message': 'Enhanced processing service is available'
            }), 200
        except Exception as e:
            return jsonify({
                'status': 'error',
                'message': f'Enhanced processing service error: {str(e)}'
            }), 500
    
    # Single main route that handles both authenticated and non-authenticated users
    @app.route('/')
    def home():
        if session.get('user'):
            user = session.get('user', {})
            
            return render_template('main.html', 
                                user=user,
                                is_admin=user.get('is_admin', False))
        return render_template('landing.html')
    
    return app

if __name__ == '__main__':
    app = create_app()
    # app.run(debug=False, port=5001, host='0.0.0.0', ssl_context=('cert.pem', 'key.pem'))
    app.run(debug=False, port=5001, host='0.0.0.0')