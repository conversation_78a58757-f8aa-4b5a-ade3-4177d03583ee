"""
Test file for Database Visualization Feature

This file contains tests and examples for the database visualization functionality.
"""

import unittest
import json
import pandas as pd
from unittest.mock import Mock, patch
from services.database_visualization_service import DatabaseVisualizationService

class TestDatabaseVisualization(unittest.TestCase):
    """Test cases for database visualization functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.visualization_service = DatabaseVisualizationService()
        
        # Sample query results for testing
        self.sample_sales_data = [
            {
                'success': True,
                'data': [
                    {'region': 'North', 'sales': 150000},
                    {'region': 'South', 'sales': 120000},
                    {'region': 'East', 'sales': 180000},
                    {'region': 'West', 'sales': 90000}
                ],
                'columns': ['region', 'sales'],
                'row_count': 4
            }
        ]
        
        self.sample_time_series_data = [
            {
                'success': True,
                'data': [
                    {'month': 'Jan', 'sales': 100000},
                    {'month': 'Feb', 'sales': 120000},
                    {'month': 'Mar', 'sales': 110000},
                    {'month': 'Apr', 'sales': 140000},
                    {'month': 'May', 'sales': 130000},
                    {'month': 'Jun', 'sales': 160000}
                ],
                'columns': ['month', 'sales'],
                'row_count': 6
            }
        ]
        
        self.sample_correlation_data = [
            {
                'success': True,
                'data': [
                    {'advertising': 10000, 'sales': 150000},
                    {'advertising': 15000, 'sales': 180000},
                    {'advertising': 20000, 'sales': 220000},
                    {'advertising': 25000, 'sales': 250000},
                    {'advertising': 30000, 'sales': 280000}
                ],
                'columns': ['advertising', 'sales'],
                'row_count': 5
            }
        ]
    
    def test_should_generate_visualization_with_keywords(self):
        """Test that visualization is triggered by keywords"""
        questions_with_keywords = [
            "Show me a chart of sales by region",
            "Create a graph comparing performance",
            "Display the data as a pie chart",
            "Plot the trends over time",
            "Visualize the distribution",
            "Compare sales across regions"
        ]
        
        for question in questions_with_keywords:
            with self.subTest(question=question):
                should_generate = self.visualization_service.should_generate_visualization(
                    question, self.sample_sales_data
                )
                self.assertTrue(should_generate, f"Should generate visualization for: {question}")
    
    def test_should_not_generate_visualization_without_keywords(self):
        """Test that visualization is not triggered without keywords"""
        questions_without_keywords = [
            "What is the total sales?",
            "Get me the data",
            "Show the records",
            "List all items"
        ]
        
        for question in questions_without_keywords:
            with self.subTest(question=question):
                should_generate = self.visualization_service.should_generate_visualization(
                    question, self.sample_sales_data
                )
                self.assertTrue(should_generate, f"Should still generate for structured data: {question}")
    
    def test_extract_bar_chart_data(self):
        """Test bar chart data extraction"""
        question = "Compare sales by region"
        chart_data = self.visualization_service.extract_visualization_data(
            self.sample_sales_data, question
        )
        
        self.assertIsNotNone(chart_data)
        self.assertEqual(chart_data['type'], 'bar')
        self.assertEqual(len(chart_data['labels']), 4)
        self.assertEqual(len(chart_data['datasets']), 1)
        self.assertEqual(chart_data['datasets'][0]['label'], 'sales')
    
    def test_extract_line_chart_data(self):
        """Test line chart data extraction"""
        question = "Show sales trends over time"
        chart_data = self.visualization_service.extract_visualization_data(
            self.sample_time_series_data, question
        )
        
        self.assertIsNotNone(chart_data)
        self.assertEqual(chart_data['type'], 'line')
        self.assertEqual(len(chart_data['labels']), 6)
        self.assertEqual(len(chart_data['datasets']), 1)
    
    def test_extract_pie_chart_data(self):
        """Test pie chart data extraction"""
        question = "Show market share distribution"
        chart_data = self.visualization_service.extract_visualization_data(
            self.sample_sales_data, question
        )
        
        self.assertIsNotNone(chart_data)
        self.assertEqual(chart_data['type'], 'pie')
        self.assertEqual(len(chart_data['labels']), 4)
        self.assertEqual(len(chart_data['datasets']), 1)
    
    def test_extract_scatter_chart_data(self):
        """Test scatter chart data extraction"""
        question = "Show correlation between advertising and sales"
        chart_data = self.visualization_service.extract_visualization_data(
            self.sample_correlation_data, question
        )
        
        self.assertIsNotNone(chart_data)
        self.assertEqual(chart_data['type'], 'scatter')
        self.assertEqual(len(chart_data['datasets']), 2)  # x and y datasets
    
    @patch('services.database_visualization_service.generate_chart')
    def test_generate_visualization_complete_flow(self, mock_generate_chart):
        """Test complete visualization generation flow"""
        # Mock the chart generation to return a base64 image
        mock_generate_chart.return_value = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
        
        question = "Show me sales by region"
        result = self.visualization_service.generate_visualization(
            question, self.sample_sales_data
        )
        
        self.assertIsNotNone(result)
        self.assertIn('visualization_data', result)
        self.assertIn('visualization_type', result)
        self.assertIn('visualization_image', result)
        self.assertIn('visualization_metadata', result)
        
        # Verify the chart was generated
        mock_generate_chart.assert_called_once()
    
    def test_get_visualization_suggestions(self):
        """Test getting visualization suggestions"""
        question = "Compare sales by region"
        suggestions = self.visualization_service.get_visualization_suggestions(
            question, self.sample_sales_data
        )
        
        self.assertIsInstance(suggestions, list)
        # Should suggest pie chart for small categorical data
        self.assertTrue(any('pie' in suggestion for suggestion in suggestions))
    
    def test_handle_empty_data(self):
        """Test handling of empty query results"""
        empty_data = []
        question = "Show me sales data"
        
        should_generate = self.visualization_service.should_generate_visualization(
            question, empty_data
        )
        self.assertFalse(should_generate)
        
        chart_data = self.visualization_service.extract_visualization_data(
            empty_data, question
        )
        self.assertIsNone(chart_data)
    
    def test_handle_large_dataset(self):
        """Test handling of large datasets"""
        # Create a large dataset
        large_data = [
            {
                'success': True,
                'data': [{'region': f'Region_{i}', 'sales': i * 1000} for i in range(150)],
                'columns': ['region', 'sales'],
                'row_count': 150
            }
        ]
        
        question = "Show me all sales data"
        should_generate = self.visualization_service.should_generate_visualization(
            question, large_data
        )
        self.assertFalse(should_generate)  # Too many data points
    
    def test_chart_type_detection(self):
        """Test automatic chart type detection"""
        test_cases = [
            ("Show me sales trends over time", "line"),
            ("Compare sales by region", "bar"),
            ("Show market share breakdown", "pie"),
            ("Show correlation between variables", "scatter"),
            ("Display cumulative sales", "area")
        ]
        
        for question, expected_type in test_cases:
            with self.subTest(question=question):
                chart_data = self.visualization_service.extract_visualization_data(
                    self.sample_sales_data, question
                )
                if chart_data:
                    self.assertEqual(chart_data['type'], expected_type)

def run_visualization_demo():
    """Run a demonstration of the visualization functionality"""
    print("=== Database Visualization Feature Demo ===\n")
    
    service = DatabaseVisualizationService()
    
    # Demo 1: Sales comparison
    print("Demo 1: Sales Comparison")
    print("Question: 'Compare sales performance across different regions'")
    
    sales_data = [
        {
            'success': True,
            'data': [
                {'region': 'North', 'sales': 150000},
                {'region': 'South', 'sales': 120000},
                {'region': 'East', 'sales': 180000},
                {'region': 'West', 'sales': 90000}
            ],
            'columns': ['region', 'sales'],
            'row_count': 4
        }
    ]
    
    # Check if visualization should be generated
    should_generate = service.should_generate_visualization("Compare sales by region", sales_data)
    print(f"Should generate visualization: {should_generate}")
    
    # Extract chart data
    chart_data = service.extract_visualization_data(sales_data, "Compare sales by region")
    if chart_data:
        print(f"Chart type: {chart_data['type']}")
        print(f"Labels: {chart_data['labels']}")
        print(f"Data: {chart_data['datasets'][0]['data']}")
    
    # Get suggestions
    suggestions = service.get_visualization_suggestions("Compare sales by region", sales_data)
    print(f"Suggestions: {suggestions}")
    
    print("\n" + "="*50 + "\n")
    
    # Demo 2: Time series
    print("Demo 2: Time Series Analysis")
    print("Question: 'Show me sales trends over the last 6 months'")
    
    time_series_data = [
        {
            'success': True,
            'data': [
                {'month': 'Jan', 'sales': 100000},
                {'month': 'Feb', 'sales': 120000},
                {'month': 'Mar', 'sales': 110000},
                {'month': 'Apr', 'sales': 140000},
                {'month': 'May', 'sales': 130000},
                {'month': 'Jun', 'sales': 160000}
            ],
            'columns': ['month', 'sales'],
            'row_count': 6
        }
    ]
    
    chart_data = service.extract_visualization_data(time_series_data, "Show me sales trends over the last 6 months")
    if chart_data:
        print(f"Chart type: {chart_data['type']}")
        print(f"Labels: {chart_data['labels']}")
        print(f"Data: {chart_data['datasets'][0]['data']}")
    
    print("\n" + "="*50 + "\n")
    
    # Demo 3: Correlation
    print("Demo 3: Correlation Analysis")
    print("Question: 'Is there a relationship between advertising spend and sales?'")
    
    correlation_data = [
        {
            'success': True,
            'data': [
                {'advertising': 10000, 'sales': 150000},
                {'advertising': 15000, 'sales': 180000},
                {'advertising': 20000, 'sales': 220000},
                {'advertising': 25000, 'sales': 250000},
                {'advertising': 30000, 'sales': 280000}
            ],
            'columns': ['advertising', 'sales'],
            'row_count': 5
        }
    ]
    
    chart_data = service.extract_visualization_data(correlation_data, "Is there a relationship between advertising spend and sales?")
    if chart_data:
        print(f"Chart type: {chart_data['type']}")
        print(f"X-axis data: {chart_data['datasets'][0]['data']}")
        print(f"Y-axis data: {chart_data['datasets'][1]['data']}")
    
    print("\n=== Demo Complete ===")

if __name__ == '__main__':
    # Run the demo
    run_visualization_demo()
    
    # Run tests
    unittest.main(verbosity=2) 