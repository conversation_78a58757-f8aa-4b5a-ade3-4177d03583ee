"""
Database Visualization Service

This service handles the generation of charts and visualizations from database query results.
It provides intelligent chart type detection and data processing for various visualization types.
"""

import json
import logging
import pandas as pd
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import numpy as np
from utils.visualization_utils import generate_chart, determine_chart_type
from openai import AzureOpenAI
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)

logger = logging.getLogger(__name__)

class DatabaseVisualizationService:
    """Service for generating visualizations from database query results"""
    
    def __init__(self):
        self.supported_chart_types = ['bar', 'line', 'pie', 'scatter', 'area', 'heatmap']
        self.client = AzureOpenAI(
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT
        )
        self.deployment_name = AZURE_OPENAI_DEPLOYMENT_NAME
    
    def should_generate_visualization(self, question: str, query_results: List[Dict], 
                                    previous_messages: List = None, user_context: Dict = None) -> Dict[str, Any]:
        """
        Agentic function to determine if visualization should be generated and what type
        
        Returns a dictionary with:
        - should_generate: boolean
        - chart_type: string (if should_generate is True)
        - confidence: float (0-1)
        - reasoning: string
        - alternative_types: list of alternative chart types
        """
        try:
            logger.info(f"should_generate_visualization called with question: '{question}'")
            logger.info(f"Query results count: {len(query_results) if query_results else 0}")
            
            # Basic validation
            if not query_results or len(query_results) == 0:
                logger.info("No query results available")
                return {
                    'should_generate': False,
                    'chart_type': None,
                    'confidence': 0.0,
                    'reasoning': 'No query results available',
                    'alternative_types': []
                }
            
            # Analyze data structure
            data_analysis = self._analyze_data_structure(query_results)
            logger.info(f"Data analysis result: has_data={data_analysis.get('has_data', False)}")
            
            # Build context for AI analysis
            context = self._build_analysis_context(question, data_analysis, previous_messages, user_context)
            logger.info(f"Context built, length: {len(context)}")
            
            # Use AI to make intelligent decision
            ai_decision = self._get_ai_visualization_decision(context)
            logger.info(f"AI decision: {ai_decision}")
            
            # Validate AI decision against data constraints
            validated_decision = self._validate_ai_decision(ai_decision, data_analysis)
            logger.info(f"Validated decision: {validated_decision}")
            
            return validated_decision
            
        except Exception as e:
            logger.error(f"Error in should_generate_visualization: {str(e)}")
            import traceback
            traceback.print_exc()
            # Fallback to basic logic
            return self._fallback_visualization_decision(question, query_results)
    
    def _analyze_data_structure(self, query_results: List[Dict]) -> Dict[str, Any]:
        """Analyze the structure and characteristics of query results"""
        try:
            # Find the first successful query result with data
            data_result = None
            for result in query_results:
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    data_result = result
                    break
            
            if not data_result:
                return {'has_data': False}
            
            data = data_result['data']
            columns = data_result.get('columns', [])
            row_count = len(data)
            
            # Convert to DataFrame for analysis
            df = pd.DataFrame(data)
            
            # Analyze column types
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'string']).columns.tolist()
            datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
            
            # Detect time-related columns
            time_cols = []
            for col in columns:
                col_lower = col.lower()
                if any(time_word in col_lower for time_word in ['date', 'time', 'year', 'month', 'day', 'quarter', 'week']):
                    time_cols.append(col)
            
            # Analyze data characteristics
            data_characteristics = {
                'row_count': row_count,
                'column_count': len(columns),
                'numeric_columns': numeric_cols,
                'categorical_columns': categorical_cols,
                'datetime_columns': datetime_cols,
                'time_related_columns': time_cols,
                'has_time_series': len(time_cols) > 0,
                'has_categories': len(categorical_cols) > 0,
                'has_numeric_data': len(numeric_cols) > 0,
                'data_range': {
                    'min_rows': 1,
                    'max_rows': 100,
                    'suitable_for_viz': 1 <= row_count <= 100
                }
            }
            
            # Analyze data patterns
            if len(numeric_cols) > 0:
                numeric_data = df[numeric_cols[0]]
                data_characteristics.update({
                    'numeric_range': {
                        'min': float(numeric_data.min()),
                        'max': float(numeric_data.max()),
                        'mean': float(numeric_data.mean()),
                        'std': float(numeric_data.std())
                    },
                    'has_variance': numeric_data.std() > 0
                })
            
            # Detect percentage-like data
            if len(numeric_cols) > 0:
                total_sum = df[numeric_cols[0]].sum()
                if 95 <= total_sum <= 105:
                    data_characteristics['is_percentage_data'] = True
                else:
                    data_characteristics['is_percentage_data'] = False
            
            return {
                'has_data': True,
                'columns': columns,
                'characteristics': data_characteristics,
                'sample_data': data[:3] if len(data) > 3 else data  # First 3 rows for context
            }
            
        except Exception as e:
            logger.error(f"Error analyzing data structure: {str(e)}")
            return {'has_data': False, 'error': str(e)}
    
    def _build_analysis_context(self, question: str, data_analysis: Dict, 
                               previous_messages: List = None, user_context: Dict = None) -> str:
        """Build comprehensive context for AI analysis"""
        
        # Question analysis
        question_context = f"""
User Question: "{question}"

Question Analysis:
- Length: {len(question)} characters
- Contains visualization keywords: {self._has_visualization_keywords(question)}
- Question type: {self._classify_question_type(question)}
"""
        
        # Data analysis
        if data_analysis.get('has_data'):
            characteristics = data_analysis['characteristics']
            data_context = f"""
Data Structure Analysis:
- Rows: {characteristics['row_count']}
- Columns: {characteristics['column_count']}
- Numeric columns: {characteristics['numeric_columns']}
- Categorical columns: {characteristics['categorical_columns']}
- Time-related columns: {characteristics['time_related_columns']}
- Has time series: {characteristics['has_time_series']}
- Has categories: {characteristics['has_categories']}
- Has numeric data: {characteristics['has_numeric_data']}
- Suitable for visualization: {characteristics['data_range']['suitable_for_viz']}
- Is percentage data: {characteristics.get('is_percentage_data', False)}
- Has variance: {characteristics.get('has_variance', False)}

Sample Data: {data_analysis['sample_data']}
"""
        else:
            data_context = "Data Structure: No data available"
        
        # Conversation context
        conversation_context = ""
        if previous_messages:
            conversation_context = "\nPrevious Conversation Context:\n"
            for i, msg in enumerate(previous_messages[-3:]):  # Last 3 messages
                conversation_context += f"Message {i+1}: {msg.question[:100]}...\n"
        
        # User context
        user_context_str = ""
        if user_context:
            user_context_str = f"\nUser Context: {json.dumps(user_context, indent=2)}"
        
        return f"{question_context}\n{data_context}\n{conversation_context}{user_context_str}"
    
    def _get_ai_visualization_decision(self, context: str) -> Dict[str, Any]:
        """Use AI to make intelligent visualization decision"""
        try:
            prompt = f"""
You are an expert data visualization analyst. Analyze the following context and determine if a visualization should be generated and what type would be most appropriate.

Context:
{context}

Consider the following factors:
1. User's question and intent
2. Data structure and characteristics
3. Previous conversation context
4. Best practices for data visualization
5. User experience and clarity

Available chart types: bar, line, pie, scatter, area, heatmap

Respond in JSON format:
{{
    "should_generate": true/false,
    "chart_type": "bar|line|pie|scatter|area|heatmap|null",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of the decision",
    "alternative_types": ["list", "of", "alternative", "chart", "types"],
    "user_benefit": "How this visualization will help the user understand the data"
}}

Guidelines:
- Generate visualization if it adds value to understanding the data
- Choose chart type based on data characteristics and user intent
- Consider data size (1-100 rows is optimal)
- Bar charts for comparisons, line for trends, pie for distributions, scatter for correlations
- High confidence (>0.7) for clear cases, lower for ambiguous situations
"""

            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a data visualization expert. Always respond with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=800
            )
            
            decision_text = response.choices[0].message.content.strip()
            
            # Parse JSON response
            try:
                decision = json.loads(decision_text)
                return decision
            except json.JSONDecodeError:
                logger.error(f"Failed to parse AI decision JSON: {decision_text}")
                return self._fallback_visualization_decision("", [])
                
        except Exception as e:
            logger.error(f"Error getting AI visualization decision: {str(e)}")
            return self._fallback_visualization_decision("", [])
    
    def _validate_ai_decision(self, ai_decision: Dict, data_analysis: Dict) -> Dict[str, Any]:
        """Validate AI decision against data constraints and business rules"""
        
        if not ai_decision.get('should_generate', False):
            return ai_decision
        
        # Validate chart type
        chart_type = ai_decision.get('chart_type')
        if chart_type not in self.supported_chart_types:
            ai_decision['chart_type'] = 'bar'  # Default fallback
            ai_decision['confidence'] *= 0.8  # Reduce confidence
            ai_decision['reasoning'] += " (Chart type adjusted to supported type)"
        
        # Validate data constraints
        if data_analysis.get('has_data'):
            characteristics = data_analysis['characteristics']
            
            # Check if data size is suitable
            if not characteristics['data_range']['suitable_for_viz']:
                ai_decision['should_generate'] = False
                ai_decision['reasoning'] = f"Data size ({characteristics['row_count']} rows) not suitable for visualization"
                return ai_decision
            
            # Validate chart type against data characteristics
            if chart_type == 'pie' and characteristics['row_count'] > 8:
                ai_decision['chart_type'] = 'bar'
                ai_decision['reasoning'] += " (Too many categories for pie chart, using bar chart)"
            
            if chart_type == 'scatter' and len(characteristics['numeric_columns']) < 2:
                ai_decision['chart_type'] = 'bar'
                ai_decision['reasoning'] += " (Insufficient numeric columns for scatter plot)"
            
            if chart_type == 'line' and not characteristics['has_time_series']:
                # Check if we have sequential data
                if characteristics['row_count'] < 3:
                    ai_decision['chart_type'] = 'bar'
                    ai_decision['reasoning'] += " (Insufficient data points for line chart)"
        
        return ai_decision
    
    def _fallback_visualization_decision(self, question: str, query_results: List[Dict]) -> Dict[str, Any]:
        """Fallback logic when AI analysis fails"""
        question_lower = question.lower()
        
        # Check for explicit visualization requests
        viz_keywords = [
            'chart', 'graph', 'plot', 'visualization', 'visualize', 'show me',
            'display', 'compare', 'trend', 'distribution', 'breakdown',
            'percentage', 'proportion', 'share', 'over time', 'by category',
            'correlation', 'relationship', 'analysis'
        ]
        
        should_generate = any(keyword in question_lower for keyword in viz_keywords)
        
        if should_generate:
            # Simple keyword-based chart type detection
            if any(word in question_lower for word in ['trend', 'over time', 'growth']):
                chart_type = 'line'
            elif any(word in question_lower for word in ['compare', 'versus', 'vs']):
                chart_type = 'bar'
            elif any(word in question_lower for word in ['distribution', 'breakdown', 'share']):
                chart_type = 'pie'
            elif any(word in question_lower for word in ['correlation', 'relationship']):
                chart_type = 'scatter'
            else:
                chart_type = 'bar'
            
            return {
                'should_generate': True,
                'chart_type': chart_type,
                'confidence': 0.6,
                'reasoning': 'Fallback decision based on keyword analysis',
                'alternative_types': ['bar', 'line', 'pie'],
                'user_benefit': 'Visual representation of the data for better understanding'
            }
        else:
            return {
                'should_generate': False,
                'chart_type': None,
                'confidence': 0.5,
                'reasoning': 'No clear visualization need detected',
                'alternative_types': [],
                'user_benefit': 'Text-based response is sufficient'
            }
    
    def _has_visualization_keywords(self, question: str) -> bool:
        """Check if question contains visualization-related keywords"""
        viz_keywords = [
            'chart', 'graph', 'plot', 'visualization', 'visualize', 'show me',
            'display', 'compare', 'trend', 'distribution', 'breakdown',
            'percentage', 'proportion', 'share', 'over time', 'by category',
            'correlation', 'relationship', 'analysis'
        ]
        return any(keyword in question.lower() for keyword in viz_keywords)
    
    def _classify_question_type(self, question: str) -> str:
        """Classify the type of question being asked"""
        question_lower = question.lower()
        
        if any(word in question_lower for word in ['compare', 'versus', 'vs', 'difference']):
            return 'comparison'
        elif any(word in question_lower for word in ['trend', 'over time', 'growth', 'decline']):
            return 'trend_analysis'
        elif any(word in question_lower for word in ['distribution', 'breakdown', 'share', 'percentage']):
            return 'distribution'
        elif any(word in question_lower for word in ['correlation', 'relationship', 'correlate']):
            return 'correlation'
        elif any(word in question_lower for word in ['total', 'sum', 'count', 'how many']):
            return 'aggregation'
        elif any(word in question_lower for word in ['list', 'show', 'get', 'find']):
            return 'listing'
        else:
            return 'general'

    def extract_visualization_data(self, query_results: List[Dict], question: str) -> Optional[Dict[str, Any]]:
        """
        Extract and structure data for visualization from query results
        """
        try:
            # Find the first successful query result with data
            data_result = None
            for result in query_results:
                if result.get('success') and result.get('data') and len(result['data']) > 0:
                    data_result = result
                    break
            
            if not data_result:
                return None
            
            data = data_result['data']
            columns = data_result.get('columns', [])
            
            if not data or not columns:
                return None
            
            # Convert to pandas DataFrame for easier manipulation
            df = pd.DataFrame(data)
            
            # Use the agentic decision to determine chart type
            decision = self.should_generate_visualization(question, query_results)
            chart_type = decision.get('chart_type', 'bar')
            
            # Process data based on chart type
            if chart_type == 'pie':
                return self._prepare_pie_chart_data(df, columns, question)
            elif chart_type == 'line':
                return self._prepare_line_chart_data(df, columns, question)
            elif chart_type == 'bar':
                return self._prepare_bar_chart_data(df, columns, question)
            elif chart_type == 'scatter':
                return self._prepare_scatter_chart_data(df, columns, question)
            elif chart_type == 'area':
                return self._prepare_area_chart_data(df, columns, question)
            else:
                # Default to bar chart for unknown types
                return self._prepare_bar_chart_data(df, columns, question)
                
        except Exception as e:
            logger.error(f"Error extracting visualization data: {str(e)}")
            return None
    
    def _prepare_pie_chart_data(self, df: pd.DataFrame, columns: List[str], question: str) -> Dict[str, Any]:
        """Prepare data for pie chart visualization"""
        try:
            # For pie charts, we need one categorical column and one numeric column
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'string']).columns.tolist()
            
            if len(numeric_cols) == 0 or len(categorical_cols) == 0:
                # If no clear numeric/categorical split, use first two columns
                if len(columns) >= 2:
                    label_col = columns[0]
                    value_col = columns[1]
                else:
                    return None
            else:
                label_col = categorical_cols[0]
                value_col = numeric_cols[0]
            
            # Aggregate data if needed
            if len(df) > 10:  # Too many categories for pie chart
                # Group by label and sum values, take top 8
                grouped = df.groupby(label_col)[value_col].sum().sort_values(ascending=False).head(8)
                labels = grouped.index.tolist()
                values = grouped.values.tolist()
            else:
                labels = df[label_col].tolist()
                values = df[value_col].tolist()
            
            # Generate title
            title = f"{question.split('?')[0].strip()} - Distribution"
            
            return {
                'type': 'pie',
                'title': title,
                'labels': labels,
                'datasets': [{
                    'label': value_col,
                    'data': values
                }]
            }
            
        except Exception as e:
            logger.error(f"Error preparing pie chart data: {str(e)}")
            return None
    
    def _prepare_bar_chart_data(self, df: pd.DataFrame, columns: List[str], question: str) -> Dict[str, Any]:
        """Prepare data for bar chart visualization"""
        try:
            # For bar charts, we need categorical labels and numeric values
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_cols = df.select_dtypes(include=['object', 'string']).columns.tolist()
            
            if len(numeric_cols) == 0:
                # If no numeric columns, count occurrences
                if len(categorical_cols) > 0:
                    label_col = categorical_cols[0]
                    value_counts = df[label_col].value_counts().head(10)
                    labels = value_counts.index.tolist()
                    values = value_counts.values.tolist()
                    value_col = 'Count'
                else:
                    return None
            else:
                if len(categorical_cols) > 0:
                    label_col = categorical_cols[0]
                    value_col = numeric_cols[0]
                    
                    # Group by category and aggregate
                    grouped = df.groupby(label_col)[value_col].sum().sort_values(ascending=False).head(10)
                    labels = grouped.index.tolist()
                    values = grouped.values.tolist()
                else:
                    # Use first two numeric columns
                    if len(numeric_cols) >= 2:
                        labels = df[numeric_cols[0]].tolist()
                        values = df[numeric_cols[1]].tolist()
                        value_col = numeric_cols[1]
                    else:
                        return None
            
            # Generate title
            title = f"{question.split('?')[0].strip()} - Comparison"
            
            return {
                'type': 'bar',
                'title': title,
                'labels': labels,
                'datasets': [{
                    'label': value_col,
                    'data': values
                }]
            }
            
        except Exception as e:
            logger.error(f"Error preparing bar chart data: {str(e)}")
            return None
    
    def _prepare_line_chart_data(self, df: pd.DataFrame, columns: List[str], question: str) -> Dict[str, Any]:
        """Prepare data for line chart visualization"""
        try:
            # For line charts, we need time-series or sequential data
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_cols) < 2:
                return None
            
            # Try to find time-related columns
            time_cols = [col for col in columns if any(time_word in col.lower() 
                       for time_word in ['date', 'time', 'year', 'month', 'day', 'quarter'])]
            
            if time_cols:
                # Use time column as x-axis
                x_col = time_cols[0]
                y_col = numeric_cols[0]
                
                # Sort by time
                df_sorted = df.sort_values(x_col)
                labels = df_sorted[x_col].tolist()
                values = df_sorted[y_col].tolist()
            else:
                # Use first numeric column as x-axis, second as y-axis
                x_col = numeric_cols[0]
                y_col = numeric_cols[1]
                
                df_sorted = df.sort_values(x_col)
                labels = df_sorted[x_col].tolist()
                values = df_sorted[y_col].tolist()
            
            # Generate title
            title = f"{question.split('?')[0].strip()} - Trend"
            
            return {
                'type': 'line',
                'title': title,
                'labels': labels,
                'datasets': [{
                    'label': y_col,
                    'data': values
                }]
            }
            
        except Exception as e:
            logger.error(f"Error preparing line chart data: {str(e)}")
            return None
    
    def _prepare_scatter_chart_data(self, df: pd.DataFrame, columns: List[str], question: str) -> Dict[str, Any]:
        """Prepare data for scatter chart visualization"""
        try:
            # For scatter plots, we need two numeric columns
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_cols) < 2:
                return None
            
            x_col = numeric_cols[0]
            y_col = numeric_cols[1]
            
            x_values = df[x_col].tolist()
            y_values = df[y_col].tolist()
            
            # Generate title
            title = f"{question.split('?')[0].strip()} - Correlation"
            
            return {
                'type': 'scatter',
                'title': title,
                'labels': x_values,
                'datasets': [
                    {
                        'label': x_col,
                        'data': x_values
                    },
                    {
                        'label': y_col,
                        'data': y_values
                    }
                ]
            }
            
        except Exception as e:
            logger.error(f"Error preparing scatter chart data: {str(e)}")
            return None
    
    def _prepare_area_chart_data(self, df: pd.DataFrame, columns: List[str], question: str) -> Dict[str, Any]:
        """Prepare data for area chart visualization"""
        try:
            # Similar to line chart but for area visualization
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_cols) < 2:
                return None
            
            # Try to find time-related columns
            time_cols = [col for col in columns if any(time_word in col.lower() 
                       for time_word in ['date', 'time', 'year', 'month', 'day', 'quarter'])]
            
            if time_cols:
                x_col = time_cols[0]
                y_col = numeric_cols[0]
            else:
                x_col = numeric_cols[0]
                y_col = numeric_cols[1]
            
            df_sorted = df.sort_values(x_col)
            labels = df_sorted[x_col].tolist()
            values = df_sorted[y_col].tolist()
            
            # Generate title
            title = f"{question.split('?')[0].strip()} - Area Trend"
            
            return {
                'type': 'area',
                'title': title,
                'labels': labels,
                'datasets': [{
                    'label': y_col,
                    'data': values
                }]
            }
            
        except Exception as e:
            logger.error(f"Error preparing area chart data: {str(e)}")
            return None
    
    def generate_visualization(self, question: str, query_results: List[Dict], 
                             previous_messages: List = None, user_context: Dict = None) -> Optional[Dict[str, Any]]:
        """
        Main method to generate visualization from database query results
        """
        try:
            logger.info(f"generate_visualization called with question: '{question}'")
            
            # Use agentic decision making
            decision = self.should_generate_visualization(question, query_results, previous_messages, user_context)
            logger.info(f"Decision result: {decision}")
            
            if not decision.get('should_generate', False):
                logger.info("Visualization not needed based on decision")
                return None
            
            logger.info("Visualization needed, proceeding with generation...")
            
            # Extract and structure data
            chart_data = self.extract_visualization_data(query_results, question)
            logger.info(f"Chart data extracted: {chart_data is not None}")
            if not chart_data:
                logger.warning("Failed to extract chart data")
                return None
            
            logger.info(f"Chart data: type={chart_data.get('type')}, title={chart_data.get('title')}")
            
            # Generate the chart image
            logger.info("Generating chart image...")
            chart_image = generate_chart(chart_data)
            logger.info(f"Chart image generated: {chart_image is not None}")
            if not chart_image:
                logger.warning("Failed to generate chart image")
                return None
            
            # Prepare visualization metadata
            visualization_metadata = {
                'chart_type': chart_data['type'],
                'data_points': len(chart_data.get('labels', [])),
                'generated_at': datetime.utcnow().isoformat(),
                'ai_decision': decision,
                'question_analysis': {
                    'original_question': question,
                    'detected_chart_type': chart_data['type'],
                    'confidence': decision.get('confidence', 0.0),
                    'reasoning': decision.get('reasoning', ''),
                    'user_benefit': decision.get('user_benefit', ''),
                    'alternative_types': decision.get('alternative_types', [])
                }
            }
            
            result = {
                'visualization_data': chart_data,
                'visualization_type': chart_data['type'],
                'visualization_image': chart_image,
                'visualization_metadata': visualization_metadata
            }
            
            logger.info(f"Visualization generation completed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error generating visualization: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    def get_visualization_suggestions(self, question: str, query_results: List[Dict]) -> List[str]:
        """
        Get suggestions for alternative visualization types
        """
        suggestions = []
        
        try:
            # Get the AI decision to see what alternatives were suggested
            decision = self.should_generate_visualization(question, query_results)
            alternative_types = decision.get('alternative_types', [])
            
            # Format suggestions
            for chart_type in alternative_types:
                if chart_type == 'pie':
                    suggestions.append("pie - For showing proportions and percentages")
                elif chart_type == 'bar':
                    suggestions.append("bar - For easier comparison of values")
                elif chart_type == 'line':
                    suggestions.append("line - For showing trends over time")
                elif chart_type == 'area':
                    suggestions.append("area - For showing cumulative trends")
                elif chart_type == 'scatter':
                    suggestions.append("scatter - For showing relationships between variables")
                elif chart_type == 'heatmap':
                    suggestions.append("heatmap - For showing patterns in large datasets")
            
            # If no AI suggestions, provide basic ones
            if not suggestions:
                suggestions = ["bar - For comparisons", "line - For trends", "pie - For distributions"]
            
        except Exception as e:
            logger.error(f"Error getting visualization suggestions: {str(e)}")
            suggestions = ["bar - For comparisons", "line - For trends", "pie - For distributions"]
        
        return suggestions

# Global instance
database_visualization_service = DatabaseVisualizationService() 