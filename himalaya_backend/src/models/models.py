from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
from sqlalchemy.dialects.postgresql import ARRAY
from sqlalchemy.orm import relationship

db = SQLAlchemy()

# Define association tables first
user_verticals = db.<PERSON>('user_verticals',
    db.<PERSON>umn('id', db.Integer, primary_key=True),
    db.<PERSON>('user_id', db.Integer, db.<PERSON>('users.id', ondelete='CASCADE')),
    db.<PERSON>('vertical_id', db.Integer, db.<PERSON>('verticals.id', ondelete='CASCADE')),
    db.<PERSON>umn('created_at', db.DateTime, default=datetime.utcnow)
)

user_departments = db.Table('user_departments',
    db.Column('id', db.Integer, primary_key=True),
    db.Column('user_id', db.Integer, db.<PERSON>('users.id', ondelete='CASCADE')),
    db.<PERSON>umn('department_id', db.Integer, db.<PERSON>('departments.id', ondelete='CASCADE')),
    db.<PERSON>('vertical_id', db.Integer, db.Foreign<PERSON>ey('verticals.id', ondelete='CASCADE')),
    db.Column('created_at', db.DateTime, default=datetime.utcnow)
)
# Association table for user database permissions
user_database_permissions = db.Table('user_database_permissions',
    db.Column('id', db.Integer, primary_key=True),
    db.Column('user_id', db.Integer, db.ForeignKey('users.id', ondelete='CASCADE')),
    db.Column('external_database_id', db.Integer, db.ForeignKey('external_databases.id', ondelete='CASCADE')),
    db.Column('permission_level', db.String(50), default='read'),  # read, write, admin
    db.Column('granted_by', db.Integer, db.ForeignKey('users.id')),
    db.Column('created_at', db.DateTime, default=datetime.utcnow),
    db.Column('expires_at', db.DateTime, nullable=True),
    db.Column('allowed_schemas', db.JSON, nullable=True)  # JSON array of allowed schema names, NULL means all schemas
)


# Then define the models
class Department(db.Model):
    __tablename__ = 'departments'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    vertical_id = db.Column(db.Integer, db.ForeignKey('verticals.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Add relationship to vertical
    vertical = db.relationship('Vertical', backref='departments')

class Position(db.Model):
    __tablename__ = 'positions'
    id = db.Column(db.Integer, primary_key=True)
    level = db.Column(db.Integer, nullable=False, unique=True)
    name = db.Column(db.String(100), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Vertical(db.Model):
    __tablename__ = 'verticals'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(db.Integer, primary_key=True)
    user_name = db.Column(db.String(100), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    ms_user_id = db.Column(db.String(100), unique=True)
    scopes = db.Column(ARRAY(db.Integer), nullable=True)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Add relationships for verticals and departments
    verticals = db.relationship('Vertical', secondary=user_verticals, backref='users')
    departments = db.relationship('Department', secondary=user_departments, backref='users')

class Scope(db.Model):
    __tablename__ = 'scopes'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Sensitivity(db.Model):
    __tablename__ = 'sensitivities'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Tag(db.Model):
    __tablename__ = 'tags'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    vertical_id = db.Column(db.Integer, db.ForeignKey('verticals.id'), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    creator = db.relationship('User', backref='created_tags', lazy=True)
    vertical = db.relationship('Vertical', backref='tags', lazy=True)
    department = db.relationship('Department', backref='tags', lazy=True)

class FileFormat(db.Model):
    __tablename__ = 'file_formats'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    files = db.relationship('File', backref='format', lazy=True)

class FileType(db.Model):
    __tablename__ = 'file_types'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationship
    files = db.relationship('File', backref='type', lazy=True)

class File(db.Model):
    __tablename__ = 'files'
    id = db.Column(db.Integer, primary_key=True)
    file_name = db.Column(db.String(255), nullable=False)
    file_format_id = db.Column(db.Integer, db.ForeignKey('file_formats.id'))
    file_type_id = db.Column(db.Integer, db.ForeignKey('file_types.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    vertical_id = db.Column(db.Integer, db.ForeignKey('verticals.id'))
    blob_url = db.Column(db.Text, nullable=False)
    uploaded_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    upload_time = db.Column(db.DateTime, default=datetime.utcnow)
    updated_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Existing media processing columns
    media_type = db.Column(db.String(50))  # 'video' or 'audio'
    transcription_blob_url = db.Column(db.Text)
    processing_status = db.Column(db.String(50), default='pending')
    error_message = db.Column(db.Text)
    processed_at = db.Column(db.DateTime)
    archived_uploader_name = db.Column(db.String(100))
    comments = db.Column(db.Text)  # New column for file comments
    file_tags = db.Column(db.JSON, nullable=True)  # Changed from ARRAY to JSON type
    sensitivity_id = db.Column(db.Integer, db.ForeignKey('sensitivities.id'), nullable=False, default=4)  # Default to General

    # Relationships
    uploader = db.relationship('User', backref='uploaded_files', lazy=True)
    department = db.relationship('Department', backref='files', lazy=True)
    vertical = db.relationship('Vertical', backref='files', lazy=True)
    sensitivity = db.relationship('Sensitivity', backref='files', lazy=True)
    
    # NEW RELATIONSHIPS FOR ENHANCED PROCESSING
    processing_metadata = db.relationship('FileProcessingMetadata', backref='file', uselist=False, lazy=True, cascade='all, delete-orphan')
    chunks = db.relationship('DocumentChunk', backref='file', lazy=True, cascade='all, delete-orphan')
    tables = db.relationship('DocumentTable', backref='file', lazy=True, cascade='all, delete-orphan')

class FileProcessingMetadata(db.Model):
    """
    Stores enhanced document processing metadata separately from the main files table.
    This keeps the files table clean and follows database normalization principles.
    """
    __tablename__ = 'file_processing_metadata'
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=False, unique=True)  # 1:1 relationship
    
    # Document classification and analysis
    document_type = db.Column(db.String(50))  # pdf, excel, word, image, etc.
    content_summary = db.Column(db.Text)      # AI-generated summary of content
    extracted_entities = db.Column(db.JSON)   # Named entities extracted from document
    
    # Document statistics
    page_count = db.Column(db.Integer, default=0)    # Number of pages in document
    table_count = db.Column(db.Integer, default=0)   # Number of tables found
    chunk_count = db.Column(db.Integer, default=0)   # Number of chunks created
    word_count = db.Column(db.Integer, default=0)    # Total word count
    char_count = db.Column(db.Integer, default=0)    # Total character count
    
    # Processing status and metadata
    enhanced_processing_status = db.Column(db.String(50), default='pending')  # pending, processing, completed, failed
    processing_metadata = db.Column(db.JSON)  # Rich processing information (confidence scores, processing time, etc.)
    error_details = db.Column(db.JSON)        # Detailed error information if processing fails
    
    # Processing timestamps
    processing_started_at = db.Column(db.DateTime)   # When enhanced processing started
    enhanced_processed_at = db.Column(db.DateTime)   # When enhanced processing completed
    
    # Processing configuration used
    chunk_size_used = db.Column(db.Integer)          # Chunk size used for this document
    chunk_overlap_used = db.Column(db.Integer)       # Chunk overlap used for this document
    embedding_model_used = db.Column(db.String(100)) # Embedding model used
    
    # Quality metrics
    processing_confidence_score = db.Column(db.Float) # Overall confidence in processing quality
    extraction_quality_score = db.Column(db.Float)    # Quality score for content extraction
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_file_processing_metadata_file_id', 'file_id'),
        db.Index('idx_file_processing_metadata_status', 'enhanced_processing_status'),
        db.Index('idx_file_processing_metadata_document_type', 'document_type'),
    )

class DocumentChunk(db.Model):
    """
    Stores intelligent chunks of documents with metadata for enhanced search and retrieval.
    Each chunk represents a semantically meaningful portion of the document.
    """
    __tablename__ = 'document_chunks'
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=False)
    chunk_index = db.Column(db.Integer, nullable=False)  # Order of chunk in document
    content = db.Column(db.Text, nullable=False)  # The actual text content
    chunk_metadata = db.Column(db.JSON)  # Page numbers, section info, chunk type, etc.
    
    # Azure AI Search integration - store the search document ID for reference
    azure_search_doc_id = db.Column(db.String(255))  # Reference to Azure AI Search document
    
    # Chunk statistics
    word_count = db.Column(db.Integer)
    char_count = db.Column(db.Integer)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_document_chunks_file_id', 'file_id'),
        db.Index('idx_document_chunks_azure_search_doc_id', 'azure_search_doc_id'),
    )

class DocumentTable(db.Model):
    """
    Stores extracted tables from documents with structured data and metadata.
    Supports advanced table processing including split table detection and merging.
    """
    __tablename__ = 'document_tables'
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=False)
    table_index = db.Column(db.Integer, nullable=False)  # Order of table in document
    page_number = db.Column(db.Integer)  # Page where table appears
    
    # Table structure and data
    table_data = db.Column(db.JSON, nullable=False)  # Structured table data (rows, columns)
    table_headers = db.Column(db.JSON)  # Column headers
    table_summary = db.Column(db.Text)  # AI-generated summary of table content
    
    # Table processing metadata
    table_metadata = db.Column(db.JSON)  # Processing info, confidence scores, etc.
    is_split_table = db.Column(db.Boolean, default=False)  # Part of a table split across pages
    merged_table_group_id = db.Column(db.String(100))  # ID for grouping split tables
    
    # Table statistics
    row_count = db.Column(db.Integer)
    column_count = db.Column(db.Integer)
    
    # Azure AI Search integration
    azure_search_doc_id = db.Column(db.String(255))  # Reference to Azure AI Search document
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_document_tables_file_id', 'file_id'),
        db.Index('idx_document_tables_merged_group', 'merged_table_group_id'),
        db.Index('idx_document_tables_azure_search_doc_id', 'azure_search_doc_id'),
    )

class ChatSession(db.Model):
    __tablename__ = 'chat_sessions'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    vertical_id = db.Column(db.Integer, db.ForeignKey('verticals.id'), nullable=True)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=True)
    session_name = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    user = db.relationship('User', backref='chat_sessions', lazy=True)
    vertical = db.relationship('Vertical', backref='chat_sessions', lazy=True)
    department = db.relationship('Department', backref='chat_sessions', lazy=True)
    messages = db.relationship('ChatMessage', backref='session', lazy=True, order_by='ChatMessage.created_at')

class ChatMessage(db.Model):
    __tablename__ = 'chat_messages'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('chat_sessions.id'), nullable=False)
    question = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=False)
    sources = db.Column(db.JSON)  # Store sources as JSON
    token_usage = db.Column(db.JSON)  # Store token usage stats
    search_metadata = db.Column(db.JSON)  # Renamed from metadata to search_metadata
    web_search = db.Column(db.Boolean, default=False)  # New column to indicate web search used
    
    # NEW AGENTIC SYSTEM COLUMNS
    agent_type = db.Column(db.String(50), default='rag')  # Type of agent that processed the query
    agent_metadata = db.Column(db.JSON)  # Agent-specific metadata and processing details
    processing_time = db.Column(db.Float)  # Time taken to process the query in seconds
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Language(db.Model):
    __tablename__ = 'languages'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False, unique=True)
    code = db.Column(db.String(10), nullable=False, unique=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class FGD(db.Model):
    __tablename__ = 'fgds'
    id = db.Column(db.Integer, primary_key=True)
    theme_id = db.Column(db.Integer, db.ForeignKey('themes.id'), nullable=False)
    language_id = db.Column(db.Integer, db.ForeignKey('languages.id'), nullable=False)
    group_size = db.Column(db.Integer, nullable=False)
    conductor_name = db.Column(db.String(255), nullable=False)
    discussion_date = db.Column(db.Date, nullable=False)
    country = db.Column(db.String(100))
    status = db.Column(db.String(50), default='Pending')
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # NEW: Track who created the FGD
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    participants = relationship('Participant', backref='fgd', lazy=True)
    videos = relationship('Video', backref='fgd', lazy=True)
    language = relationship('Language', foreign_keys=[language_id])
    creator = relationship('User', backref='created_fgds', lazy=True)  # NEW: Relationship to FGD creator

class Participant(db.Model):
    __tablename__ = 'participants'
    id = db.Column(db.Integer, primary_key=True)
    fgd_id = db.Column(db.Integer, db.ForeignKey('fgds.id'), nullable=False)
    gender = db.Column(db.String(10))
    age = db.Column(db.Integer)
    nationality = db.Column(db.String(100))
    marital_status = db.Column(db.String(50))
    has_children = db.Column(db.Boolean)
    age_range = db.Column(db.String(50))

class Video(db.Model):
    __tablename__ = 'videos'
    id = db.Column(db.Integer, primary_key=True)
    fgd_id = db.Column(db.Integer, db.ForeignKey('fgds.id'), nullable=False)
    blob_url = db.Column(db.Text, nullable=False)
    audio_blob_url = db.Column(db.Text, nullable=True)
    transcription_blob_url = db.Column(db.Text, nullable=True)
    transcription_json_url = db.Column(db.Text, nullable=True)
    transcription_text = db.Column(db.Text, nullable=True)
    transcription_metadata = db.Column(db.JSON, nullable=True)  # Renamed from metadata to transcription_metadata
    processing_status = db.Column(db.String(50), default='pending')  # pending, processing, completed, failed
    error_message = db.Column(db.Text, nullable=True)
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    processed_at = db.Column(db.DateTime, nullable=True)
    file_name = db.Column(db.String(255), nullable=True)  # New column for original file name
    vector_blob_url = db.Column(db.Text, nullable=True)  # New column for vector blob URL

class Theme(db.Model):
    __tablename__ = 'themes'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)
    objective = db.Column(db.Text, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=True)  # New field
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    creator = relationship('User', backref='created_themes', lazy=True)  # New relationship
    owners = relationship('User', secondary='theme_owners', backref='themes', lazy=True)
    languages = relationship('Language', secondary='theme_languages', backref='themes', lazy=True)
    discussion_guides = relationship('ThemeDiscussionGuide', backref='theme', lazy=True)
    interview_guide_language_id = db.Column(db.Integer, db.ForeignKey('languages.id'), nullable=True)
    interview_guide_language = relationship('Language', foreign_keys=[interview_guide_language_id])
    fgds = relationship('FGD', backref='theme', lazy=True)

# Define the association tables for many-to-many relationships
theme_owners = db.Table('theme_owners',
    db.Column('theme_id', db.Integer, db.ForeignKey('themes.id'), primary_key=True),
    db.Column('user_id', db.Integer, db.ForeignKey('users.id'), primary_key=True)
)

theme_languages = db.Table('theme_languages',
    db.Column('theme_id', db.Integer, db.ForeignKey('themes.id'), primary_key=True),
    db.Column('language_id', db.Integer, db.ForeignKey('languages.id'), primary_key=True)
)

class ThemeDiscussionGuide(db.Model):
    __tablename__ = 'theme_discussion_guides'
    id = db.Column(db.Integer, primary_key=True)
    theme_id = db.Column(db.Integer, db.ForeignKey('themes.id'), nullable=False)
    language_id = db.Column(db.Integer, db.ForeignKey('languages.id'), nullable=False)
    guide_url = db.Column(db.Text, nullable=False)
    is_base_language = db.Column(db.Boolean, default=False)
    
    # Add this relationship
    language = relationship('Language', foreign_keys=[language_id])

class Presentation(db.Model):
    __tablename__ = 'presentations'
    id = db.Column(db.Integer, primary_key=True)
    theme_id = db.Column(db.Integer, db.ForeignKey('themes.id'), nullable=False)
    ppt_url = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    theme = relationship('Theme', backref='presentations', lazy=True)

class FGDChatSession(db.Model):
    __tablename__ = 'fgd_chat_sessions'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    session_name = db.Column(db.String(255))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    user = db.relationship('User', backref='fgd_chat_sessions', lazy=True)
    messages = db.relationship('FGDChatMessage', backref='session', lazy=True, order_by='FGDChatMessage.created_at')

class FGDChatMessage(db.Model):
    __tablename__ = 'fgd_chat_messages'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('fgd_chat_sessions.id'), nullable=False)
    question = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=False)
    selected_video_ids = db.Column(ARRAY(db.Integer), nullable=False)
    token_usage = db.Column(db.JSON)
    search_metadata = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# ============================================================================
# AGENTIC SYSTEM MODELS - Added for enhanced CSV processing
# ============================================================================

class ExcelFile(db.Model):
    """
    Model for storing Excel file metadata for agentic CSV processing.
    Links to the existing file system while providing specialized Excel tracking.
    """
    __tablename__ = 'excel_files'
    
    id = db.Column(db.Integer, primary_key=True)
    file_id = db.Column(db.String(255), nullable=False, unique=True)  # Unique identifier for Excel processing
    original_file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=True)  # Link to existing file system
    file_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    storage_path = db.Column(db.String(255), nullable=True)
    azure_url = db.Column(db.String(255), nullable=True)
    sheet_count = db.Column(db.Integer, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    csv_files = db.relationship('CSVFile', backref='excel_file', lazy=True, cascade='all, delete-orphan')
    original_file = db.relationship('File', backref='excel_files', lazy=True)
    
    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_excel_files_file_id', 'file_id'),
        db.Index('idx_excel_files_original_file_id', 'original_file_id'),
        db.Index('idx_excel_files_created_at', 'created_at'),
    )

    def __repr__(self):
        return f"<ExcelFile(id={self.id}, file_id='{self.file_id}', file_name='{self.file_name}')>"

class CSVFile(db.Model):
    """
    Model for storing CSV file metadata for agentic processing.
    Supports both standalone CSV files and sheets extracted from Excel files.
    """
    __tablename__ = 'csv_files'
    
    id = db.Column(db.Integer, primary_key=True)
    csv_id = db.Column(db.String(255), nullable=False, unique=True)  # Unique identifier for CSV processing
    excel_id = db.Column(db.String(255), db.ForeignKey('excel_files.file_id'), nullable=True)  # Link to Excel file if applicable
    original_file_id = db.Column(db.Integer, db.ForeignKey('files.id'), nullable=True)  # Link to existing file system
    sheet_name = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(255), nullable=False)
    storage_path = db.Column(db.String(255), nullable=True)
    azure_url = db.Column(db.String(255), nullable=True)
    row_count = db.Column(db.Integer, nullable=False)
    column_count = db.Column(db.Integer, nullable=False)
    columns_metadata = db.Column(db.JSON, nullable=False)  # Column types, names, sample data
    semantic_summary = db.Column(db.Text, nullable=False)  # AI-generated summary of CSV content
    potential_analyses = db.Column(db.JSON, nullable=False)  # Suggested analysis types
    sample_row = db.Column(db.JSON, nullable=False)  # Sample data for quick preview
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    embeddings = db.relationship('CSVEmbedding', backref='csv_file', lazy=True, cascade='all, delete-orphan')
    original_file = db.relationship('File', backref='csv_files', lazy=True)
    
    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_csv_files_csv_id', 'csv_id'),
        db.Index('idx_csv_files_excel_id', 'excel_id'),
        db.Index('idx_csv_files_original_file_id', 'original_file_id'),
        db.Index('idx_csv_files_created_at', 'created_at'),
    )

    def __repr__(self):
        return f"<CSVFile(id={self.id}, csv_id='{self.csv_id}', sheet_name='{self.sheet_name}')>"

class CSVEmbedding(db.Model):
    """
    Model for storing CSV embedding metadata for vector search integration.
    Tracks embeddings generated for CSV data analysis.
    """
    __tablename__ = 'csv_embeddings'
    
    id = db.Column(db.Integer, primary_key=True)
    embedding_id = db.Column(db.String(255), nullable=False, unique=True)  # Unique identifier for embedding
    csv_id = db.Column(db.String(255), db.ForeignKey('csv_files.csv_id'), nullable=False)
    vector_store_id = db.Column(db.String(255), nullable=False)  # Reference to vector store
    model = db.Column(db.String(255), nullable=False)  # Embedding model used
    dimensions = db.Column(db.Integer, nullable=False)  # Embedding dimensions
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_csv_embeddings_embedding_id', 'embedding_id'),
        db.Index('idx_csv_embeddings_csv_id', 'csv_id'),
        db.Index('idx_csv_embeddings_vector_store_id', 'vector_store_id'),
    )

    def __repr__(self):
        return f"<CSVEmbedding(id={self.id}, embedding_id='{self.embedding_id}', csv_id='{self.csv_id}')>"

class UserNavigation(db.Model):
    """
    Model for storing user navigation preferences and settings.
    Similar to user_departments but as a proper model class for easier CRUD operations.
    """
    __tablename__ = 'user_navigation'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id', ondelete='CASCADE'), nullable=False)
    vertical_id = db.Column(db.Integer, db.ForeignKey('verticals.id', ondelete='CASCADE'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id', ondelete='CASCADE'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='user_navigations', lazy=True)
    vertical = db.relationship('Vertical', backref='user_navigations', lazy=True)
    department = db.relationship('Department', backref='user_navigations', lazy=True)
    
    # Indexes for better query performance
    __table_args__ = (
        db.Index('idx_user_navigation_user_id', 'user_id'),
        db.Index('idx_user_navigation_vertical_id', 'vertical_id'),
        db.Index('idx_user_navigation_department_id', 'department_id'),
        db.Index('idx_user_navigation_user_vertical', 'user_id', 'vertical_id'),
        db.UniqueConstraint('user_id', 'vertical_id', 'department_id', name='uq_user_navigation_combination'),
    )

    def __repr__(self):
        return f"<UserNavigation(id={self.id}, user_id={self.user_id}, vertical_id={self.vertical_id}, department_id={self.department_id})>"

    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'vertical_id': self.vertical_id,
            'department_id': self.department_id,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'vertical_name': self.vertical.name if self.vertical else None,
            'department_name': self.department.name if self.department else None
        }
    

# Database Chat Models
class ExternalDatabase(db.Model):
    __tablename__ = 'external_databases'
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(255), nullable=False)  # User-friendly name
    db_type = db.Column(db.String(50), nullable=False)  # mysql, postgresql, sqlserver, oracle, etc.
    host = db.Column(db.String(255), nullable=False)
    port = db.Column(db.Integer, nullable=False)
    database_name = db.Column(db.String(255), nullable=False)
    username = db.Column(db.String(255), nullable=False)
    password_encrypted = db.Column(db.Text, nullable=False)  # Encrypted password
    connection_string_template = db.Column(db.Text, nullable=True)  # Custom connection string if needed
    ssl_enabled = db.Column(db.Boolean, default=False)
    ssl_cert_path = db.Column(db.String(500), nullable=True)
    additional_params = db.Column(db.JSON, nullable=True)  # Additional connection parameters
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_tested_at = db.Column(db.DateTime, nullable=True)
    test_status = db.Column(db.String(50), default='pending')  # pending, success, failed
    test_error_message = db.Column(db.Text, nullable=True)

    # Relationships
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_databases', lazy=True)
    authorized_users = db.relationship(
        'User',
        secondary=user_database_permissions,
        primaryjoin='ExternalDatabase.id == user_database_permissions.c.external_database_id',
        secondaryjoin='User.id == user_database_permissions.c.user_id',
        backref='accessible_databases'
    )
    chat_sessions = db.relationship('DatabaseChatSession', backref='database', lazy=True)

class DatabaseChatSession(db.Model):
    __tablename__ = 'database_chat_sessions'
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    external_database_id = db.Column(db.Integer, db.ForeignKey('external_databases.id'), nullable=False)
    session_name = db.Column(db.String(255))
    is_connected = db.Column(db.Boolean, default=False)
    connection_established_at = db.Column(db.DateTime, nullable=True)
    connection_error = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    user = db.relationship('User', backref='database_chat_sessions', lazy=True)
    messages = db.relationship('DatabaseChatMessage', backref='session', lazy=True, order_by='DatabaseChatMessage.created_at')

class DatabaseChatMessage(db.Model):
    __tablename__ = 'database_chat_messages'
    id = db.Column(db.Integer, primary_key=True)
    session_id = db.Column(db.Integer, db.ForeignKey('database_chat_sessions.id'), nullable=False)
    question = db.Column(db.Text, nullable=False)
    answer = db.Column(db.Text, nullable=False)
    sql_queries = db.Column(db.JSON, nullable=True)  # Array of SQL queries executed
    query_results = db.Column(db.JSON, nullable=True)  # Results from SQL queries
    execution_plan = db.Column(db.JSON, nullable=True)  # Planner's execution plan
    qa_feedback = db.Column(db.JSON, nullable=True)  # QA component feedback
    token_usage = db.Column(db.JSON, nullable=True)  # AI token usage stats
    execution_time_ms = db.Column(db.Integer, nullable=True)  # Query execution time
    error_message = db.Column(db.Text, nullable=True)  # Any errors during execution
    
    # NEW VISUALIZATION FIELDS
    visualization_data = db.Column(db.JSON, nullable=True)  # Chart configuration and data
    visualization_type = db.Column(db.String(50), nullable=True)  # Type of chart (bar, line, pie, etc.)
    visualization_image = db.Column(db.Text, nullable=True)  # Base64 encoded chart image
    visualization_metadata = db.Column(db.JSON, nullable=True)  # Additional visualization info
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)