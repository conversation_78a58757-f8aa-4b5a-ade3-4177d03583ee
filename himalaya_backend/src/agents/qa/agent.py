"""
QA Agent for the Himalaya Azure system.

This agent evaluates responses and determines if they need improvement.
"""

import os
import json
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langgraph.graph import StateGraph, END

# Import configuration
import sys
#sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

class QAState(TypedDict):
    """State for the QA agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    last_qa_pair: Optional[Dict[str, str]]
    answer: str
    references: Dict[str, str]
    evaluation: Dict[str, Any]
    needs_improvement: bool
    improvement_count: int
    improvement_instructions: Optional[str]

class QAAgent:
    """
    QA agent that evaluates responses and determines if they need improvement.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True, max_improvement_loops=3):
        """
        Initialize the QA agent.

        Args:
            model: LLM to use for evaluation
            system_prompt: System prompt for the QA agent
            verbose: Whether to print verbose output
            max_improvement_loops: Maximum number of improvement loops
        """
        self.verbose = verbose
        self.max_improvement_loops = max_improvement_loops
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(QAState)

        # Add nodes
        graph.add_node("evaluate_answer", self.evaluate_answer)
        graph.add_node("determine_improvement", self.determine_improvement)

        # Add edges
        graph.add_edge("evaluate_answer", "determine_improvement")
        graph.add_edge("determine_improvement", END)

        # Set entry point
        graph.set_entry_point("evaluate_answer")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the QA agent.

        Returns:
            The default system prompt
        """
        return f"""You are a Quality Assurance agent that evaluates answers to user queries, with a CRITICAL focus on preventing hallucinations.

Your task is to:
1. Evaluate if the answer adequately addresses the user's query
2. Identify any missing information or areas that need improvement
3. Determine if the answer needs to be improved
4. MOST CRITICAL TASK: Verify that EVERY SINGLE PIECE of information in the answer is EXPLICITLY supported by the provided references

HALLUCINATION DETECTION - YOUR PRIMARY MISSION:
- STRICTLY verify that ALL claims, facts, and information in the answer are DIRECTLY supported by the provided references
- If the answer contains ANY information not found in the references, flag it as a hallucination
- EVEN COMMON KNOWLEDGE must be supported by references - do not allow ANY general knowledge claims
- If the answer discusses entities, companies, people, topics, or concepts not mentioned in the references, this is a hallucination
- If the references are about one topic but the answer discusses a different topic, this is a SEVERE hallucination
- If the answer claims to not have information but actually fabricates a response, this is a CRITICAL hallucination
- General statements like "X is a programming language" or "Y is widely used for Z" are hallucinations UNLESS explicitly stated in references

ENTERPRISE SAFETY GUIDELINES:
- This is an ENTERPRISE SYSTEM where hallucinations can cause SERIOUS BUSINESS IMPACT
- Users RELY on this system for ACCURATE information from their enterprise documents
- NEVER allow information that isn't explicitly in the references - this is a STRICT REQUIREMENT
- Even if a statement seems obviously true or common knowledge, it MUST be in the references
- When in doubt, always flag potential hallucinations - false positives are better than false negatives

SOURCE VERIFICATION:
- Check that EACH claim in the answer can be traced back to a SPECIFIC reference
- If the answer contains information that seems to come from general knowledge but isn't in references, FLAG IT
- If references are about one company/topic but the answer discusses a different company/topic, this is a severe hallucination
- If the query asks about X but references only contain information about Y, the answer should acknowledge the lack of information about X

MULTI-FILE COVERAGE EVALUATION:
- If the user's query mentions multiple files or requests information "separated by file names", check if ALL mentioned files are adequately covered
- Look for keywords like "separate", "separated", "by file names", "each file", "individual files", "document by document"
- If multiple files are referenced in the sources but the answer only covers some files, this is incomplete
- Generic sections like "Additional Context" without clear file attribution are problematic for multi-file queries
- Each file should have substantive coverage, not just brief mentions

IMPORTANT: Your response must be a valid JSON object with the following structure:
{{
    "evaluation": {{
        "completeness": 1-5 (1=very incomplete, 5=very complete),
        "relevance": 1-5 (1=not relevant, 5=highly relevant),
        "clarity": 1-5 (1=unclear, 5=very clear),
        "source_adherence": 1-5 (1=severe hallucinations, 5=perfectly sourced)
    }},
    "hallucinations_detected": ["list specific hallucinations or unsupported claims found"],
    "missing_information": ["list of specific missing information"],
    "needs_improvement": true/false,
    "improvement_instructions": "specific instructions for improvement (only if needs_improvement is true)"
}}

SCORING GUIDELINES:
- source_adherence score of 1: Major hallucinations present, answer contains significant information not in references
- source_adherence score of 2: Several unsupported claims or general knowledge statements not in references
- source_adherence score of 3: Some minor unsupported claims, but mostly accurate
- source_adherence score of 4: Very few minor unsupported details, almost all information directly supported
- source_adherence score of 5: Every single piece of information is directly supported by references

If ANY hallucinations are detected, source_adherence MUST be 2 or lower and needs_improvement MUST be true.
If more than 3 hallucinations are detected, source_adherence MUST be 1.

Today is {self.today_date}. Remember that even if information seems current or factual based on your training data, it MUST be verified against the provided references.
"""

    def evaluate_answer(self, state: QAState) -> QAState:
        """
        Evaluate the answer for quality and completeness.

        Args:
            state: The current state

        Returns:
            Updated state with evaluation
        """
        if self.verbose:
            print("\n=== Starting evaluate_answer node ===")
        
        # Extract query and answer from state
        query = state["query"]
        answer = state["answer"]
        references = state["references"]
        
        # ENTITY CHECKING REMOVED: Previously this code extracted entities from the answer 
        # and checked if each entity appeared in references, causing 95+ false positive 
        # hallucinations. LLM-based evaluation is more appropriate for detecting actual 
        # hallucinations vs. legitimate summarization.
        
        # Build system prompt with enhanced hallucination detection guidance
        messages = [
            SystemMessage(content=self.system_prompt or self._get_default_system_prompt()),
            HumanMessage(content=f"""
            Query: {query}
            
            Answer to evaluate:
            {answer}
            
            References used:
            {json.dumps(references, indent=2)}
            
            Note: Relying on LLM-based evaluation for hallucination detection rather than rigid entity matching.
            
            Please evaluate this answer based on:
            - Completeness: Does it fully address the query?
            - Relevance: Is it directly relevant to what was asked?
            - Clarity: Is it clear and well-structured?
            - Source adherence: Does it ONLY contain information from the references?
            
            CRITICAL INSTRUCTIONS FOR HALLUCINATION DETECTION:
            - Be EXTREMELY strict about hallucinations
            - ANY claim not EXPLICITLY supported by the references is a hallucination
            - General knowledge statements like "Python is a programming language" are hallucinations UNLESS explicitly stated in references
            - Do NOT allow ANY information beyond what's in the references, even if it seems like common knowledge
            - If the references don't mention specific features, applications, or characteristics, the answer MUST NOT include them
            - Mark ANY examples, statistics, or applications not found in references as hallucinations
            - If the answer contains ANY information not in references, source_adherence MUST be 2 or lower
            - If more than 3 hallucinations are detected, source_adherence MUST be 1
            
            Pay special attention to:
            - Specific examples not found in references
            - Statistics or numbers not mentioned in references
            - Generalizations that go beyond what the references state
            - Claims about popularity, effectiveness, or common usage not supported by references
            - Are all aspects of their query addressed?
            - Is all information supported by references?
            """)
        ]

        # Get evaluation from model
        response = self.model.invoke(messages)

        if self.verbose:
            print("Raw response from model:", response.content)

        try:
            # Clean the response content if it contains markdown code blocks
            content = response.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()

            evaluation = json.loads(content)

            if self.verbose:
                print("Parsed evaluation:", json.dumps(evaluation, indent=2))

            # Ensure all required fields are present
            if "evaluation" not in evaluation:
                evaluation["evaluation"] = {
                    "completeness": 3,
                    "relevance": 3,
                    "clarity": 3,
                    "source_adherence": 3
                }
            elif "source_adherence" not in evaluation["evaluation"]:
                evaluation["evaluation"]["source_adherence"] = 3

            if "hallucinations_detected" not in evaluation:
                evaluation["hallucinations_detected"] = []
                
            # ENTITY CHECKING REMOVED: No longer adding pre-detected potential hallucinations
            # as this was causing 95+ false positives. LLM evaluation is more appropriate.

            if "missing_information" not in evaluation:
                evaluation["missing_information"] = []

            # STRICTER ENFORCEMENT: Force needs_improvement to true if ANY hallucinations are detected
            # or source_adherence is below 4
            if evaluation.get("hallucinations_detected"):
                evaluation["needs_improvement"] = True
                # Force source_adherence to be lower if hallucinations are detected
                if len(evaluation["hallucinations_detected"]) > 3:
                    evaluation["evaluation"]["source_adherence"] = 1
                elif len(evaluation["hallucinations_detected"]) > 0:
                    evaluation["evaluation"]["source_adherence"] = min(evaluation["evaluation"].get("source_adherence", 3), 2)
                
                # Generate detailed improvement instructions
                hallucinations_list = "\n".join([f"- {h}" for h in evaluation.get("hallucinations_detected", [])])
                
                if hallucinations_list:
                    evaluation["improvement_instructions"] = f"""Remove the following hallucinated content:
{hallucinations_list}

STRICT INSTRUCTION: Ensure ALL information is DIRECTLY supported by the provided references. Do not include ANY information, examples, or claims that aren't EXPLICITLY mentioned in the references. If the references don't provide enough information to fully answer the query, acknowledge this limitation rather than filling in gaps with unsupported information."""
                else:
                    evaluation["improvement_instructions"] = "STRICT INSTRUCTION: Ensure ALL information is DIRECTLY supported by the provided references. Remove any claims or examples not explicitly found in the references."
                    
            elif "needs_improvement" not in evaluation:
                evaluation["needs_improvement"] = False

            if "improvement_instructions" not in evaluation and evaluation["needs_improvement"]:
                evaluation["improvement_instructions"] = "Please provide more detailed information that is directly supported by the references."

        except (json.JSONDecodeError, KeyError) as e:
            if self.verbose:
                print(f"Error parsing response: {e}")

            # Default evaluation if parsing fails
            evaluation = {
                "evaluation": {
                    "completeness": 3,
                    "relevance": 3,
                    "clarity": 3,
                    "source_adherence": 3
                },
                "hallucinations_detected": [],
                "missing_information": [],
                "needs_improvement": False,
                "improvement_instructions": None
            }

        # Update state
        return {
            **state,
            "evaluation": evaluation
        }
        
    # REMOVED: _extract_key_entities method was causing 95+ false positive hallucinations
    # by flagging every capitalized word as a potential entity. LLM-based evaluation
    # is more appropriate for detecting actual hallucinations vs. legitimate summarization.

    def determine_improvement(self, state: QAState) -> QAState:
        """
        Determine if the answer needs improvement based on the evaluation.

        Args:
            state: The current state

        Returns:
            Updated state with improvement decision
        """
        if self.verbose:
            print("\n=== Starting determine_improvement node ===")

        evaluation = state["evaluation"]

        # Extract evaluation metrics
        source_adherence = evaluation.get("evaluation", {}).get("source_adherence", 3)
        hallucinations = evaluation.get("hallucinations_detected", [])
        
        # Log hallucination detection results
        if self.verbose:
            if hallucinations:
                print(f"⚠️ HALLUCINATION ALERT: {len(hallucinations)} hallucinations detected")
                for i, h in enumerate(hallucinations):
                    print(f"  {i+1}. {h}")
            else:
                print("✅ No hallucinations detected")
                
            print(f"Source adherence score: {source_adherence}/5")

        # Extract needs_improvement and improvement_instructions
        needs_improvement = evaluation.get("needs_improvement", False)
        
        # Override based on hallucination detection
        if hallucinations or source_adherence < 4:
            needs_improvement = True
            
        improvement_instructions = evaluation.get("improvement_instructions") if needs_improvement else None

        # If we've reached the maximum number of improvement loops, we need to handle hallucinations differently
        if state['improvement_count'] >= self.max_improvement_loops:
            if hallucinations or source_adherence < 3:
                # CRITICAL CHANGE: Instead of allowing hallucinated content after max loops,
                # force a complete rewrite with a strict "no information" fallback
                print(f"⚠️ WARNING: Answer still contains hallucinations after {self.max_improvement_loops} improvement attempts")
                print(f"⚠️ Implementing STRICT FALLBACK MODE - will provide minimal answer with no hallucinations")
                
                # Create specific instructions for a minimal, strictly reference-based answer
                improvement_instructions = """STRICT FALLBACK MODE ACTIVATED: 
                
1. COMPLETELY DISCARD your current answer.
2. Provide ONLY this response:
"Based on the available references, I cannot provide reliable information about this topic. The documents I have access to do not contain sufficient details about [TOPIC]. To get accurate information about [TOPIC], I recommend consulting specialized resources on this subject."

3. Replace [TOPIC] with the specific topic of the query.
4. DO NOT add ANY additional information, examples, or claims beyond this template.
5. DO NOT attempt to answer the query with any information not explicitly present in the references.
6. This is a CRITICAL SAFETY REQUIREMENT to prevent misinformation."""
                
                needs_improvement = True
            else:
                # If no hallucinations but other quality issues, we can stop improvements
                needs_improvement = False
                improvement_instructions = None
                
            if self.verbose:
                print(f"Reached maximum improvement loops ({self.max_improvement_loops})")

        if self.verbose:
            print(f"Needs improvement: {needs_improvement}")
            if needs_improvement:
                print(f"Improvement instructions: {improvement_instructions}")

        # Update state
        return {
            **state,
            "needs_improvement": needs_improvement,
            "improvement_instructions": improvement_instructions
        }

def create_qa_agent(model=None, system_prompt=None, verbose=True, max_improvement_loops=3) -> QAAgent:
    """
    Create a QA agent.

    Args:
        model: LLM to use for evaluation
        system_prompt: System prompt for the QA agent
        verbose: Whether to print verbose output
        max_improvement_loops: Maximum number of improvement loops

    Returns:
        A QA agent
    """
    return QAAgent(
        model=model,
        system_prompt=system_prompt,
        verbose=verbose,
        max_improvement_loops=max_improvement_loops
    )
