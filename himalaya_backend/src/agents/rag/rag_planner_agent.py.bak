"""
RAG Planner Agent for the Enhanced Himalaya system.

This agent analyzes retrieved documents and decides which specialized agents to call.
Adapted from Himalaya Azure system for Azure Search integration.
"""

import os
import json
import logging
import numpy as np
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_DEPLOYMENT_NAME
)

# Import CSV agent
from agents.csv.excel_langchain_rag import ExcelLangChainRAG

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Custom JSON encoder to handle NumPy types
class NumpyJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        return super(NumpyJSONEncoder, self).default(obj)

class RAGPlannerState(TypedDict):
    """State for the RAG Planner agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    standalone_query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    csv_documents: List[Dict[str, Any]]
    text_documents: List[Dict[str, Any]]
    pdf_documents: List[Dict[str, Any]]
    needs_csv_processing: bool
    needs_pdf_processing: bool
    processed_csv_documents: List[Dict[str, Any]]
    remaining_csv_documents: List[Dict[str, Any]]
    csv_processing_results: List[Dict[str, Any]]
    pdf_processing_result: Optional[Dict[str, Any]]
    current_csv_result: Optional[Dict[str, Any]]
    text_processing_result: Optional[Dict[str, Any]]
    final_answer: Optional[str]
    references: Optional[List[str]]
    qa_feedback: Optional[Dict[str, Any]]
    needs_additional_csv: bool

class RAGPlannerAgent:
    """
    RAG Planner Agent that analyzes retrieved documents and decides which specialized agents to call.
    """

    def __init__(self, data_dir="data", model=None, system_prompt=None, verbose=True):
        """
        Initialize the RAG Planner agent.

        Args:
            data_dir: Directory where data is stored
            model: LLM model to use
            system_prompt: System prompt for the agent
            verbose: Whether to print verbose output
        """
        self.data_dir = data_dir
        self.verbose = verbose

        # Initialize the LLM
        if model:
            self.llm = model
        else:
            self.llm = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Initialize the Excel RAG agent
        self.excel_rag = ExcelLangChainRAG(data_dir=data_dir)
        
        # Initialize the PDF agent (will be imported when needed)
        self.pdf_agent = None
        
        # Initialize the RAG QA agent (will be imported when needed)
        self.rag_qa_agent = None

        # Create the graph
        graph = StateGraph(RAGPlannerState)

        # Add nodes
        graph.add_node("analyze_documents", self.analyze_documents)
        graph.add_node("process_pdf", self.process_pdf)
        graph.add_node("process_csv", self.process_csv)
        graph.add_node("evaluate_csv_result", self.evaluate_csv_result)
        graph.add_node("process_text", self.process_text)
        graph.add_node("generate_final_answer", self.generate_final_answer)

        # Add conditional edges
        graph.add_conditional_edges(
            "analyze_documents",
            self.route_after_analysis,
            {
                "process_pdf": "process_pdf",
                "process_csv": "process_csv",
                "process_text": "process_text"
            }
        )
        
        graph.add_conditional_edges(
            "evaluate_csv_result",
            self.route_after_evaluation,
            {
                "process_more_csv": "process_csv",
                "process_text": "process_text"
            }
        )
        
        graph.add_conditional_edges(
            "process_pdf",
            self.route_after_pdf,
            {
                "process_csv": "process_csv",
                "process_text": "process_text"
            }
        )
        graph.add_edge("process_csv", "evaluate_csv_result")
        graph.add_edge("process_text", "generate_final_answer")
        graph.add_edge("generate_final_answer", END)

        # Set entry point
        graph.set_entry_point("analyze_documents")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the agent.

        Returns:
            Default system prompt
        """
        return """You are a RAG Planner Agent that analyzes retrieved documents and decides which specialized agents to call.
Your job is to analyze the user's query and the retrieved documents to decide the best course of action.

You will:
1. Analyze the retrieved documents (both text and CSV/Excel)
2. Formulate queries for CSV processing
3. Process CSV data incrementally, starting with the most relevant
4. Process text documents
5. Generate a final answer

Always provide clear, concise information about your reasoning and decisions.
"""

    def _initialize_pdf_agent(self):
        """Initialize the PDF agent if not already done."""
        if self.pdf_agent is None:
            try:
                from agents.pdf.pdf_table_processor import create_pdf_agent
                self.pdf_agent = create_pdf_agent(data_dir=self.data_dir, verbose=self.verbose)
                if self.verbose:
                    print("PDF agent initialized successfully")
            except ImportError as e:
                if self.verbose:
                    print(f"PDF agent not available: {e}")
                self.pdf_agent = None

    def _normalize_document_score(self, document: Dict[str, Any]) -> Dict[str, Any]:
        """
        Ensure document has a proper score field for agent analysis.
        
        Args:
            document: Document to normalize
            
        Returns:
            Document with normalized score
        """
        # Make a copy to avoid modifying the original
        normalized_doc = document.copy()
        
        # If score is missing or 0, try to get it from metadata
        if not normalized_doc.get('score', 0):
            metadata = normalized_doc.get('metadata', {})
            search_score = metadata.get('search_score', 0)
            if search_score:
                normalized_doc['score'] = search_score
                if self.verbose:
                    print(f"    → Normalized score from metadata: {search_score}")
        
        return normalized_doc

    def _classify_documents_by_type(self, documents: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Classify documents by their type (PDF, CSV, text) and normalize scores.
        
        Args:
            documents: List of documents to classify
            
        Returns:
            Dictionary with classified documents
        """
        classified = {
            'pdf_documents': [],
            'csv_documents': [],
            'text_documents': []
        }
        
        for i, doc in enumerate(documents):
            # Normalize document score first
            normalized_doc = self._normalize_document_score(doc)
            
            # Debug logging
            if self.verbose:
                doc_keys = list(normalized_doc.keys()) if isinstance(normalized_doc, dict) else 'Not a dict'
                source_type = normalized_doc.get('source_type', 'None') if isinstance(normalized_doc, dict) else 'N/A'
                file_id = normalized_doc.get('file_id', 'None') if isinstance(normalized_doc, dict) else 'N/A'
                score = normalized_doc.get('score', 0)
                print(f"  Document {i+1}: keys={doc_keys}, source_type={source_type}, file_id={file_id}, score={score}")
            
            # Check if it's an enhanced document from chat_routes (has file_id field)
            if 'file_id' in normalized_doc and 'source_type' in normalized_doc:
                # This is an enhanced document from chat_routes classification
                source_type = normalized_doc.get('source_type', '')
                
                if source_type == 'pdf_table':
                    # PDF table - treat as both PDF and CSV for processing
                    classified['pdf_documents'].append(normalized_doc)
                    classified['csv_documents'].append(normalized_doc)
                    if self.verbose:
                        print(f"    → Classified as PDF table (added to both PDF and CSV)")
                elif source_type == 'csv_excel':
                    # CSV/Excel file
                    classified['csv_documents'].append(normalized_doc)
                    if self.verbose:
                        print(f"    → Classified as CSV/Excel file")
                else:
                    # Unknown enhanced document type, treat as text
                    classified['text_documents'].append(normalized_doc)
                    if self.verbose:
                        print(f"    → Classified as text (unknown enhanced type: {source_type})")
            else:
                # This is a raw search result document
                # Check if it's a CSV document using file extension
                if self._is_csv_document(normalized_doc):
                    classified['csv_documents'].append(normalized_doc)
                    if self.verbose:
                        print(f"    → Classified as CSV (raw document)")
                else:
                    classified['text_documents'].append(normalized_doc)
                    if self.verbose:
                        print(f"    → Classified as text (raw document)")
        
        return classified

    def _is_csv_document(self, document: Dict[str, Any]) -> bool:
        """
        Check if a document is a CSV/Excel document.
        
        Args:
            document: Document to check
            
        Returns:
            True if it's a CSV/Excel document
        """
        # Check file name
        file_name = document.get('file_name', '')
        if file_name.endswith(('.csv', '.xlsx', '.xls')):
            return True
        
        # Check metadata
        metadata = document.get('metadata', {})
        storage_name = metadata.get('metadata_storage_name', '')
        if storage_name.endswith(('.csv', '.xlsx', '.xls')):
            return True
        
        return False

    def analyze_documents(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Analyze the retrieved documents to determine next steps.

        Args:
            state: The current state

        Returns:
            Updated state with analysis
        """
        if self.verbose:
            print("\n=== Starting analyze_documents node ===")
            print(f"Query: {state['query']}")
            print(f"CSV documents: {len(state['csv_documents'])}")
            print(f"Text documents: {len(state['text_documents'])}")

        # Classify all documents by type
        all_documents = state["csv_documents"] + state["text_documents"]
        
        if self.verbose:
            print(f"Input documents to classify:")
            print(f"  CSV documents from state: {len(state['csv_documents'])}")
            print(f"  Text documents from state: {len(state['text_documents'])}")
            print(f"  Total documents to classify: {len(all_documents)}")
        
        classified_docs = self._classify_documents_by_type(all_documents)
        
        # Check what types of documents we have
        has_pdf = len(classified_docs["pdf_documents"]) > 0
        has_csv = len(classified_docs["csv_documents"]) > 0
        has_text = len(classified_docs["text_documents"]) > 0
        
        if self.verbose:
            print(f"Document classification:")
            print(f"  PDF documents (with tables): {len(classified_docs['pdf_documents'])}")
            print(f"  CSV/Excel documents: {len(classified_docs['csv_documents'])}")
            print(f"  Text documents: {len(classified_docs['text_documents'])}")

        # Add system message with conversation context
        context_info = ""
        if state.get('conversation_history'):
            context_info = f"\n\nConversation Context:\n{state['conversation_history']}"
        
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"I'm analyzing documents for the query: {state['standalone_query']}{context_info}")
        ]
        
        # Sort CSV documents by relevance score
        if has_csv:
            # Debug log the document format
            if self.verbose and state["csv_documents"]:
                sample_doc = state["csv_documents"][0]
                try:
                    print(f"CSV document structure sample: {json.dumps(sample_doc, indent=2, cls=NumpyJSONEncoder)}")
                except Exception as e:
                    print(f"Error serializing CSV document: {str(e)}")
                    print(f"CSV document keys: {list(sample_doc.keys()) if isinstance(sample_doc, dict) else 'Not a dict'}")

            # Sort CSV documents by score (highest first)
            sorted_csv_docs = sorted(
                state["csv_documents"], 
                key=lambda x: float(x.get('score', 0)), 
                reverse=True
            )
            
            if self.verbose:
                print("CSV documents sorted by relevance:")
                for i, doc in enumerate(sorted_csv_docs[:3]):  # Show top 3
                    score = doc.get('score', 0)
                    metadata = doc.get('metadata', {})
                    storage_name = metadata.get('metadata_storage_name', 'Unknown')
                    print(f"  {i+1}. {storage_name}: score={score}")

        else:
            sorted_csv_docs = []

        # Determine if CSV processing is needed based on content analysis and relevance
        needs_csv_processing = has_csv and self._analyze_csv_intent(
            state['standalone_query'], 
            classified_docs["csv_documents"],
            state.get("conversation_history"),
            state.get("conversation_summary")
        )
        
        # Determine if PDF processing is needed
        needs_pdf_processing = has_pdf
        
        if self.verbose:
            print(f"Needs CSV processing: {needs_csv_processing}")
            print(f"Needs PDF processing: {needs_pdf_processing}")
            print(f"Has text documents: {has_text}")

        return {
            **state,
            "messages": messages,
            "csv_documents": classified_docs["csv_documents"],
            "pdf_documents": classified_docs["pdf_documents"],
            "text_documents": classified_docs["text_documents"],
            "needs_csv_processing": needs_csv_processing,
            "needs_pdf_processing": needs_pdf_processing,
            "remaining_csv_documents": classified_docs["csv_documents"] if needs_csv_processing else [],
            "processed_csv_documents": [],
            "csv_processing_results": [],
            "pdf_processing_result": None,
            "needs_additional_csv": False
        }

    def _analyze_csv_intent(self, query: str, csv_documents: List[Dict[str, Any]], 
                           conversation_history: str = None, conversation_summary: str = None) -> bool:
        """
        Intelligently analyze if the query requires CSV/Excel processing using comprehensive context analysis.
        Considers query intent, document relevance, conversation context, and user patterns.
        
        Args:
            query: The user query
            csv_documents: List of CSV documents with relevance scores
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation
            
        Returns:
            True if CSV analysis is needed based on comprehensive analysis
        """
        if not csv_documents:
            return False
        
        # Use LLM to analyze query intent and document relevance
        try:
            # Get document context for analysis (consider all available docs, not just high-scoring)
            csv_context = "Available CSV/Excel documents:\n"
            for i, doc in enumerate(csv_documents[:5]):  # Analyze top 5 most relevant
                content = doc.get('content', doc.get('semantic_summary', ''))[:200]
                score = doc.get('score', 0)
                csv_context += f"{i+1}. Score: {score:.3f} - {content}...\n"
            
            # Include comprehensive conversation context
            conversation_context = ""
            if conversation_history:
                conversation_context += f"\n\nConversation History:\n{conversation_history[-500:]}\n"
            if conversation_summary:
                conversation_context += f"\n\nConversation Summary:\n{conversation_summary}\n"
            
            intent_analysis_prompt = f"""
            Analyze the user query to determine if it requires structured data/CSV processing or general document analysis.

            User Query: "{query}"{conversation_context}

            {csv_context}

            Determine the query intent:

            **CSV PROCESSING NEEDED** if the query:
            - Seeks specific numerical data, calculations, or statistical information
            - Asks for data comparisons, rankings, or aggregations  
            - Requests information that appears available in the CSV documents
            - Needs structured data analysis rather than narrative understanding
            - Requires precise data extraction or mathematical operations
            - Is a follow-up question relating to previously discussed tabular data

            **DOCUMENT PROCESSING SUFFICIENT** if the query:
            - Asks for document summary, overview, or general understanding
            - Seeks conceptual explanations or contextual information
            - Wants narrative content rather than structured data
            - Requests general information that doesn't require data analysis
            - Can be answered from text content without tabular analysis
            
            **IMPORTANT**: Document processing can now use content from CSV documents when needed,
            so choose this option for overview/summary queries even if the content comes from CSV sources.

            Consider:
            1. The user's actual information need and intent
            2. Whether the available CSV documents contain relevant information
            3. The relevance scores of the CSV documents (higher scores = more relevant)
            4. The conversation context and any previous data discussions
            5. Whether the query can be answered without structured data analysis

            Respond with only "CSV" if structured data processing is needed, or "DOCUMENT" if general document analysis is sufficient.
            """
            
            messages = [
                SystemMessage(content="You are an expert at analyzing user query intent for document processing. Consider the user's actual information need, not just keywords. You must respond with only 'CSV' or 'DOCUMENT'."),
                HumanMessage(content=intent_analysis_prompt)
            ]
            
            response = self.llm.invoke(messages)
            decision = response.content.strip().upper()
            
            needs_csv = decision == "CSV"
            
            if self.verbose:
                print(f"🧠 RAG PLANNER: LLM Intent Analysis - Query: '{query[:50]}...'")
                print(f"🧠 RAG PLANNER: Available CSV docs: {len(csv_documents)}")
                print(f"🧠 RAG PLANNER: Intent Result: {decision} -> {'CSV Processing' if needs_csv else 'Document Processing'}")
                if csv_documents:
                    top_scores = [doc.get('score', 0) for doc in csv_documents[:3]]
                    print(f"🧠 RAG PLANNER: Top CSV scores: {top_scores}")
            
            return needs_csv
            
        except Exception as e:
            logger.error(f"Error in LLM intent analysis: {str(e)}")

            # SAFETY FIX: When LLM intent analysis fails, be conservative
            if self.verbose:
                print(f"🔄 RAG PLANNER: Intent analysis failed - defaulting to safe document processing")
                print(f"🔄 RAG PLANNER: Cannot determine intent safely, avoiding CSV processing to prevent data mixing")

            # Always default to document processing when intent analysis fails
            # This prevents potential data mixing from CSV fallbacks
            return False

    def process_pdf(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Process PDF documents using the PDF agent.

        Args:
            state: The current state

        Returns:
            Updated state with PDF processing results
        """
        if self.verbose:
            print("\n=== Starting process_pdf node ===")
        
        # Initialize PDF agent if needed
        self._initialize_pdf_agent()
        
        if not self.pdf_agent:
            if self.verbose:
                print("PDF agent not available, skipping PDF processing")
            return {
                **state,
                "pdf_processing_result": {
                    "answer": "PDF processing not available",
                    "status": "error",
                    "sources": [],
                    "metadata": {"agent": "pdf", "status": "not_available"}
                }
            }
        
        try:
            # Process PDF documents with full context
            pdf_result = self.pdf_agent.process_pdf_query(
                query=state["standalone_query"],
                pdf_documents=state["pdf_documents"],
                conversation_history=state.get("conversation_history"),
                conversation_summary=state.get("conversation_summary"),
                user_context={
                    "original_query": state["query"],
                    "has_csv_documents": len(state["csv_documents"]) > 0,
                    "has_text_documents": len(state["text_documents"]) > 0
                }
            )
            
            if self.verbose:
                print(f"PDF processing completed with status: {pdf_result.get('metadata', {}).get('status', 'unknown')}")
            
            return {
                **state,
                "pdf_processing_result": pdf_result
            }
            
        except Exception as e:
            logger.error(f"Error in PDF processing: {str(e)}")
            return {
                **state,
                "pdf_processing_result": {
                    "answer": f"Error processing PDF documents: {str(e)}",
                    "status": "error",
                    "sources": [],
                    "metadata": {"agent": "pdf", "status": "error", "error": str(e)}
                }
            }

    def route_after_pdf(self, state: RAGPlannerState) -> str:
        """
        Determine the next step after PDF processing, respecting PDF Agent's intelligent decision.

        Args:
            state: The current state

        Returns:
            Next step to take
        """
        # Check PDF Agent's intelligent decision
        pdf_result = state.get("pdf_processing_result", {})
        pdf_metadata = pdf_result.get("metadata", {})
        pdf_processing_type = pdf_metadata.get("processing_type", "").upper()
        
        # If PDF Agent made an intelligent decision for DOCUMENT processing,
        # respect that decision and don't force CSV processing
        if pdf_processing_type == "DOCUMENT":
            if self.verbose:
                print("🧠 RAG PLANNER: Respecting PDF Agent's DOCUMENT decision - skipping CSV processing")
                print("PDF processing complete, routing to text processing")
            return "process_text"
        
        # If PDF Agent chose TABLE processing or no explicit decision,
        # proceed with original CSV processing logic
        if state["needs_csv_processing"] and len(state["remaining_csv_documents"]) > 0:
            if self.verbose:
                print(f"PDF processing complete, routing to CSV processing for {len(state['remaining_csv_documents'])} documents")
            return "process_csv"
        else:
            if self.verbose:
                print("PDF processing complete, routing to text processing")
            return "process_text"

    def route_after_analysis(self, state: RAGPlannerState) -> str:
        """
        Determine the next step after analyzing documents.

        Args:
            state: The current state

        Returns:
            Next step to take
        """
        # Enhanced routing: Handle both PDF and CSV processing when both are needed
        
        # Priority 1: PDF processing first (since it may contain tabular data that also needs CSV processing)
        if state["needs_pdf_processing"]:
            return "process_pdf"
        
        # Priority 2: CSV processing (including PDF tables that need table analysis)
        elif state["needs_csv_processing"] and len(state["remaining_csv_documents"]) > 0:
            return "process_csv"
        
        # Priority 3: Text processing as fallback
        else:
            return "process_text"

    def process_csv(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Process CSV documents using the Excel RAG agent.

        Args:
            state: The current state

        Returns:
            Updated state with CSV processing results
        """
        if self.verbose:
            print("\n=== Starting process_csv node ===")
            
        # Get the most relevant remaining CSV document
        if not state["remaining_csv_documents"]:
            if self.verbose:
                print("No remaining CSV documents to process")
                
            return {
                **state,
                "current_csv_result": None,
                "needs_additional_csv": False
            }
            
        csv_document = state["remaining_csv_documents"][0]
        remaining_csv_documents = state["remaining_csv_documents"][1:]
        processed_csv_documents = state["processed_csv_documents"] + [csv_document]
        
        # Extract CSV information from Azure Search document
        metadata = csv_document.get('metadata', {})
        storage_name = metadata.get('metadata_storage_name', '')
        
        # For Azure Search integration, we need to map back to actual file
        # The storage name should correspond to the blob name
        csv_path = self._resolve_csv_path_from_storage_name(storage_name)
        
        if not csv_path:
            logger.warning(f"Could not resolve CSV path for storage name: {storage_name}")
            if self.verbose:
                print(f"Skipping CSV document - could not resolve path for: {storage_name}")
                print("This could happen if:")
                print("  1. PDF table document exists but CSV hasn't been generated yet")
                print("  2. File record not found in database")
                print("  3. CSV file was deleted or moved")
                
            return {
                **state,
                "processed_csv_documents": processed_csv_documents,
                "remaining_csv_documents": remaining_csv_documents,
                "current_csv_result": None,
                "needs_additional_csv": len(remaining_csv_documents) > 0
            }
        
        csv_name = os.path.basename(csv_path)
        csv_id = os.path.splitext(csv_name)[0]
        
        # Get score
        relevance_score = float(csv_document.get('score', 0))
        
        if self.verbose:
            print(f"Processing CSV: {csv_id}, Path: {csv_path}, Relevance Score: {relevance_score}")
            
        # Process the CSV with conversation context
        try:
            # Prepare enhanced query with conversation context for CSV processing
            enhanced_csv_query = state["standalone_query"]
            if state.get("conversation_history"):
                enhanced_csv_query = f"Context: {state['conversation_history']}\n\nCurrent Query: {state['standalone_query']}"
            
            csv_result = self.excel_rag.process_csv_directly(
                csv_path=csv_path,
                query=enhanced_csv_query,
                csv_id=csv_id
            )
            
            # Add score to the result for reference
            csv_result['relevance_score'] = relevance_score
            
        except Exception as e:
            logger.error(f"Error processing CSV {csv_id}: {str(e)}")
            csv_result = {
                'csv_id': csv_id,
                'csv_path': csv_path,
                'answer': f"Error processing CSV file: {str(e)}",
                'relevance_score': relevance_score,
                'error': True
            }
        
        # Add to processing results
        csv_processing_results = state["csv_processing_results"] + [csv_result]
        
        # Add assistant message with CSV processing result including conversation context
        context_note = ""
        if state.get("conversation_history"):
            context_note = f"\nConversation Context: {state['conversation_history'][:200]}..."
        
        messages = state["messages"] + [
            self.llm.invoke([
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=f"""
                I've processed the CSV file: {csv_id}
                Relevance Score: {relevance_score}{context_note}
                
                Result:
                {csv_result.get('answer', 'No answer generated')}
                """)
            ])
        ]
        
        if self.verbose:
            print(f"CSV processing result: {csv_result.get('answer', '')[:100]}...")
            print(f"Remaining CSV files: {len(remaining_csv_documents)}")
            
        return {
            **state,
            "messages": messages,
            "processed_csv_documents": processed_csv_documents,
            "remaining_csv_documents": remaining_csv_documents,
            "csv_processing_results": csv_processing_results,
            "current_csv_result": csv_result
        }

    def _resolve_csv_path_from_storage_name(self, storage_name: str) -> Optional[str]:
        """
        Resolve the actual CSV file path from Azure Search storage name.
        For PDF table documents, this returns the generated CSV file URL.
        For native CSV/Excel files, this returns the original file URL.
        
        Args:
            storage_name: The metadata_storage_name from Azure Search
            
        Returns:
            Actual CSV file path or None if not found
        """
        # Import here to avoid circular imports
        from models.models import File, CSVFile
        
        try:
            # Find the file by blob URL containing the storage name
            file_record = File.query.filter(File.blob_url.contains(storage_name)).first()
            
            if not file_record:
                logger.warning(f"No file record found for storage name: {storage_name}")
                return None
            
            # Check if this is a PDF file (indicating it's a PDF table document)
            if file_record.file_name.lower().endswith('.pdf'):
                # This is a PDF table document - look for the generated CSV file
                csv_file = CSVFile.query.filter_by(original_file_id=file_record.id).first()
                
                if csv_file and csv_file.azure_url:
                    if self.verbose:
                        print(f"Found generated CSV for PDF {file_record.file_name}: {csv_file.azure_url}")
                    return csv_file.azure_url
                else:
                    logger.warning(f"No generated CSV found for PDF file {file_record.file_name} (ID: {file_record.id})")
                    return None
            else:
                # This is a native CSV/Excel file - return the original blob URL
                if self.verbose:
                    print(f"Using original file URL for {file_record.file_name}: {file_record.blob_url}")
                return file_record.blob_url
                
        except Exception as e:
            logger.error(f"Error resolving CSV path for {storage_name}: {str(e)}")
            return None

    def evaluate_csv_result(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Evaluate the CSV processing result using the RAG QA agent.

        Args:
            state: The current state

        Returns:
            Updated state with evaluation
        """
        if self.verbose:
            print("\n=== Starting evaluate_csv_result node ===")
            
        # Get the most recent CSV result
        current_csv_result = state["current_csv_result"]
        
        if not current_csv_result:
            if self.verbose:
                print("No current CSV result to evaluate")
                
            return {
                **state,
                "needs_additional_csv": False,
                "qa_feedback": None
            }
            
        # Initialize RAG QA agent if not already done
        if self.rag_qa_agent is None:
            from agents.rag.rag_qa_agent import create_rag_qa_agent
            self.rag_qa_agent = create_rag_qa_agent(verbose=self.verbose)
            
        # Get CSV details
        csv_id = current_csv_result.get('csv_id', 'Unknown')
        csv_path = current_csv_result.get('csv_path', '')
        answer = current_csv_result.get('answer', '')
        
        # Convert remaining CSV documents to the format expected by the QA agent
        other_relevant_csvs = []
        for csv_doc in state["remaining_csv_documents"]:
            metadata = csv_doc.get('metadata', {})
            storage_name = metadata.get('metadata_storage_name', '')
            csv_path_other = self._resolve_csv_path_from_storage_name(storage_name)
            
            if csv_path_other:
                csv_name_other = os.path.basename(csv_path_other)
                csv_id_other = os.path.splitext(csv_name_other)[0]
                
                # Get score directly - convert possible NumPy float to Python float
                relevance_score = float(csv_doc.get('score', 0))
                
                other_relevant_csvs.append({
                    "csv_id": csv_id_other,
                    "csv_path": csv_path_other,
                    "relevance_score": relevance_score
                })
                
        # Log the other relevant CSVs
        if self.verbose and other_relevant_csvs:
            print("Other relevant CSV files (in order of processing):")
            for i, csv_info in enumerate(other_relevant_csvs):
                print(f"  {i+1}. {csv_info['csv_id']}: score={csv_info['relevance_score']}")
        
        # Use the RAG QA agent to evaluate the result
        qa_result = self.rag_qa_agent.process_answer(
            query=state["standalone_query"],
            answer=answer,
            processed_csv_id=csv_id,
            processed_csv_path=csv_path,
            other_relevant_csvs=other_relevant_csvs
        )
        
        # Only process additional CSV files if the answer is incomplete (below 4) 
        # AND there are explicit missing information items that were asked for
        completeness = qa_result.get('evaluation', {}).get('completeness', 0)
        missing_info = qa_result.get('missing_information', [])
        
        needs_additional_csv = (
            completeness < 4 and 
            len(missing_info) > 0 and 
            len(other_relevant_csvs) > 0 and
            qa_result.get('needs_additional_csv', False)
        )
        
        # Add QA evaluation messages to the state
        messages = state["messages"] + [
            self.llm.invoke([
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=f"""
                I've evaluated the CSV result:
                
                Completeness: {completeness}/5
                Missing Information: {missing_info}
                Needs Additional CSV: {needs_additional_csv}
                
                Evaluation: {qa_result.get('evaluation', {})}
                """)
            ])
        ]
        
        if self.verbose:
            print(f"QA Evaluation - Completeness: {completeness}/5")
            print(f"Missing Information: {missing_info}")
            print(f"Needs Additional CSV: {needs_additional_csv}")
            
        return {
            **state,
            "messages": messages,
            "qa_feedback": qa_result,
            "needs_additional_csv": needs_additional_csv
        }

    def route_after_evaluation(self, state: RAGPlannerState) -> str:
        """
        Determine the next step after evaluating CSV results.

        Args:
            state: The current state

        Returns:
            Next step to take
        """
        if state["needs_additional_csv"] and len(state["remaining_csv_documents"]) > 0:
            return "process_more_csv"
        else:
            return "process_text"

    def process_text(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Process text documents using standard RAG.
        
        🔧 ENHANCED: Also use CSV document content when routed to text processing
        but CSV processing was not selected (e.g., for overview queries).

        Args:
            state: The current state

        Returns:
            Updated state with text processing results
        """
        if self.verbose:
            print("\n=== Starting process_text node ===")
            
        # Combine text documents into context
        text_context = ""
        for doc in state["text_documents"]:
            content = doc.get('chunk', doc.get('content', ''))
            text_context += f"{content}\n\n"
        
        # SAFETY FIX: Don't use CSV content for text processing as this can mix unrelated data
        csv_content_used = False
        if not text_context.strip() and state["csv_documents"]:
            if self.verbose:
                print(f"🔧 SAFETY: No text documents found, but {len(state['csv_documents'])} CSV documents available")
                print(f"🔧 SAFETY: Not using CSV content for text processing to prevent data mixing")
            # Don't extract CSV content for text processing to avoid hallucinations from unrelated data
        
        # Create a simple text processing result
        if text_context.strip():
            text_result = {
                'context': text_context,
                'document_count': len(state["text_documents"]) + (len(state["csv_documents"]) if csv_content_used else 0),
                'processed': True,
                'csv_content_used': csv_content_used,  # Track that we used CSV content
                'source_note': 'Used CSV document content for text processing' if csv_content_used else 'Used text documents'
            }
        else:
            text_result = {
                'context': '',
                'document_count': 0,
                'processed': False,
                'csv_content_used': False
            }
        
        if self.verbose:
            total_docs = len(state['text_documents']) + (len(state["csv_documents"]) if csv_content_used else 0)
            print(f"Processed {total_docs} documents ({len(state['text_documents'])} text + {len(state['csv_documents']) if csv_content_used else 0} CSV)")
            print(f"Text context length: {len(text_context)} characters")
            if csv_content_used:
                print(f"✅ SMART FALLBACK: Successfully used CSV content for text processing")
            
        return {
            **state,
            "text_processing_result": text_result
        }

    def generate_final_answer(self, state: RAGPlannerState) -> RAGPlannerState:
        """
        Generate the final answer combining all processing results.

        Args:
            state: The current state

        Returns:
            Updated state with final answer
        """
        if self.verbose:
            print("\n=== Starting generate_final_answer node ===")
            
        # Combine all results
        csv_results = state.get("csv_processing_results", [])
        pdf_result = state.get("pdf_processing_result", {})
        text_result = state.get("text_processing_result", {})
        
        # Create context for final answer generation
        context_parts = []
        
        # 🔧 ENHANCED: Track which documents actually contribute to the final answer
        contributing_documents = {
            'csv_documents': [],
            'pdf_documents': [],
            'text_documents': []
        }
        
        # PRIORITY ORDER: CSV first (for tabular data), then PDF, then text
        # This ensures CSV agent results (more accurate for tabular data) take priority
        
        # Add CSV results FIRST (higher priority for tabular analysis)
        csv_answers = []
        for i, csv_result in enumerate(csv_results):
            csv_id = csv_result.get('csv_id', f'CSV_{i+1}')
            answer = csv_result.get('answer', '')
            if answer and not csv_result.get('error', False):
                csv_answers.append(f"CSV Analysis ({csv_id}):\n{answer}")
                # Track that CSV processing contributed to the answer
                contributing_documents['csv_documents'].append(csv_id)
        
        # Add CSV results to context first
        context_parts.extend(csv_answers)
        
        # Add PDF results SECOND (lower priority when CSV exists)
        if pdf_result and pdf_result.get('answer') and pdf_result.get('metadata', {}).get('status') == 'success':
            # If we have CSV results, add PDF as supporting information
            if csv_answers:
                context_parts.append(f"Supporting PDF Analysis:\n{pdf_result['answer']}")
            else:
                # If no CSV results, treat PDF as primary
                context_parts.append(f"PDF Analysis:\n{pdf_result['answer']}")
            
            # Track that PDF processing contributed to the answer
            contributing_documents['pdf_documents'].append('pdf_analysis')
        
        # Add text context LAST
        text_context = text_result.get('context', '')
        if text_context.strip():
            # 🔧 ENHANCED: Log when smart fallback was used
            if text_result.get('csv_content_used', False):
                if self.verbose:
                    print(f"✅ SMART FALLBACK: Using CSV content for document processing (overview query)")
                    print(f"✅ SMART FALLBACK: Content length: {len(text_context)} characters")
                context_parts.append(f"Document Context (from file summaries):\n{text_context}")
                # Track that CSV content was used for text processing
                contributing_documents['text_documents'].append('csv_content_fallback')
            else:
                context_parts.append(f"Document Context:\n{text_context}")
                # Track that text processing contributed to the answer
                contributing_documents['text_documents'].append('text_processing')
        
        # Generate final answer
        if context_parts:
            combined_context = "\n\n".join(context_parts)
            
            # Create final answer prompt with priority instructions
            priority_instruction = ""
            if csv_answers and pdf_result and pdf_result.get('answer'):
                priority_instruction = "\n\nIMPORTANT: When both CSV and PDF analysis are available for the same data, prioritize the CSV analysis result as it is specifically designed for tabular data processing and is more accurate for data queries."
            
            # Include conversation context and INTENT information in final answer generation
            conversation_context = ""
            if state.get('conversation_history'):
                conversation_context = f"\n\nConversation Context:\n{state['conversation_history']}\n"
                
            # CRITICAL: Include query intent information for the final answer generator
            intent_context = f"""
QUERY INTENT ANALYSIS:
- Original Query: "{state['query']}"
- Enhanced Query: "{state['standalone_query']}"
- This ensures the final answer directly addresses the user's specific intent and context.
"""
            
            # Check if user is asking for tabular format OR if CSV results contain formatted tables
            query_lower = state['standalone_query'].lower()
            wants_tabular = any(term in query_lower for term in [
                'table', 'tabular', 'format', 'grid', 'rows', 'columns', 
                'csv format', 'structured', 'formatted'
            ])
            
            # Check if CSV results contain formatted tables (look for table characters)
            has_formatted_tables = False
            for csv_result in csv_results:
                answer = csv_result.get('answer', '')
                if any(char in answer for char in ['+---', '|', '====', '```']):
                    has_formatted_tables = True
                    break
            
            # Extract query topic for topic mismatch detection
            query_entities = self._extract_key_entities(state['standalone_query'].lower())
            
            # Extract entities from available context
            context_entities = set()
            for part in context_parts:
                context_entities.update(self._extract_key_entities(part.lower()))
            
            # Check for topic mismatch between query and available context
            topic_mismatch = False
            query_only_entities = query_entities - context_entities
            
            if query_only_entities and len(query_only_entities) > 0:
                topic_mismatch = True
                if self.verbose:
                    print(f"⚠️ TOPIC MISMATCH DETECTED: Query mentions entities not in context: {', '.join(query_only_entities)}")
                    print(f"⚠️ This suggests the available documents may not contain information about the query topic")
            
            # Anti-hallucination instructions based on topic mismatch
            anti_hallucination_instructions = ""
            if topic_mismatch:
                anti_hallucination_instructions = """
                CRITICAL - TOPIC MISMATCH DETECTED:
                1. The query asks about topics/entities NOT present in the available documents
                2. DO NOT fabricate or hallucinate information that is not in the provided context
                3. EXPLICITLY acknowledge when you don't have information on a topic
                4. Say "I don't have information about [specific topic]" rather than making up an answer
                5. If the query is about X but documents only contain information about Y, clearly state this mismatch
                6. NEVER pretend to have information you don't have - be transparent about limitations
                """
            
            if wants_tabular or has_formatted_tables:
                final_prompt = f"""
                Based on the following information, provide a comprehensive answer to the query: "{state['standalone_query']}"
                {intent_context}
                {conversation_context}
                Available Information:
                {combined_context}{priority_instruction}
                
                {anti_hallucination_instructions}
                
                CRITICAL: The analysis contains formatted tables. You MUST:
                1. PRESERVE ALL tabular data EXACTLY as they appear in the available information
                2. Include the COMPLETE table structures with ALL headers and data rows
                3. Convert tables to proper MARKDOWN table format using | Column 1 | Column 2 | Column 3 |
                4. Use markdown table syntax with proper alignment: |:---:|:---:|:---:|
                5. Do NOT summarize, rewrite, or paraphrase the tabular data - copy it VERBATIM but in markdown format
                6. Format tables as markdown tables for better chat interface display
                7. Provide interpretation and insights AFTER showing the complete markdown tables
                8. Structure your response as: Answer → Complete Markdown Tables → Key Findings → Business Implications
                9. NEVER replace tables with text summaries - always show the actual data in markdown table format
                10. NEVER provide information that is not supported by the available context
                11. If the query asks about topics not covered in the available information, clearly state that you don't have that information
                """
            else:
                # Detect if user wants file-by-file breakdown or comprehensive coverage
                query_lower = state['standalone_query'].lower()
                
                # Enhanced detection for comprehensive file coverage
                wants_file_breakdown = any(term in query_lower for term in [
                    'separate', 'separated', 'by file', 'file names', 'each file', 
                    'individual', 'per file', 'document by document',
                    # ENHANCED: Add comprehensive coverage keywords
                    'all files', 'all the files', 'from all files', 'every file',
                    'from each file', 'in all files', 'across all files', 'for all files',
                    'from every file', 'complete list', 'comprehensive', 'exhaustive'
                ])
                
                # Get unique files from the context to ensure all are covered
                files_in_context = set()
                for doc in state.get('csv_documents', []) + state.get('text_documents', []) + state.get('pdf_documents', []):
                    if isinstance(doc, dict):
                        # Check multiple possible file name fields
                        file_name = doc.get('file_name', '') or doc.get('title', '')
                        if file_name:
                            files_in_context.add(file_name)
                        # Also check metadata and file_info
                        metadata = doc.get('metadata', {}) or doc.get('search_result', {}).get('metadata', {})
                        if isinstance(metadata, dict):
                            title = metadata.get('title', '')
                            if title:
                                files_in_context.add(title)
                        # Check file_info field as well
                        file_info = doc.get('file_info', {})
                        if isinstance(file_info, dict):
                            info_name = file_info.get('file_name', '')
                            if info_name:
                                files_in_context.add(info_name)
                
                # Remove empty strings and clean up file names
                files_in_context = {f for f in files_in_context if f and f.strip()}
                
                if wants_file_breakdown and len(files_in_context) > 1:
                    final_prompt = f"""
                    Based on the following information, provide a comprehensive answer to the query: "{state['standalone_query']}"
                    {intent_context}
                    {conversation_context}
                    Available Information:
                    {combined_context}{priority_instruction}
                    
                    {anti_hallucination_instructions}
                    
                    CRITICAL REQUIREMENTS for comprehensive file coverage:
                    1. The user requested information from ALL files: {', '.join(sorted(files_in_context))}
                    2. You MUST systematically go through EVERY file and extract the requested information
                    3. Create clear sections for EACH file with the file name as a header
                    4. Ensure EVERY file gets adequate coverage - do NOT skip any files
                    5. If a file has limited information for the query, acknowledge this explicitly but still mention the file
                    6. When organizing by files, use this structure:
                       ### [File Name 1]
                       [Information from file 1]
                       
                       ### [File Name 2] 
                       [Information from file 2]
                       
                       And so on for ALL files
                    7. After covering all files individually, provide a summary table or comparison
                    8. If multiple files contain similar information, explain both similarities and differences
                    9. Do NOT create generic sections - everything must be clearly attributed to specific files
                    10. The goal is COMPREHENSIVE coverage ensuring no file is overlooked
                    11. NEVER provide information that is not supported by the available context
                    12. If the query asks about topics not covered in the available information, clearly state that you don't have that information
                    """
                else:
                    final_prompt = f"""
                    Based on the following information, provide a comprehensive, clear and concise answer to the query: "{state['standalone_query']}"
                    {intent_context}
                    {conversation_context}
                    Available Information:
                    {combined_context}{priority_instruction}
                    
                    {anti_hallucination_instructions}
                    
                    Please provide a direct answer that:
                    1. States the key finding clearly
                    2. Provides detailed and relevant context or explanation
                    3. Avoids mentioning technical analysis methods or sources
                    4. Uses natural, conversational language
                    5. When multiple analyses are available, use the most accurate and relevant result
                    6. Considers the conversation context to provide contextually appropriate responses
                    7. If multiple files are involved, ensure all are adequately represented
                    8. NEVER provide information that is not supported by the available context
                    9. If the query asks about topics not covered in the available information, clearly state that you don't have that information
                    """
            
            messages = [
                SystemMessage(content="""You are a helpful assistant that provides comprehensive answers based on available information. When multiple analyses of the same data are provided, prioritize the most specialized and accurate analysis method.

CRITICAL TABLE FORMATTING REQUIREMENT:
- If the available information contains ANY tables (even if formatted with +, |, =, or grid characters), you MUST convert them to proper markdown table format
- Use this format: | Column 1 | Column 2 | Column 3 |
- Add alignment row: |:---:|:---:|:---:|
- This ensures tables display properly in the chat interface without overflowing
- Convert ALL tabular data to markdown format, regardless of how it was originally formatted
- Never preserve ASCII table formatting with grid characters (+, |, =) as it breaks the chat interface

ANTI-HALLUCINATION REQUIREMENT:
- ONLY provide information that is directly supported by the available context
- If the query asks about topics not covered in the available information, clearly state: "I don't have information about [specific topic]"
- NEVER make up or fabricate information not present in the provided context
- If you're unsure about something, acknowledge the limitation rather than guessing"""),
                HumanMessage(content=final_prompt)
            ]
            
            response = self.llm.invoke(messages)
            final_answer = response.content
            
        else:
            # 🔧 ENHANCED: Check if we had documents but couldn't process them properly
            if state.get('csv_documents') or state.get('text_documents'):
                # We had documents but couldn't generate context - this should be rare with smart fallback
                available_docs = len(state.get('csv_documents', [])) + len(state.get('text_documents', []))
                if self.verbose:
                    print(f"⚠️ FALLBACK: Had {available_docs} documents but couldn't generate useful context")
                
                # Extract query topic for better error message
                query_topic = state['standalone_query'].lower()
                query_entities = self._extract_key_entities(query_topic)
                entity_list = ", ".join(query_entities) if query_entities else "the requested topic"
                
                final_answer = f"I found {available_docs} potentially relevant document(s), but they don't contain specific information about {entity_list}. The documents I have access to don't provide sufficient information to answer your query about {entity_list}. Please try rephrasing your question or asking about a different topic that might be covered in the available documents."
            else:
                # Truly no documents found
                query_entities = self._extract_key_entities(state['standalone_query'].lower())
                entity_list = ", ".join(query_entities) if query_entities else "the requested topic"
                
                final_answer = f"I don't have any information about {entity_list}. The documents available to me don't contain information relevant to your query. Please try asking about a different topic or check if documents containing information about {entity_list} are available in the system."
        
        # 🔧 SMART REFERENCE FILTERING: Only include references from documents that actually contributed to the final answer
        references = []
        
        # Add PDF references - use actual chunk IDs from PDF processing (only if PDF contributed)
        if 'pdf_analysis' in contributing_documents['pdf_documents']:
            if pdf_result and pdf_result.get('metadata', {}).get('status') == 'success':
                pdf_sources = pdf_result.get('sources', [])
                for source in pdf_sources:
                    if isinstance(source, dict):
                        # Try to get chunk_id from the source
                        chunk_id = source.get('chunk_id')
                        if chunk_id and chunk_id.strip():
                            references.append(chunk_id)
                    elif isinstance(source, str) and source.strip():
                        # If it's already a chunk ID string, use it
                        references.append(source)
        
        # Add text references - but only if text processing actually contributed to the answer
        if 'text_processing' in contributing_documents['text_documents']:
            # Only add text references if text processing was actually used
            text_documents = state.get('text_documents', [])
            for doc in text_documents:
                if isinstance(doc, dict):
                    # Try to get chunk_id from metadata or search_result
                    chunk_id = None
                    if 'metadata' in doc and isinstance(doc['metadata'], dict):
                        chunk_id = doc['metadata'].get('chunk_id')
                    elif 'search_result' in doc and isinstance(doc['search_result'], dict):
                        metadata = doc['search_result'].get('metadata', {})
                        chunk_id = metadata.get('chunk_id')
                    
                    if chunk_id:
                        references.append(chunk_id)
        
        # 🔧 ENHANCED: Add CSV references when CSV content was used for text processing
        if 'csv_content_fallback' in contributing_documents['text_documents']:
            csv_documents = state.get('csv_documents', [])
            for doc in csv_documents:
                if isinstance(doc, dict):
                    # Try to get chunk_id from various sources
                    chunk_id = None
                    if 'metadata' in doc and isinstance(doc['metadata'], dict):
                        chunk_id = doc['metadata'].get('chunk_id')
                    elif 'search_result' in doc and isinstance(doc['search_result'], dict):
                        metadata = doc['search_result'].get('metadata', {})
                        chunk_id = metadata.get('chunk_id')
                    elif 'chunk_id' in doc:
                        chunk_id = doc['chunk_id']
                    
                    if chunk_id:
                        references.append(chunk_id)
        
        # # 🔧 ENHANCED: For queries about specific files, implement file-aware reference filtering
        # # If the answer content clearly focuses on one file, filter references to match
        # if final_answer and len(references) > 2:
        #     # Check if the answer is predominantly about one file
        #     file_mentions = {}
        #     for doc in state.get('csv_documents', []) + state.get('text_documents', []) + state.get('pdf_documents', []):
        #         if isinstance(doc, dict):
        #             file_name = doc.get('file_name', '') or doc.get('title', '')
        #             if file_name:
        #                 # Count mentions of this file in the answer
        #                 # Look for key terms that might indicate the file's content
        #                 if 'resume' in file_name.lower() or 'hari' in file_name.lower():
        #                     if any(term in final_answer.lower() for term in ['hari', 'iit', 'madras', 'chennai', 'chess', 'data science']):
        #                         file_mentions[file_name] = file_mentions.get(file_name, 0) + 5
        #                 elif 'pde' in file_name.lower() or 'phosphodiesterase' in file_name.lower():
        #                     if any(term in final_answer.lower() for term in ['pde', 'phosphodiesterase', 'assay', 'enzyme', 'luminescent']):
        #                         file_mentions[file_name] = file_mentions.get(file_name, 0) + 5
        #                 elif 'amazon' in file_name.lower() or 'customer' in file_name.lower():
        #                     if any(term in final_answer.lower() for term in ['amazon', 'customer', 'survey', 'behavior', 'e-commerce']):
        #                         file_mentions[file_name] = file_mentions.get(file_name, 0) + 5
            
        #     # If the answer is clearly about one file, filter references accordingly
        #     if file_mentions:
        #         primary_file = max(file_mentions, key=file_mentions.get)
        #         if file_mentions[primary_file] >= 5:  # Strong indication of focus on one file
        #             # Filter references to only include chunks from the primary file
        #             filtered_references = []
        #             for ref in references:
        #                 # Check if this reference is from the primary file
        #                 for doc in state.get('csv_documents', []) + state.get('text_documents', []) + state.get('pdf_documents', []):
        #                     if isinstance(doc, dict):
        #                         doc_file_name = doc.get('file_name', '') or doc.get('title', '')
        #                         if doc_file_name == primary_file:
        #                             # Check if this document's chunk_id matches the reference
        #                             chunk_id = None
        #                             if 'metadata' in doc and isinstance(doc['metadata'], dict):
        #                                 chunk_id = doc['metadata'].get('chunk_id')
        #                             elif 'search_result' in doc and isinstance(doc['search_result'], dict):
        #                                 metadata = doc['search_result'].get('metadata', {})
        #                                 chunk_id = metadata.get('chunk_id')
        #                             elif 'chunk_id' in doc:
        #                                 chunk_id = doc['chunk_id']
                                    
        #                             if chunk_id == ref:
        #                                 filtered_references.append(ref)
                    
        #             if filtered_references:
        #                 references = filtered_references
        #                 if self.verbose:
        #                     print(f"🔧 SMART FILTERING: Filtered references to {len(references)} chunks from primary file: {primary_file}")
        
        if self.verbose:
            print(f"Generated final answer: {len(final_answer)} characters")
            print(f"References: {references}")
            print(f"Contributing documents: {contributing_documents}")
            
        return {
            **state,
            "final_answer": final_answer,
            "references": references
        }

    def process_query(self, query: str, standalone_query: str, csv_documents: List[Dict[str, Any]], text_documents: List[Dict[str, Any]], conversation_history: Optional[str] = None, conversation_summary: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a query with the given documents.

        Args:
            query: Original query
            standalone_query: Standalone version of the query
            csv_documents: List of CSV documents from search
            text_documents: List of text documents from search
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation context

        Returns:
            Processing result
        """
        if self.verbose:
            print(f"\n=== RAG Planner Processing Query ===")
            print(f"Query: {query}")
            print(f"Standalone Query: {standalone_query}")
            print(f"CSV Documents: {len(csv_documents)}")
            print(f"Text Documents: {len(text_documents)}")
            print(f"Has Conversation History: {'Yes' if conversation_history else 'No'}")

        # Store conversation history for use in intent analysis
        self._current_conversation_history = conversation_history

        # Initialize state
        initial_state = {
            "messages": [],
            "query": query,
            "standalone_query": standalone_query,
            "conversation_history": conversation_history,
            "conversation_summary": conversation_summary,
            "csv_documents": csv_documents,
            "text_documents": text_documents,
            "pdf_documents": [],
            "needs_csv_processing": False,
            "needs_pdf_processing": False,
            "processed_csv_documents": [],
            "remaining_csv_documents": [],
            "csv_processing_results": [],
            "pdf_processing_result": None,
            "current_csv_result": None,
            "text_processing_result": None,
            "final_answer": None,
            "references": None,
            "qa_feedback": None,
            "needs_additional_csv": False
        }

        # Run the graph
        result = self.graph.invoke(initial_state)

        # Determine processing status
        final_answer = result.get("final_answer", "")
        has_answer = bool(final_answer and final_answer.strip() and 
                         not final_answer.startswith("I couldn't find sufficient information"))
        
        status = "success" if has_answer else "no_answer"
        
        # Determine which agents were used
        agents_used = []
        if result.get("csv_processing_results"):
            agents_used.append("csv_agent")
        if result.get("pdf_processing_result"):
            agents_used.append("pdf_agent")
        if result.get("text_processing_result", {}).get("processed"):
            agents_used.append("text_processor")
        
        return {
            "answer": final_answer,
            "references": result.get("references", []),
            "csv_processing_results": result.get("csv_processing_results", []),
            "text_processing_result": result.get("text_processing_result", {}),
            "qa_feedback": result.get("qa_feedback", {}),
            "status": status,
            "agents_used": agents_used,
            "processing_details": {
                "csv_documents_processed": len(result.get("csv_processing_results", [])),
                "text_documents_processed": result.get("text_processing_result", {}).get("document_count", 0),
                "pdf_documents_processed": 1 if result.get("pdf_processing_result") else 0
            }
        }

    def _extract_key_entities(self, text: str) -> set:
        """
        Extract key entities from a given text.

        Args:
            text: The input text

        Returns:
            A set of extracted entities
        """
        if not text:
            return set()
            
        # Simple extraction of potential entity names (capitalized words)
        words = text.split()
        entities = set()
        
        # Look for company names and product names (often capitalized)
        for i, word in enumerate(words):
            # Skip short words, punctuation, and words at the beginning of sentences
            if len(word) <= 2 or not word.strip().isalnum() or (i > 0 and words[i-1].endswith('.')):
                continue
                
            # Clean the word of punctuation
            clean_word = ''.join(c for c in word if c.isalnum())
            if not clean_word:
                continue
                
            # Check for capitalized words that might be company/brand names
            if clean_word[0].isupper() and not clean_word.isupper():
                # Check if it's part of a multi-word entity
                if i < len(words) - 1 and len(words[i+1]) > 0 and words[i+1][0].isupper():
                    entities.add(f"{clean_word} {words[i+1].strip('.,;:!?')}")
                else:
                    entities.add(clean_word)
            
            # Check for common company identifiers
            lower_word = clean_word.lower()
            if any(term in lower_word for term in ["ltd", "inc", "corp", "motors", "company", "group"]):
                if i > 0:
                    prev_word = ''.join(c for c in words[i-1] if c.isalnum())
                    if prev_word:
                        entities.add(f"{prev_word} {clean_word}")
        
        # Look for specific entity patterns
        common_companies = [
            "tvs", "tvs motors", "tvs motor", "hul", "hindustan unilever", "unilever", 
            "tata", "tata motors", "mahindra", "bajaj", "hero", "royal enfield", 
            "maruti", "suzuki", "honda", "toyota", "hyundai", "kia", "ford", "gm", 
            "general motors", "tesla", "bmw", "mercedes", "audi", "volkswagen", "vw"
        ]
        
        for company in common_companies:
            if company in text.lower():
                entities.add(company)
        
        return entities

def create_rag_planner_agent(data_dir="data", model=None, system_prompt=None, verbose=True):
    """
    Create a RAG Planner agent.

    Args:
        data_dir: Directory where data is stored
        model: LLM model to use
        system_prompt: System prompt for the agent
        verbose: Whether to print verbose output

    Returns:
        RAGPlannerAgent instance
    """
    return RAGPlannerAgent(
        data_dir=data_dir,
        model=model,
        system_prompt=system_prompt,
        verbose=verbose
    ) 