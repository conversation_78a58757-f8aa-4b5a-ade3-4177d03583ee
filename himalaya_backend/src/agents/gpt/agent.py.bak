"""
GPT Agent for the Himalaya system.

This agent answers questions using only the LLM's pre-trained knowledge,
without accessing external sources or documents.
"""

import os
import sys
import datetime
from typing import Dict, Any, Optional, List

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from config.logging_config import get_logger

# Configure logging
logger = get_logger(__name__)

class GPTAgent:
    """
    GPT agent that answers questions using only the LLM's pre-trained knowledge.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True):
        """
        Initialize the GPT agent.

        Args:
            model: LLM to use for responses
            system_prompt: Custom system prompt
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Initialize the model with higher temperature for more creative responses
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.7  # Higher temperature for more varied responses
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME} for Himalaya GPT agent")
        
        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the GPT agent.

        Returns:
            System prompt
        """
        return f"""You are a knowledgeable assistant for the Himalaya system.
        
Your role is to answer questions based on your pre-trained knowledge without referring to specific documents.
Provide helpful, accurate information based on what you know about the world.

When answering:
1. Draw on your general knowledge to provide accurate, informative responses
2. Acknowledge when you're uncertain or when information might be outdated
3. Be clear about the limitations of your knowledge when appropriate
4. For time-sensitive questions, note that your knowledge has a cutoff date
5. If appropriate, mention that for very specific or recent information, the user might want to use the document search or web search features

Today's date is {self.today_date}, but your knowledge may not include very recent events.

Keep your responses conversational, helpful, and focused on providing value from your knowledge base."""

    def process_query(
        self, 
        query: str,
        conversation_history: Optional[str] = None,
        conversation_summary: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a query using only the LLM's pre-trained knowledge.

        Args:
            query: User query
            conversation_history: Optional conversation history
            conversation_summary: Optional conversation summary

        Returns:
            Dictionary with the response
        """
        if self.verbose:
            print(f"Processing GPT knowledge query: {query}")

        # Create messages
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=query)
        ]
        
        # If there's conversation history, include it for context
        if conversation_history and len(conversation_history) > 0:
            context_note = f"Previous conversation context:\n{conversation_history}"
            messages.insert(1, SystemMessage(content=context_note))
            
            if self.verbose:
                print(f"Including conversation history context ({len(conversation_history)} chars)")
        
        # Generate response
        response = self.model.invoke(messages)
        
        if self.verbose:
            print(f"Generated GPT response: {response.content[:100]}...")
        
        # Return the result
        return {
            "answer": response.content,
            "references": {},
            "agent_type": "gpt",
            "knowledge_based": True
        }

def create_gpt_agent(model=None, system_prompt=None, verbose=True) -> GPTAgent:
    """
    Create a GPT agent.

    Args:
        model: LLM to use for responses
        system_prompt: Custom system prompt
        verbose: Whether to print verbose output

    Returns:
        A GPT agent
    """
    return GPTAgent(model=model, system_prompt=system_prompt, verbose=verbose) 