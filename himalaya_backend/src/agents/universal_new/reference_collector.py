"""
Reference Collection System

Collects, deduplicates, and manages references from multiple agents
with proper source attribution and file name resolution.
"""

import os
import sys
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
import hashlib
import json
import time
import re

# Import database models for file name resolution
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
try:
    from models.models import File
except ImportError:
    # Fallback if models are not available
    File = None

from .state import (
    UniversalNewState, AgentResult, AgentType,
    ReferenceCollectionConfig
)


@dataclass
class ProcessedReference:
    """A processed and standardized reference"""
    source_type: str  # 'file', 'web', 'knowledge'
    source_id: str    # Unique identifier
    title: str        # Clean display name
    content: str      # Reference content/snippet
    url: Optional[str] = None
    file_id: Optional[int] = None
    blob_name: Optional[str] = None
    confidence_score: float = 0.0
    agent_source: AgentType = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ReferenceCollector:
    """
    Collects and manages references from multiple agents.
    
    Features:
    - Deduplication by source and content
    - File name resolution using database
    - Source attribution tracking
    - Confidence score aggregation
    - Clean reference formatting
    """
    
    def __init__(self, config: ReferenceCollectionConfig = None, verbose: bool = True):
        """
        Initialize the Reference Collector.
        
        Args:
            config: Configuration for reference collection
            verbose: Whether to enable verbose logging
        """
        self.config = config or ReferenceCollectionConfig()
        self.verbose = verbose
        self.collected_references: Dict[str, ProcessedReference] = {}
        self.source_hashes: Set[str] = set()
        self.content_hashes: Set[str] = set()
        
        if self.verbose:
            print("📚 REFERENCE COLLECTOR: Initialized")
    
    def collect_references_from_result(
        self,
        agent_type: AgentType,
        result: AgentResult
    ) -> None:
        """
        Collect references from an agent result.
        
        Args:
            agent_type: Type of agent that produced the result
            result: Agent result containing references
        """
        if not result.references and agent_type != AgentType.GPT:
            # For GPT, we'll create a reference even if none exists in the result
            if self.verbose:
                print(f"   No references found in {agent_type.value} result")
            return
        
        if self.verbose:
            print(f"📚 REFERENCE COLLECTOR: Collecting references from {agent_type.value}")
            if result.references:
                print(f"   References type: {type(result.references)}")
                
                if isinstance(result.references, dict):
                    print(f"   Dictionary keys: {list(result.references.keys())}")
                elif isinstance(result.references, list):
                    print(f"   List length: {len(result.references)}")
                    if result.references and isinstance(result.references[0], dict):
                        print(f"   First item keys: {list(result.references[0].keys())}")
        
        try:
            if agent_type == AgentType.RAG:
                self._collect_rag_references(result.references, agent_type)
            elif agent_type == AgentType.WEB_SEARCH:
                self._collect_web_references(result.references, agent_type)
            elif agent_type == AgentType.DEEP_SEARCH:
                self._collect_deep_search_references(result.references, agent_type)
            elif agent_type == AgentType.GPT:
                self._collect_gpt_references(result, agent_type)
            
            if self.verbose:
                print(f"   Collected {len(self.collected_references)} unique references so far")
                
        except Exception as e:
            if self.verbose:
                print(f"❌ REFERENCE COLLECTOR: Error collecting references from {agent_type.value}: {e}")
                import traceback
                traceback.print_exc()
    
    def _collect_rag_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from RAG agent"""
        # Handle different formats of RAG results
        rag_refs = []
        
        # Check if references is a dictionary with rag_references key
        if isinstance(references, dict) and 'rag_references' in references:
            rag_refs = references.get('rag_references', [])
        # Check if references is a dictionary with file_references key
        elif isinstance(references, dict) and 'file_references' in references:
            rag_refs = references.get('file_references', [])
        # Check if references is already a list of file references
        elif isinstance(references, list):
            rag_refs = references
        
        if self.verbose:
            print(f"   RAG references: Found {len(rag_refs)} references")
        
        for ref in rag_refs:
            if isinstance(ref, dict):
                # Extract file information
                blob_name = ref.get('blob_name', '')
                file_id = ref.get('file_id')
                content = ref.get('content', '')
                
                # Resolve clean file name
                clean_title = self._resolve_file_name(blob_name, file_id)
                
                # Create processed reference
                processed_ref = ProcessedReference(
                    source_type='file',
                    source_id=blob_name or str(file_id),
                    title=clean_title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    file_id=file_id,
                    blob_name=blob_name,
                    confidence_score=ref.get('confidence_score', 0.7),
                    agent_source=agent_type,
                    metadata=ref
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not rag_refs:
            print(f"⚠️ REFERENCE COLLECTOR: No RAG references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_web_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from Web Search agent"""
        # Handle different formats of web search results
        web_sources = {}
        
        # Check if references is a dictionary with web_sources key
        if isinstance(references, dict) and 'web_sources' in references:
            web_sources = references.get('web_sources', {})
        # Check if references is a dictionary with web_references key
        elif isinstance(references, dict) and 'web_references' in references:
            web_refs = references.get('web_references', [])
            # Convert list format to dictionary format
            for ref in web_refs:
                if isinstance(ref, dict) and 'url' in ref:
                    web_sources[ref['url']] = ref
        # Check if references is already a dictionary of URL -> data
        elif isinstance(references, dict) and all(isinstance(k, str) and (k.startswith('http') or k.startswith('www')) for k in references.keys()):
            web_sources = references
        # Check if references is a list of web references
        elif isinstance(references, list):
            # Convert list format to dictionary format
            for ref in references:
                if isinstance(ref, dict) and 'url' in ref:
                    web_sources[ref['url']] = ref
        
        if self.verbose:
            print(f"   Web references: Found {len(web_sources)} sources")
        
        for url, source_data in web_sources.items():
            if isinstance(source_data, dict):
                title = source_data.get('title', url)
                content = source_data.get('content', '')
                
                processed_ref = ProcessedReference(
                    source_type='web',
                    source_id=url,
                    title=title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    url=url,
                    confidence_score=source_data.get('confidence_score', 0.6),
                    agent_source=agent_type,
                    metadata=source_data
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not web_sources:
            print(f"⚠️ REFERENCE COLLECTOR: No Web references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_deep_search_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from Deep Search agent"""
        
        # Check if references is a list (direct format from deep_search)
        if isinstance(references, list):
            deep_refs = references
        else:
            # Try to get from the expected key
            deep_refs = references.get('deep_search_references', [])
            # If still empty, check if the entire references dict should be treated as a single reference
            if not deep_refs and isinstance(references, dict) and 'file_name' in references:
                deep_refs = [references]
        
        if self.verbose:
            print(f"   Deep Search references: Found {len(deep_refs)} references")
        
        for ref in deep_refs:
            if isinstance(ref, dict):
                file_name = ref.get('file_name', '')
                file_id = ref.get('file_id')
                content = ref.get('content', '')
                blob_name = ref.get('blob_name', '')
                chunk_id = ref.get('chunk_id', '')
                
                # Look up the file in the database by blob name to get the correct file ID
                correct_file_id = None
                if File is not None:
                    try:
                        if blob_name:
                            print(f"   Looking up file in database by blob name: {blob_name}")
                            file_record = File.query.filter(File.blob_url.contains(blob_name)).first()
                            if file_record:
                                correct_file_id = file_record.id
                                file_name = file_record.file_name
                                print(f"   Found file in database: ID={correct_file_id}, Name={file_name}")
                            else:
                                print(f"   File not found in database by blob name: {blob_name}")
                    except Exception as e:
                        print(f"   Error looking up file in database: {e}")
                
                # Use correct file ID if found, otherwise use the one from the reference
                file_id = correct_file_id or file_id
                
                # Use file_name directly from deep search as it should be clean
                clean_title = file_name or self._resolve_file_name(blob_name, file_id)
                
                processed_ref = ProcessedReference(
                    source_type='file',
                    source_id=str(file_id) if file_id else (chunk_id or file_name),
                    title=clean_title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    file_id=file_id,
                    blob_name=blob_name,
                    confidence_score=ref.get('confidence_score', 0.9),
                    agent_source=agent_type,
                    metadata=ref
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not deep_refs:
            print(f"⚠️ REFERENCE COLLECTOR: No Deep Search references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_gpt_references(self, result: AgentResult, agent_type: AgentType) -> None:
        """Collect references from GPT agent - create a reference for GPT's knowledge"""
        # Create a reference for GPT's knowledge
        source_id = f"gpt_knowledge_{int(time.time())}"
        
        # Extract metadata if available
        metadata = result.metadata if hasattr(result, 'metadata') and result.metadata else {}
        
        # Extract targeted query if available for better context
        targeted_query = metadata.get('targeted_query', '')
        context_provided = metadata.get('context_provided', True)
        context_length = metadata.get('context_length', 0)
        response_length = metadata.get('response_length', 0)
        
        # Enhanced metadata for better UI display
        enhanced_metadata = {
            'agent_type': 'universal_gpt',
            'targeted_query': targeted_query,
            'context_provided': context_provided,
            'context_length': context_length,
            'response_length': response_length,
            'source_type': 'knowledge',
            'metadata': metadata  # Preserve original metadata
        }
        
        # Create a reference for GPT's knowledge
        processed_ref = ProcessedReference(
            source_type='knowledge',
            source_id=source_id,
            title="GPT Knowledge",
            content="Information from GPT's knowledge base",
            confidence_score=result.confidence_score if hasattr(result, 'confidence_score') else 0.85,
            agent_source=agent_type,
            metadata=enhanced_metadata
        )
        
        self._add_reference_if_unique(processed_ref)
        
        if self.verbose:
            print(f"   Added GPT knowledge reference: {processed_ref.title}")
            print(f"   GPT reference metadata: agent_type=universal_gpt, context_length={context_length}, response_length={response_length}")
    
    def _add_reference_if_unique(self, ref: ProcessedReference) -> None:
        """
        Add reference if it's unique based on deduplication settings.
        
        Args:
            ref: Processed reference to add
        """
        # Check source deduplication
        if self.config.deduplicate_by_source:
            source_hash = self._get_source_hash(ref)
            if source_hash in self.source_hashes:
                # Update existing reference with higher confidence if applicable
                existing_ref = self.collected_references.get(ref.source_id)
                if existing_ref and ref.confidence_score > existing_ref.confidence_score:
                    existing_ref.confidence_score = ref.confidence_score
                    existing_ref.metadata.update(ref.metadata)
                return
            self.source_hashes.add(source_hash)
        
        # Check content deduplication
        if self.config.deduplicate_by_content:
            content_hash = self._get_content_hash(ref.content)
            if content_hash in self.content_hashes:
                return
            self.content_hashes.add(content_hash)
        
        # Add the reference
        self.collected_references[ref.source_id] = ref
        
        if self.verbose:
            print(f"   Added reference: {ref.title[:50]}...")
    
    def _get_source_hash(self, ref: ProcessedReference) -> str:
        """Get hash for source deduplication"""
        if ref.source_type == 'file':
            return f"file_{ref.file_id}_{ref.blob_name}"
        elif ref.source_type == 'web':
            return f"web_{ref.url}"
        else:
            return f"{ref.source_type}_{ref.source_id}"
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash for content deduplication"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _resolve_file_name(self, blob_name: Optional[str], file_id: Optional[int]) -> str:
        """
        Resolve clean file name using database lookup.
        
        Args:
            blob_name: Blob name from storage
            file_id: File ID from database
            
        Returns:
            Clean file name
        """
        if not self.config.resolve_file_names:
            return blob_name or f"File_{file_id}"
        
        try:
            # Try to get file info from database using File model (like RAG agent does)
            if blob_name and File is not None:
                file_record = File.query.filter(File.blob_url.contains(blob_name)).first()
                if file_record and file_record.file_name:
                    return file_record.file_name
            
            # Fallback to blob name processing (similar to deep_search agent)
            if blob_name:
                clean_name = self._extract_clean_filename(blob_name)
                return clean_name
            
            # Always return a valid filename that can be used for URLs
            if blob_name:
                # Always prefer blob name for URL construction
                return blob_name
            elif file_id:
                return f"File_{file_id}"
            else:
                # Return a generic but valid filename
                return "resume.pdf"
            
        except Exception as e:
            if self.verbose:
                print(f"⚠️ REFERENCE COLLECTOR: Error resolving file name: {e}")
            # Always return a valid filename that can be used for URLs
            if blob_name:
                return blob_name
            elif file_id:
                return f"File_{file_id}"
            else:
                return "resume.pdf"
    
    def get_final_references(self) -> Dict[str, Any]:
        """
        Get the final processed references in a standardized format matching the working system.

        Returns:
            Dictionary of processed references in the format expected by the UI
        """
        print("\n==== UNIVERSAL AGENT: get_final_references CALLED ====")
        print(f"Number of collected references: {len(self.collected_references)}")
        
        if not self.collected_references:
            print("No references collected, returning empty dict")
            return {}

        # Initialize the sources structure to match the working system format
        sources = {
            "rag": [],
            "gpt": [],
            "web_search": {}
        }

        for ref_id, ref in self.collected_references.items():
            print(f"\nProcessing reference: {ref_id}")
            print(f"  Type: {ref.source_type}")
            print(f"  Title: {ref.title}")
            print(f"  File ID: {ref.file_id}")
            print(f"  Blob name: {ref.blob_name}")
            
            if ref.source_type == 'file':
                # Format file references for RAG section
                blob_name = ref.blob_name or ""
                file_id = ref.file_id or 0
                
                # Generate SAS URL for blob access
                blob_url = ""
                
                # Clean up filename to remove UUID and timestamp prefixes
                clean_filename = self._extract_clean_filename(ref.title)
                print(f"  Original title: {ref.title}")
                print(f"  Clean filename after extraction: {clean_filename}")
                
                # Try to get SAS URL for the file
                try:
                    # Import here to avoid circular imports
                    import sys
                    import os
                    sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                    from utils.blob_utils import generate_sas_url
                    
                    # First try to get file from database by ID
                    file_record = None
                    department_id = None
                    vertical_id = None
                    upload_time = None
                    
                    if File is not None and file_id:
                        print(f"  Looking up file in database with ID: {file_id}")
                        file_record = File.query.get(file_id)
                        if file_record and file_record.blob_url:
                            print(f"  Found file by ID: {file_id}, name: {file_record.file_name}")
                            department_id = file_record.department_id
                            vertical_id = file_record.vertical_id
                            upload_time = file_record.upload_time
                    
                    # If file not found by ID, try looking up by blob name
                    if not file_record and File is not None and blob_name:
                        print(f"  Looking up file in database by blob name: {blob_name}")
                        file_record = File.query.filter(File.blob_url.contains(blob_name)).first()
                        if file_record:
                            print(f"  Found file by blob name: {blob_name}")
                            # Update file_id with the correct one from database
                            file_id = file_record.id
                            department_id = file_record.department_id
                            vertical_id = file_record.vertical_id
                            upload_time = file_record.upload_time
                    
                    # If file record found, generate SAS URL and get clean filename
                    if file_record and file_record.blob_url:
                        blob_url = generate_sas_url(file_record.blob_url)
                        print(f"  Generated SAS URL: {blob_url[:50]}...")
                        
                        # Get clean filename from database record if possible
                        db_file_name = file_record.file_name
                        if db_file_name:
                            print(f"  DB filename before cleaning: {db_file_name}")
                            # Extract clean filename without path or UUID prefixes
                            clean_filename = self._extract_clean_filename(db_file_name)
                            print(f"  Final clean filename: {clean_filename}")
                        
                        if self.verbose:
                            print(f"Generated SAS URL for file {file_id}: {clean_filename}")
                    else:
                        print(f"  File not found in database with ID: {file_id} or blob name: {blob_name}")
                        print(f"  Using clean filename from reference title: {clean_filename}")
                except Exception as e:
                    print(f"  ERROR generating SAS URL: {e}")
                    import traceback
                    traceback.print_exc()

                # Create RAG-style reference matching the working system
                rag_ref = {
                    "blob_url": blob_url,
                    "chunk_id": f"chunk_{file_id}_0_{hash(ref.content) % 100000:08x}",
                    "content": ref.content,
                    "file_info": {
                        "blob_url": blob_url,
                        "file_name": clean_filename,
                        "id": file_id,
                        "file_type": "document",  # Use file_type instead of source_type for consistency
                        "file_size": getattr(file_record, 'file_size', 0) if file_record else 0,
                        "upload_time": upload_time.isoformat() if upload_time else None,
                        "media_type": getattr(file_record, 'media_type', None) if file_record else None,
                        "department_id": department_id,
                        "vertical_id": vertical_id
                    },
                    "score": ref.confidence_score
                }
                sources["rag"].append(rag_ref)
                print(f"  Added RAG reference with clean filename: {clean_filename}")

            elif ref.source_type == 'knowledge':
                # Format knowledge references for GPT section
                gpt_ref = {
                    "content": "GPT",
                    "reference_id": "gpt",
                    "score": ref.confidence_score,
                    "title": "GPT",
                    "type": "gpt"
                }
                sources["gpt"].append(gpt_ref)
                print(f"  Added GPT reference")

            elif ref.source_type == 'web':
                # Format web references for web_search section
                # Use a numbered key format like in the working system
                web_key = str(len(sources["web_search"]) + 1)
                sources["web_search"][web_key] = ref.url
                print(f"  Added Web reference: {ref.url}")

        # Clean up empty sections to match working system behavior
        if not sources["rag"]:
            del sources["rag"]
        if not sources["gpt"]:
            del sources["gpt"]
        if not sources["web_search"]:
            del sources["web_search"]

        print("\n==== FINAL REFERENCES STRUCTURE ====")
        for section, refs in sources.items():
            print(f"Section: {section}, Count: {len(refs) if isinstance(refs, list) else len(refs.keys())}")
            if section == "rag" and refs:
                for i, ref in enumerate(refs):
                    print(f"  RAG ref {i}: file_name={ref['file_info']['file_name']}, blob_url={ref['blob_url'][:30]}...")
        
        return sources
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the reference collection.
        
        Returns:
            Statistics dictionary
        """
        total_refs = len(self.collected_references)
        
        # Count by type
        type_counts = {}
        agent_counts = {}
        
        for ref in self.collected_references.values():
            type_counts[ref.source_type] = type_counts.get(ref.source_type, 0) + 1
            agent_source = ref.agent_source.value if ref.agent_source else 'unknown'
            agent_counts[agent_source] = agent_counts.get(agent_source, 0) + 1
        
        # Calculate average confidence
        avg_confidence = 0.0
        if total_refs > 0:
            avg_confidence = sum(ref.confidence_score for ref in self.collected_references.values()) / total_refs
        
        return {
            'total_references': total_refs,
            'references_by_type': type_counts,
            'references_by_agent': agent_counts,
            'average_confidence': avg_confidence,
            'deduplication_stats': {
                'unique_sources': len(self.source_hashes),
                'unique_content': len(self.content_hashes)
            },
            'config': {
                'deduplicate_by_source': self.config.deduplicate_by_source,
                'deduplicate_by_content': self.config.deduplicate_by_content,
                'resolve_file_names': self.config.resolve_file_names,
                'max_references_per_agent': self.config.max_references_per_agent
            }
        }

    def _extract_clean_filename(self, blob_name: str) -> str:
        """
        Extract clean filename from blob name by removing UUID and timestamp prefixes.

        This follows the same logic as the deep_search agent.
        Blob names typically follow pattern: uuid_timestamp_filename.ext
        Example: bb8f1d8e-f681-4e9a-bd55-44f0ba876c9c_20250708_143514_OECD491.pdf

        Args:
            blob_name: The blob name to clean

        Returns:
            Clean filename
        """
        if not blob_name:
            return "document.pdf"
            
        # Remove path if present
        if '/' in blob_name:
            blob_name = blob_name.split('/')[-1]
        if '\\' in blob_name:
            blob_name = blob_name.split('\\')[-1]

        # Check for UUID pattern (8-4-4-4-12 hexadecimal digits)
        uuid_pattern = r'^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}_'
        if re.match(uuid_pattern, blob_name):
            # Remove UUID prefix
            blob_name = re.sub(uuid_pattern, '', blob_name)
        
        # Split by underscore
        parts = blob_name.split('_')

        # Check if it has timestamp pattern (typically YYYYMMDD_HHMMSS)
        if len(parts) >= 2 and parts[0].isdigit() and len(parts[0]) == 8:
            # Skip timestamp parts (typically first two segments if they're numeric)
            if len(parts) >= 3 and parts[1].isdigit():
                # Remove both timestamp parts
                clean_name = '_'.join(parts[2:])
                return clean_name
            else:
                # Remove just the date part
                clean_name = '_'.join(parts[1:])
                return clean_name
        
        # Check for the common prefix pattern without UUID but with timestamp
        if len(parts) >= 3:
            # If first part is date-like (all digits, 8 chars)
            if parts[0].isdigit() and len(parts[0]) == 8:
                # Second part is likely time
                if parts[1].isdigit() and (len(parts[1]) == 6 or len(parts[1]) == 4):
                    # Skip first two parts (date and time)
                    clean_name = '_'.join(parts[2:])
                    return clean_name
        
        # If we can't identify a pattern to remove, return the original
        return blob_name
