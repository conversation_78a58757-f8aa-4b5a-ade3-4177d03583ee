import os
import threading
from datetime import datetime
import tempfile
import uuid
import logging
from models.models import db, File
from utils.audio_processor import AudioProcessor
from azure.storage.blob import BlobServiceClient
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    GENERAL_MEDIA_CONTAINER,
    AZURE_STORAGE_CONTAINER_NAME
)
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class MediaProcessor:
    def __init__(self, app=None):
        self.connection_string = AZURE_STORAGE_CONNECTION_STRING
        self.media_container = GENERAL_MEDIA_CONTAINER
        self.search_container = AZURE_STORAGE_CONTAINER_NAME
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        self.audio_processor = AudioProcessor()
        self.app = app

    def download_blob(self, blob_url, local_path):
        try:
            # Parse the URL to extract container and blob path
            parsed_url = urlparse(blob_url)
            path_parts = parsed_url.path.strip('/').split('/')
            
            # The first part is the container name, the rest is the blob path
            container_name = path_parts[0]
            blob_path = '/'.join(path_parts[1:])
            
            logger.info(f"Downloading from container: {container_name}, blob: {blob_path}")
            
            # Get container client
            container_client = self.blob_service_client.get_container_client(container_name)
            
            # Get blob client
            blob_client = container_client.get_blob_client(blob_path)
            
            # Download the blob
            with open(local_path, "wb") as file:
                data = blob_client.download_blob()
                file.write(data.readall())
            
            logger.info(f"Successfully downloaded blob to: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading blob: {str(e)}")
            return False

    def upload_blob(self, local_path, blob_name, container_name=None):
        try:
            # Use provided container name or default to self.container_name
            container_name = container_name or self.media_container
            container_client = self.blob_service_client.get_container_client(container_name)
            
            with open(local_path, "rb") as file:
                blob_client = container_client.upload_blob(name=blob_name, data=file, overwrite=True)
                logger.info(f"Successfully uploaded blob: {blob_name} to container: {container_name}")
                return blob_client.url
        except Exception as e:
            logger.error(f"Error uploading blob: {str(e)}")
            return None

    def process_media(self, file_id):
        with self.app.app_context():
            start_time = datetime.utcnow()
            try:
                file = File.query.get(file_id)
                if not file:
                    logger.error(f"❌ File not found: {file_id}")
                    return

                logger.info(f"🚀 Starting to process {file.media_type} file {file_id}: {file.file_name}")
                logger.info(f"📄 File blob URL: {file.blob_url}")
                
                file.processing_status = 'processing'
                db.session.commit()
                logger.info("✅ Updated file status to 'processing'")

                with tempfile.TemporaryDirectory() as temp_dir:
                    logger.info(f"📁 Created temporary directory: {temp_dir}")
                    
                    # Download media file
                    media_path = os.path.join(temp_dir, f"media_{uuid.uuid4()}")
                    logger.info(f"⬇️ Downloading media file to: {media_path}")
                    
                    download_start = datetime.utcnow()
                    if not self.download_blob(file.blob_url, media_path):
                        raise Exception("Failed to download media file")
                    download_duration = (datetime.utcnow() - download_start).total_seconds()
                    
                    downloaded_size = os.path.getsize(media_path)
                    logger.info(f"✅ Media file downloaded successfully")
                    logger.info(f"📊 Download stats: {downloaded_size} bytes in {download_duration:.2f}s")

                    # For video, extract audio first
                    if file.media_type == 'video':
                        logger.info(f"🎵 Extracting audio from video file: {media_path}")
                        
                        extract_start = datetime.utcnow()
                        audio_path = self.audio_processor.extract_audio(media_path, temp_dir)
                        extract_duration = (datetime.utcnow() - extract_start).total_seconds()
                        
                        if not audio_path:
                            raise Exception("Failed to extract audio from video")
                        
                        audio_size = os.path.getsize(audio_path)
                        logger.info(f"✅ Audio extracted successfully to: {audio_path}")
                        logger.info(f"📊 Audio extraction stats: {audio_size} bytes in {extract_duration:.2f}s")
                    else:
                        audio_path = media_path
                        logger.info(f"🎵 Using audio file directly: {audio_path}")

                    # Verify audio file before transcription
                    logger.info(f"🔍 Verifying audio file before transcription")
                    if os.path.exists(audio_path):
                        file_size = os.path.getsize(audio_path)
                        logger.info(f"✅ Audio file exists, size: {file_size:,} bytes")
                        
                        if file_size == 0:
                            raise Exception("Audio file is empty (0 bytes)")
                        if file_size < 1000:  # Less than 1KB might be too small
                            logger.warning(f"⚠️ Audio file is very small: {file_size} bytes")
                    else:
                        logger.error("❌ Audio file does not exist")
                        raise Exception("Audio file not found after extraction")

                    # Get transcription
                    logger.info(f"🗣️ Starting transcription process...")
                    logger.info(f"📝 Audio file path: {audio_path}")
                    
                    transcription_start = datetime.utcnow()
                    transcription, metadata = self.audio_processor.transcribe_media_file(audio_path)
                    transcription_duration = (datetime.utcnow() - transcription_start).total_seconds()
                    
                    if not transcription:
                        raise Exception("Failed to generate transcription - no text returned")
                    
                    logger.info(f"✅ Transcription completed successfully!")
                    logger.info(f"📊 Transcription stats: {len(transcription):,} characters in {transcription_duration:.2f}s")
                    logger.info(f"📝 Transcription preview: {transcription[:200]}...")
                    
                    if metadata:
                        logger.info(f"📊 Transcription metadata: {metadata}")

                    # Save transcription to blob
                    transcription_blob_name = f"{uuid.uuid4()}.txt"
                    transcription_path = os.path.join(temp_dir, transcription_blob_name)
                    
                    logger.info(f"💾 Saving transcription to file: {transcription_path}")
                    with open(transcription_path, 'w', encoding='utf-8') as f:
                        f.write(transcription)
                    
                    saved_size = os.path.getsize(transcription_path)
                    logger.info(f"✅ Transcription saved to local file: {saved_size} bytes")

                    # Upload to generalaisearch container
                    logger.info(f"☁️ Uploading transcription to Azure blob storage")
                    logger.info(f"📁 Target container: {self.search_container}")
                    logger.info(f"📄 Blob name: {transcription_blob_name}")
                    
                    upload_start = datetime.utcnow()
                    transcription_blob_url = self.upload_blob(
                        transcription_path,
                        transcription_blob_name,
                        self.search_container
                    )
                    upload_duration = (datetime.utcnow() - upload_start).total_seconds()
                    
                    if not transcription_blob_url:
                        raise Exception("Failed to upload transcription to blob storage")
                    
                    logger.info(f"✅ Transcription uploaded successfully in {upload_duration:.2f}s")
                    logger.info(f"🔗 Transcription blob URL: {transcription_blob_url}")

                    # Update file record
                    logger.info(f"💾 Updating database record for file {file_id}")
                    file.transcription_blob_url = transcription_blob_url
                    file.processing_status = 'completed'
                    file.processed_at = datetime.utcnow()
                    db.session.commit()
                    
                    total_duration = (datetime.utcnow() - start_time).total_seconds()
                    logger.info(f"🎉 Successfully processed {file.media_type} file {file_id}")
                    logger.info(f"⏱️ Total processing time: {total_duration:.2f} seconds")

            except Exception as e:
                total_duration = (datetime.utcnow() - start_time).total_seconds()
                logger.error(f"❌ Error processing {file.media_type} file {file_id}: {str(e)}")
                logger.error(f"⏱️ Failed after {total_duration:.2f} seconds")
                
                # Log full traceback for debugging
                import traceback
                logger.error(f"📊 Full error traceback:\n{traceback.format_exc()}")
                
                try:
                    file = File.query.get(file_id)
                    if file:
                        file.processing_status = 'failed'
                        file.error_message = str(e)
                        db.session.commit()
                        logger.info(f"💾 Updated file {file_id} status to 'failed'")
                except Exception as inner_e:
                    logger.error(f"❌ Error updating file status: {str(inner_e)}")

def process_media_async(file_id, app):
    processor = MediaProcessor(app)
    thread = threading.Thread(target=processor.process_media, args=(file_id,))
    thread.daemon = True
    thread.start() 