import os
import subprocess
import logging
from pathlib import Path
import time
import requests
import json
from typing import Optional, <PERSON><PERSON>
from datetime import datetime, timedelta
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from urllib.parse import urlparse
from config.settings import (
    AZURE_SPEECH_KEY, AZURE_SPEECH_REGION, AZURE_STORAGE_CONNECTION_STRING,
    FGD_VIDEOS_CONTAINER, GENERAL_MEDIA_CONTAINER
)
import uuid

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AudioProcessor:
    def __init__(self):
        self.speech_key = AZURE_SPEECH_KEY
        self.speech_region = AZURE_SPEECH_REGION
        self.connection_string = AZURE_STORAGE_CONNECTION_STRING
        
        if not all([self.speech_key, self.speech_region, self.connection_string]):
            raise ValueError("Azure credentials not properly configured")

    def extract_audio(self, video_path: str, output_dir: str) -> Optional[str]:
        """Extract audio from video file using ffmpeg with specific format for Azure Speech Service"""
        try:
            video_filename = Path(video_path).stem
            audio_path = os.path.join(output_dir, f"{video_filename}.wav")

            # FFmpeg command with Azure's recommended audio format
            command = [
                'ffmpeg',
                '-i', video_path,
                '-vn',                    # No video
                '-acodec', 'pcm_s16le',  # PCM 16-bit
                '-ac', '1',              # Mono
                '-ar', '16000',          # 16kHz sampling
                '-y',                    # Overwrite output
                audio_path
            ]

            logger.info(f"Executing FFmpeg command: {' '.join(command)}")
            
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True
            )

            if os.path.exists(audio_path):
                # Verify the audio file
                probe_command = [
                    'ffprobe',
                    '-v', 'error',
                    '-select_streams', 'a:0',
                    '-show_entries', 'stream=codec_name,channels,sample_rate',
                    '-of', 'json',
                    audio_path
                ]
                
                probe_result = subprocess.run(probe_command, capture_output=True, text=True)
                audio_info = json.loads(probe_result.stdout)
                logger.info(f"Generated audio file info: {audio_info}")
                
                # Check file size
                file_size = os.path.getsize(audio_path)
                logger.info(f"Audio file size: {file_size} bytes")
                
                if file_size == 0:
                    logger.error("Generated audio file is empty")
                    return None
                
                return audio_path
            else:
                logger.error(f"Failed to create audio file. FFmpeg output: {result.stderr}")
                return None

        except subprocess.CalledProcessError as e:
            logger.error(f"FFmpeg error: {e.stderr}")
            return None
        except Exception as e:
            logger.error(f"Error extracting audio: {str(e)}")
            return None

    def _generate_sas_url(self, blob_url: str) -> str:
        """Generate SAS URL for blob access"""
        try:
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            
            # Parse the blob URL to get container and blob name
            parsed_url = urlparse(blob_url)
            path_parts = parsed_url.path.lstrip('/').split('/')
            
            # Extract container and blob name correctly
            container_name = path_parts[0]
            blob_name = '/'.join(path_parts[1:])
            
            logger.info(f"Generating SAS for container: {container_name}, blob: {blob_name}")
            
            # Generate SAS token with all required permissions
            sas_token = generate_blob_sas(
                account_name=blob_service_client.account_name,
                container_name=container_name,
                blob_name=blob_name,
                account_key=blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(hours=2),
                start=datetime.utcnow() - timedelta(minutes=5)  # Start time slightly in the past
            )
            
            # Construct the full URL correctly
            account_name = blob_service_client.account_name
            sas_url = f"https://{account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
            
            logger.info(f"Generated SAS URL with pattern: https://<account>.blob.core.windows.net/{container_name}/{blob_name}?<token>")
            return sas_url
        except Exception as e:
            logger.error(f"Error generating SAS URL: {str(e)}")
            raise

    def transcribe_audio(self, audio_path: str) -> Tuple[Optional[str], Optional[dict]]:
        """
        Transcribe audio using Azure Speech Batch Transcription API with support for long files
        """
        try:
            # First, upload the audio file to Azure Blob Storage temporarily
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
            
            # Generate a unique blob name for temporary storage
            temp_blob_name = f"temp_audio/{uuid.uuid4()}.wav"
            
            # Upload the audio file
            with open(audio_path, "rb") as audio_file:
                blob_client = container_client.upload_blob(name=temp_blob_name, data=audio_file)
            
            try:
                # Generate SAS token for the uploaded audio file
                sas_token = generate_blob_sas(
                    account_name=blob_service_client.account_name,
                    container_name=FGD_VIDEOS_CONTAINER,
                    blob_name=temp_blob_name,
                    account_key=blob_service_client.credential.account_key,
                    permission=BlobSasPermissions(read=True),
                    expiry=datetime.utcnow() + timedelta(hours=1)
                )
                
                # Construct the full URL with SAS token
                audio_url = f"{blob_client.url}?{sas_token}"
                
                # API endpoints
                base_url = f"https://{self.speech_region}.api.cognitive.microsoft.com/speechtotext/v3.1"
                transcription_url = f"{base_url}/transcriptions"
                
                # Headers for API requests
                headers = {
                    'Ocp-Apim-Subscription-Key': self.speech_key,
                    'Content-Type': 'application/json'
                }
                
                # Create transcription request
                transcription_config = {
                    'displayName': f'Transcription_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}',
                    'locale': 'en-US',
                    'properties': {
                        'wordLevelTimestamps': True,
                        'punctuationMode': 'DictatedAndAutomatic',
                        'profanityFilterMode': 'None',
                        'timeToLive': 'PT24H'
                    },
                    'contentUrls': [audio_url]
                }
                
                # Start transcription
                logger.info("Starting batch transcription")
                response = requests.post(transcription_url, headers=headers, json=transcription_config)
                
                if not response.ok:
                    error_msg = f"Failed to start transcription. Status: {response.status_code}, Response: {response.text}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                
                transcription_location = response.headers.get('location') or response.json().get('self')
                if not transcription_location:
                    raise Exception("No transcription location received in response")
                
                transcription_id = transcription_location.split('/')[-1]
                status_url = f"{transcription_url}/{transcription_id}"
                
                # Poll for completion with extended timeout
                MAX_POLLING_TIME = 4 * 60 * 60  # 4 hours maximum
                INITIAL_POLLING_INTERVAL = 30  # Start with 30 seconds
                MAX_POLLING_INTERVAL = 300  # Maximum 5 minutes between polls
                current_interval = INITIAL_POLLING_INTERVAL
                
                start_time = time.time()
                while True:
                    current_time = time.time()
                    if current_time - start_time > MAX_POLLING_TIME:
                        raise Exception(f"Transcription timed out after {MAX_POLLING_TIME/3600:.1f} hours")
                    
                    response = requests.get(status_url, headers=headers)
                    if not response.ok:
                        logger.error(f"Failed to get status. Response: {response.text}")
                        raise Exception("Failed to get transcription status")
                    
                    status_data = response.json()
                    status = status_data.get('status')
                    logger.info(f"Transcription status: {status} (elapsed time: {(current_time-start_time)/60:.1f} minutes)")
                    
                    if status == 'Succeeded':
                        break
                    elif status in ['Failed', 'Cancelled']:
                        error_details = status_data.get('properties', {}).get('error', 'No error details available')
                        raise Exception(f"Transcription failed. Status: {status}, Details: {error_details}")
                    
                    # Exponential backoff for polling interval
                    time.sleep(current_interval)
                    current_interval = min(current_interval * 1.5, MAX_POLLING_INTERVAL)
                
                # Get transcription files
                files_url = f"{status_url}/files"
                response = requests.get(files_url, headers=headers)
                if not response.ok:
                    raise Exception(f"Failed to get transcription files: {response.text}")
                
                files = response.json().get('values', [])
                if not files:
                    raise Exception("No transcription files found")
                
                # Get the transcription file
                transcription_file = next((f for f in files if f['kind'] == 'Transcription'), None)
                if not transcription_file:
                    raise Exception("Transcription file not found in response")
                
                # Get the actual transcription content
                content_url = transcription_file['links']['contentUrl']
                response = requests.get(content_url)
                if not response.ok:
                    raise Exception(f"Failed to get transcription content: {response.text}")
                
                results = response.json()
                
                # Process results
                combined_text = []
                segments = []
                
                recognized_phrases = results.get('recognizedPhrases', [])
                if not recognized_phrases:
                    raise Exception("No recognized phrases in transcription")
                
                for phrase in recognized_phrases:
                    if 'nBest' in phrase and phrase['nBest']:
                        best_result = phrase['nBest'][0]
                        combined_text.append(best_result['display'])
                        segments.append({
                            'text': best_result['display'],
                            'offset': phrase.get('offsetInTicks', 0),
                            'duration': phrase.get('durationInTicks', 0),
                            'confidence': best_result.get('confidence', 0)
                        })
                
                full_text = ' '.join(combined_text)
                
                # Create metadata
                metadata = {
                    'duration': results.get('duration', 0),
                    'segments': len(segments),
                    'word_count': len(full_text.split()),
                    'character_count': len(full_text),
                    'detailed_segments': segments,
                    'locale': results.get('source', 'en-US'),
                    'processing_time': time.time() - start_time
                }
                
                logger.info(f"Transcription completed successfully: {len(segments)} segments, {metadata['word_count']} words")
                return full_text, metadata

            finally:
                # Clean up: Delete the temporary audio blob
                try:
                    container_client.delete_blob(temp_blob_name)
                except Exception as e:
                    logger.warning(f"Failed to delete temporary audio blob: {str(e)}")

        except Exception as e:
            logger.error(f"Error in batch transcription: {str(e)}")
            return None, None

    def transcribe_media_file(self, audio_path: str) -> Tuple[Optional[str], Optional[dict]]:
        """
        Transcribe media files (video/audio) using Azure Speech Batch Transcription API.
        This is separate from FGD video transcription to handle general media files.
        """
        try:
            # First, upload the audio file to Azure Blob Storage temporarily
            blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
            container_client = blob_service_client.get_container_client(GENERAL_MEDIA_CONTAINER)
            
            # Generate a unique blob name for temporary storage
            temp_blob_name = f"temp_audio/{uuid.uuid4()}.wav"
            
            # Upload the audio file
            with open(audio_path, "rb") as audio_file:
                blob_client = container_client.upload_blob(name=temp_blob_name, data=audio_file)
            
            try:
                # Generate SAS token for the uploaded audio file
                sas_token = generate_blob_sas(
                    account_name=blob_service_client.account_name,
                    container_name=GENERAL_MEDIA_CONTAINER,
                    blob_name=temp_blob_name,
                    account_key=blob_service_client.credential.account_key,
                    permission=BlobSasPermissions(read=True),
                    expiry=datetime.utcnow() + timedelta(hours=1)
                )
                
                # Construct the full URL with SAS token
                audio_url = f"{blob_client.url}?{sas_token}"
                
                # API endpoints
                base_url = f"https://{self.speech_region}.api.cognitive.microsoft.com/speechtotext/v3.1"
                transcription_url = f"{base_url}/transcriptions"
                
                # Headers for API requests
                headers = {
                    'Ocp-Apim-Subscription-Key': self.speech_key,
                    'Content-Type': 'application/json'
                }
                
                # Create transcription request - simpler config for general media
                transcription_config = {
                    'displayName': f'MediaTranscription_{datetime.utcnow().strftime("%Y%m%d_%H%M%S")}',
                    'locale': 'en-US',
                    'properties': {
                        'punctuationMode': 'DictatedAndAutomatic',
                        'profanityFilterMode': 'None',
                        'timeToLive': 'PT24H'
                    },
                    'contentUrls': [audio_url]
                }
                
                # Start transcription
                logger.info("🚀 Starting media transcription with Azure Speech API")
                logger.info(f"📍 Endpoint: {transcription_url}")
                logger.info(f"🎵 Audio URL: {audio_url[:100]}...")
                logger.info(f"⚙️ Config: {transcription_config}")
                
                response = requests.post(transcription_url, headers=headers, json=transcription_config)
                
                logger.info(f"📨 API Response Status: {response.status_code}")
                if not response.ok:
                    error_msg = f"❌ Failed to start transcription. Status: {response.status_code}, Response: {response.text}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                else:
                    logger.info(f"✅ Transcription request submitted successfully")
                
                transcription_location = response.headers.get('location') or response.json().get('self')
                if not transcription_location:
                    raise Exception("No transcription location received in response")
                
                transcription_id = transcription_location.split('/')[-1]
                status_url = f"{transcription_url}/{transcription_id}"
                
                # Poll for completion with extended timeout
                MAX_POLLING_TIME = 4 * 60 * 60  # 4 hours maximum
                INITIAL_POLLING_INTERVAL = 30  # Start with 30 seconds
                MAX_POLLING_INTERVAL = 300  # Maximum 5 minutes between polls
                current_interval = INITIAL_POLLING_INTERVAL
                
                start_time = time.time()
                while True:
                    current_time = time.time()
                    if current_time - start_time > MAX_POLLING_TIME:
                        raise Exception(f"Transcription timed out after {MAX_POLLING_TIME/3600:.1f} hours")
                    
                    response = requests.get(status_url, headers=headers)
                    if not response.ok:
                        logger.error(f"Failed to get status. Response: {response.text}")
                        raise Exception("Failed to get transcription status")
                    
                    status_data = response.json()
                    status = status_data.get('status')
                    logger.info(f"Transcription status: {status} (elapsed time: {(current_time-start_time)/60:.1f} minutes)")
                    
                    if status == 'Succeeded':
                        break
                    elif status in ['Failed', 'Cancelled']:
                        error_details = status_data.get('properties', {}).get('error', 'No error details available')
                        raise Exception(f"Transcription failed. Status: {status}, Details: {error_details}")
                    
                    # Exponential backoff for polling interval
                    time.sleep(current_interval)
                    current_interval = min(current_interval * 1.5, MAX_POLLING_INTERVAL)
                
                # Get transcription files
                files_url = f"{status_url}/files"
                response = requests.get(files_url, headers=headers)
                if not response.ok:
                    raise Exception(f"Failed to get transcription files: {response.text}")
                
                files = response.json().get('values', [])
                if not files:
                    raise Exception("No transcription files found")
                
                # Get the transcription file
                transcription_file = next((f for f in files if f['kind'] == 'Transcription'), None)
                if not transcription_file:
                    raise Exception("Transcription file not found in response")
                
                # Get the actual transcription content
                content_url = transcription_file['links']['contentUrl']
                response = requests.get(content_url)
                if not response.ok:
                    raise Exception(f"Failed to get transcription content: {response.text}")
                
                results = response.json()
                
                # Process results
                combined_text = []
                segments = []
                
                recognized_phrases = results.get('recognizedPhrases', [])
                if not recognized_phrases:
                    raise Exception("No recognized phrases in transcription")
                
                for phrase in recognized_phrases:
                    if 'nBest' in phrase and phrase['nBest']:
                        best_result = phrase['nBest'][0]
                        combined_text.append(best_result['display'])
                        segments.append({
                            'text': best_result['display'],
                            'offset': phrase.get('offsetInTicks', 0),
                            'duration': phrase.get('durationInTicks', 0),
                            'confidence': best_result.get('confidence', 0)
                        })
                
                full_text = ' '.join(combined_text)
                
                # Create metadata
                metadata = {
                    'duration': results.get('duration', 0),
                    'segments': len(segments),
                    'word_count': len(full_text.split()),
                    'character_count': len(full_text),
                    'detailed_segments': segments,
                    'locale': results.get('source', 'en-US'),
                    'processing_time': time.time() - start_time
                }
                
                logger.info(f"Transcription completed successfully: {len(segments)} segments, {metadata['word_count']} words")
                return full_text, metadata

            finally:
                # Clean up: Delete the temporary audio blob
                try:
                    container_client.delete_blob(temp_blob_name)
                except Exception as e:
                    logger.warning(f"Failed to delete temporary audio blob: {str(e)}")

        except Exception as e:
            logger.error(f"Error in media transcription: {str(e)}")
            return None, None