from functools import wraps
from flask import request, jsonify
from models.models import User, <PERSON><PERSON>
import jwt
from config.settings import JWT_SECRET_KEY

# Define scope constants - these will be dynamically resolved
SCOPE_CHAT = 1
SCOPE_UPLOAD_FILE = 2
SCOPE_REMOVE_FILE = 3
SCOPE_VIEW_FILE = 4
SCOPE_FGD_OWNER = 5
SCOPE_CREATE_THEME = 6
SCOPE_CREATE_FGD = 7
SCOPE_DELETE_FGD = 8
SCOPE_CHAT_FGD = 9

# Dynamic scope resolution for database-related scopes
_scope_cache = {}

def get_scope_id(scope_name):
    """Get scope ID by name from database with caching"""
    if scope_name not in _scope_cache:
        try:
            scope = Scope.query.filter_by(name=scope_name).first()
            if scope:
                _scope_cache[scope_name] = scope.id
            else:
                # Fallback to hardcoded values for backward compatibility
                fallback_scopes = {
                    'DATABASE_CHAT': 10,
                    'DATABASE_ADMIN': 11
                }
                _scope_cache[scope_name] = fallback_scopes.get(scope_name, 0)
        except Exception:
            # If database is not available, use fallback
            fallback_scopes = {
                'DATABASE_CHAT': 10,
                'DATABASE_ADMIN': 11
            }
            _scope_cache[scope_name] = fallback_scopes.get(scope_name, 0)
    return _scope_cache[scope_name]

# Dynamic scope constants - these will be resolved at runtime
def get_database_chat_scope():
    return get_scope_id('DATABASE_CHAT')

def get_database_admin_scope():
    return get_scope_id('DATABASE_ADMIN')

# For backward compatibility, define constants that resolve dynamically
SCOPE_DATABASE_CHAT = 'DATABASE_CHAT'  # Will be resolved in require_scope
SCOPE_DATABASE_ADMIN = 'DATABASE_ADMIN'  # Will be resolved in require_scope

def verify_jwt_token(token):
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'No valid token provided'}), 401
        
        token = auth_header.split(' ')[1]
        payload = verify_jwt_token(token)
        
        if not payload:
            return jsonify({'error': 'Invalid or expired token'}), 401

        # Get user from database
        user = User.query.get(payload['user_id'])
        if not user:
            return jsonify({'error': 'User not found'}), 401

        # Add user to request object for use in route
        request.user = user
        return f(*args, **kwargs)

    return decorated_function

def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        auth_header = request.headers.get('Authorization')
        
        if not auth_header or not auth_header.startswith('Bearer '):
            return jsonify({'error': 'No valid token provided'}), 401
        
        token = auth_header.split(' ')[1]
        payload = verify_jwt_token(token)
        
        if not payload:
            return jsonify({'error': 'Invalid or expired token'}), 401

        if not payload.get('is_admin'):
            return jsonify({'error': 'Admin access required'}), 403

        # Get user from database
        user = User.query.get(payload['user_id'])
        if not user or not user.is_admin:
            return jsonify({'error': 'Admin access required'}), 403

        # Add user to request object for use in route
        request.user = user
        return f(*args, **kwargs)

    return decorated_function

def require_scope(scope_id):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = User.query.get(request.user.id)

            # Resolve scope ID if it's a string (scope name)
            resolved_scope_id = scope_id
            scope_name = None

            if isinstance(scope_id, str):
                # If scope_id is a string (scope name), resolve it to ID
                scope_name = scope_id
                resolved_scope_id = get_scope_id(scope_id)
            elif scope_id == 10:
                # Legacy hardcoded DATABASE_CHAT scope
                scope_name = 'DATABASE_CHAT'
                resolved_scope_id = get_scope_id('DATABASE_CHAT')
            elif scope_id == 11:
                # Legacy hardcoded DATABASE_ADMIN scope
                scope_name = 'DATABASE_ADMIN'
                resolved_scope_id = get_scope_id('DATABASE_ADMIN')

            if not user or not user.scopes or resolved_scope_id not in user.scopes:
                # Create error message based on scope
                if scope_name == 'DATABASE_CHAT' or scope_id == 10:
                    error_message = "You need DATABASE_CHAT permission to chat with databases"
                elif scope_name == 'DATABASE_ADMIN' or scope_id == 11:
                    error_message = "You need DATABASE_ADMIN permission to manage databases"
                else:
                    # Static scope messages for non-database scopes
                    scope_messages = {
                        SCOPE_CHAT: "You need CHAT permission to access this feature",
                        SCOPE_UPLOAD_FILE: "You need UPLOAD_FILE permission to upload files",
                        SCOPE_REMOVE_FILE: "You need REMOVE_FILE permission to remove files",
                        SCOPE_VIEW_FILE: "You need VIEW_FILE permission to view files",
                        SCOPE_FGD_OWNER: "You need FGD_OWNER permission for this operation",
                        SCOPE_CREATE_THEME: "You need CREATE_THEME permission to create themes",
                        SCOPE_CREATE_FGD: "You need CREATE_FGD permission to create FGDs",
                        SCOPE_DELETE_FGD: "You need DELETE_FGD permission to delete FGDs",
                        SCOPE_CHAT_FGD: "You need CHAT_FGD permission to access FGD chat"
                    }
                    error_message = scope_messages.get(resolved_scope_id, "You do not have the required permissions for this operation")

                return jsonify({
                    'error': 'Unauthorized',
                    'message': error_message
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator