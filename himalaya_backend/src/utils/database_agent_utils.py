import json
import logging
from typing import Dict, Any, List, Optional
from openai import AzureOpenAI
from utils.database_utils import db_connection_manager
from services.database_visualization_service import database_visualization_service
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)

logger = logging.getLogger(__name__)

class DatabaseAgent:
    """Agentic system for database chat with planner, executor, answer maker, QA components, and reloop mechanism"""
    
    def __init__(self):
        self.client = AzureOpenAI(
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT
        )
        self.deployment_name = AZURE_OPENAI_DEPLOYMENT_NAME
        self.max_reloop_attempts = 3  # Maximum number of reloop attempts
    
    def planner(self, question: str, db_schema: Dict, previous_messages: List, permission_level: str) -> Dict[str, Any]:
        """
        Planner component: Analyzes the question and creates an execution plan
        """
        try:
            # Build context from previous messages
            conversation_context = ""
            if previous_messages:
                conversation_context = "\n\nPrevious conversation:\n"
                for msg in reversed(previous_messages[-3:]):  # Last 3 messages
                    conversation_context += f"Q: {msg.question}\nA: {msg.answer}\n\n"
            
            # Build schema context
            schema_context = self._build_schema_context(db_schema)
            
            # Permission context
            permission_context = f"User permission level: {permission_level}"
            if permission_level == 'read':
                permission_context += " (Only SELECT queries allowed)"
            else:
                permission_context += " (Only SELECT queries allowed - this is a read-only chat system)"
            
            planner_prompt = f"""
You are a database query planner. Your job is to analyze a user's question and create a step-by-step execution plan.

{permission_context}

Database Schema:
{schema_context}

{conversation_context}

User Question: {question}

Create a detailed execution plan with the following structure:
1. Analyze what the user is asking for
2. Identify which tables/columns are needed
3. Plan the SQL queries needed (step by step)
4. Consider any data transformations or calculations needed
5. Plan how to present the results
6. Determine if visualization would be helpful (charts, graphs, etc.)

Respond in JSON format:
{{
    "analysis": "What the user is asking for",
    "tables_needed": ["table1", "table2"],
    "execution_steps": [
        {{
            "step": 1,
            "description": "Description of what this step does",
            "sql_query": "SELECT statement or other SQL",
            "purpose": "Why this query is needed"
        }}
    ],
    "expected_output": "Description of expected results",
    "complexity": "simple|medium|complex",
    "visualization_needed": true/false,
    "suggested_chart_type": "bar|line|pie|scatter|area|none",
    "visualization_reasoning": "Why visualization would be helpful"
}}

Important:
- ONLY generate SELECT queries - this is a read-only chat system
- NO INSERT, UPDATE, DELETE, or any data modification operations allowed
- Break complex questions into multiple simple SELECT queries
- Consider performance and limit results when appropriate
- If the question is unclear, plan to ask for clarification
- Consider if the user would benefit from seeing the data visualized
"""
            
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a database query planner. Always respond with valid JSON only. Do not use markdown formatting or code blocks."},
                    {"role": "user", "content": planner_prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            plan_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from markdown code blocks if present
            import re
            
            # Check if response is wrapped in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*\n(.*?)\n```', plan_text, re.DOTALL)
            if json_match:
                plan_text = json_match.group(1).strip()
                logger.debug("Extracted JSON from markdown code block")
            
            # Try to parse JSON response
            try:
                plan = json.loads(plan_text)
                return {
                    'success': True,
                    'plan': plan,
                    'token_usage': {
                        'prompt_tokens': response.usage.prompt_tokens,
                        'completion_tokens': response.usage.completion_tokens,
                        'total_tokens': response.usage.total_tokens
                    }
                }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse planner JSON response: {e}")
                logger.error(f"Raw response: {plan_text}")
                return {
                    'success': False,
                    'error': f'Failed to parse execution plan: {str(e)}',
                    'raw_response': plan_text
                }
                
        except Exception as e:
            logger.error(f"Planner error: {str(e)}")
            return {
                'success': False,
                'error': f'Planner failed: {str(e)}'
            }
    
    def executor(self, plan: Dict, db_config: Dict, allowed_schemas: Optional[List[str]] = None, db_schema: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Executor component: Executes the planned SQL queries with reloop mechanism for schema fixes
        """
        try:
            execution_results = []
            all_queries = []
            reloop_attempts = 0
            
            # Use passed allowed_schemas parameter
            
            while reloop_attempts <= self.max_reloop_attempts:
                execution_results = []
                all_queries = []
                failed_queries = []
                
                for step in plan.get('execution_steps', []):
                    sql_query = step.get('sql_query', '').strip()
                    if not sql_query:
                        continue
                    
                    # Validate query safety
                    is_safe, safety_error = db_connection_manager.validate_query_safety(sql_query)
                    if not is_safe:
                        execution_results.append({
                            'step': step.get('step'),
                            'query': sql_query,
                            'success': False,
                            'error': f'Query safety check failed: {safety_error}'
                        })
                        continue
                    
                    # Check schema permissions if user has restricted access
                    if allowed_schemas is not None:
                        schema_violation = self._check_schema_permissions(sql_query, allowed_schemas)
                        if schema_violation:
                            failed_queries.append({
                                'step': step.get('step'),
                                'query': sql_query,
                                'error': schema_violation
                            })
                            execution_results.append({
                                'step': step.get('step'),
                                'query': sql_query,
                                'success': False,
                                'error': f'Schema permission denied: {schema_violation}'
                            })
                            continue
                    
                    # Execute the query
                    query_result = db_connection_manager.execute_query(db_config, sql_query)
                    all_queries.append(sql_query)
                    
                    execution_results.append({
                        'step': step.get('step'),
                        'description': step.get('description'),
                        'query': sql_query,
                        'success': query_result['success'],
                        'data': query_result.get('data', []),
                        'row_count': query_result.get('row_count', 0),
                        'error': query_result.get('error')
                    })
                    
                    # If a query fails, collect it for potential fixing
                    if not query_result['success']:
                        failed_queries.append({
                            'step': step.get('step'),
                            'query': sql_query,
                            'error': query_result.get('error', 'Unknown error')
                        })
                
                # If no failed queries, we're done
                if not failed_queries:
                    break
                
                # If we have failed queries and this is not the last attempt, try to fix them
                if reloop_attempts < self.max_reloop_attempts and allowed_schemas and db_schema:
                    logger.info(f"Attempting to fix {len(failed_queries)} failed queries (attempt {reloop_attempts + 1})")
                    
                    # Try to fix the queries
                    fix_result = self.query_fixer(failed_queries, allowed_schemas, db_schema)
                    
                    if fix_result['success'] and fix_result.get('fixed_queries'):
                        # Update the plan with fixed queries
                        fixed_queries = {item['original_query']: item['fixed_query'] for item in fix_result['fixed_queries']}
                        
                        for step in plan.get('execution_steps', []):
                            original_query = step.get('sql_query', '').strip()
                            if original_query in fixed_queries:
                                step['sql_query'] = fixed_queries[original_query]
                                logger.info(f"Fixed query: {original_query[:50]}... -> {fixed_queries[original_query][:50]}...")
                        
                        reloop_attempts += 1
                        continue
                    else:
                        logger.warning(f"Failed to fix queries: {fix_result.get('error', 'Unknown error')}")
                        break
                else:
                    # No more attempts or no schema info available
                    break
            
            return {
                'success': True,
                'execution_results': execution_results,
                'all_queries': all_queries,
                'reloop_attempts': reloop_attempts
            }
            
        except Exception as e:
            logger.error(f"Executor error: {str(e)}")
            return {
                'success': False,
                'error': f'Executor failed: {str(e)}'
            }
    
    def query_fixer(self, failed_queries: List[Dict], allowed_schemas: List[str], db_schema: Dict) -> Dict[str, Any]:
        """
        Query fixer component: Analyzes failed queries and fixes them by adding schema prefixes
        """
        try:
            # Build context from failed queries
            failed_queries_context = ""
            for i, query_info in enumerate(failed_queries):
                failed_queries_context += f"Query {i+1}:\n"
                failed_queries_context += f"SQL: {query_info['query']}\n"
                failed_queries_context += f"Error: {query_info['error']}\n\n"
            
            # Build schema context
            schema_context = self._build_schema_context(db_schema)
            
            fixer_prompt = f"""
You are a SQL query fixer. Your job is to fix failed SQL queries by adding proper schema prefixes.

Allowed schemas: {allowed_schemas}

Database Schema:
{schema_context}

Failed Queries:
{failed_queries_context}

Instructions:
1. Analyze each failed query and identify which tables need schema prefixes
2. Add the appropriate schema prefix (from allowed_schemas) to each table name
3. Use the first allowed schema if multiple are available (usually 'public')
4. Preserve the original query structure and logic
5. Only fix schema-related issues, don't change the query logic

Respond in JSON format:
{{
    "fixed_queries": [
        {{
            "original_query": "original SQL query",
            "fixed_query": "SQL query with schema prefixes added",
            "changes_made": "Description of what was changed"
        }}
    ],
    "analysis": "Summary of what was fixed"
}}

Important:
- ONLY add schema prefixes to table names
- Use the first allowed schema (usually 'public')
- Preserve all original query logic and structure
- Don't change column names, only table names
- Ensure the fixed queries are valid SQL
"""
            
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a SQL query fixer. Always respond with valid JSON only. Do not use markdown formatting or code blocks."},
                    {"role": "user", "content": fixer_prompt}
                ],
                temperature=0.1,
                max_tokens=1500
            )
            
            fixer_text = response.choices[0].message.content.strip()
            
            # Try to extract JSON from markdown code blocks if present
            import re
            
            # Check if response is wrapped in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*\n(.*?)\n```', fixer_text, re.DOTALL)
            if json_match:
                fixer_text = json_match.group(1).strip()
                logger.debug("Extracted JSON from markdown code block")
            
            # Try to parse JSON response
            try:
                fix_result = json.loads(fixer_text)
                return {
                    'success': True,
                    'fixed_queries': fix_result.get('fixed_queries', []),
                    'analysis': fix_result.get('analysis', ''),
                    'token_usage': {
                        'prompt_tokens': response.usage.prompt_tokens,
                        'completion_tokens': response.usage.completion_tokens,
                        'total_tokens': response.usage.total_tokens
                    }
                }
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse query fixer JSON response: {e}")
                logger.error(f"Raw response: {fixer_text}")
                return {
                    'success': False,
                    'error': f'Failed to parse query fixer response: {str(e)}',
                    'raw_response': fixer_text
                }
                
        except Exception as e:
            logger.error(f"Query fixer error: {str(e)}")
            return {
                'success': False,
                'error': f'Query fixer failed: {str(e)}'
            }
    
    def _check_schema_permissions(self, sql_query: str, allowed_schemas: List[str]) -> Optional[str]:
        """
        Check if a SQL query only accesses allowed schemas
        """
        try:
            import re
            
            # Convert query to uppercase for case-insensitive matching
            query_upper = sql_query.upper()
            
            # Extract table names from the query
            # This is a simplified approach - in production, you might want a more robust SQL parser
            table_patterns = [
                r'FROM\s+([A-Z_][A-Z0-9_]*\.?[A-Z_][A-Z0-9_]*)',  # FROM table or schema.table
                r'JOIN\s+([A-Z_][A-Z0-9_]*\.?[A-Z_][A-Z0-9_]*)',  # JOIN table or schema.table
                r'UPDATE\s+([A-Z_][A-Z0-9_]*\.?[A-Z_][A-Z0-9_]*)',  # UPDATE table or schema.table
                r'INTO\s+([A-Z_][A-Z0-9_]*\.?[A-Z_][A-Z0-9_]*)',  # INSERT INTO table or schema.table
            ]
            
            found_tables = set()
            for pattern in table_patterns:
                matches = re.findall(pattern, query_upper)
                for match in matches:
                    found_tables.add(match.strip())
            
            # Check each found table against allowed schemas
            for table in found_tables:
                if '.' in table:
                    # Table has schema prefix (schema.table)
                    schema_name = table.split('.')[0]
                    if schema_name not in [s.upper() for s in allowed_schemas]:
                        return f"Access to schema '{schema_name}' not allowed. Allowed schemas: {allowed_schemas}"
                else:
                    # Table without schema prefix - check if it's in any allowed schema
                    # For now, we'll be conservative and require explicit schema prefixes
                    return f"Table '{table}' must be prefixed with an allowed schema. Allowed schemas: {allowed_schemas}"
            
            return None  # No violations found
            
        except Exception as e:
            logger.error(f"Error checking schema permissions: {str(e)}")
            return f"Error checking schema permissions: {str(e)}"
    
    def answer_maker(self, question: str, plan: Dict, execution_results: List, previous_messages: List) -> Dict[str, Any]:
        """
        Answer maker component: Creates a natural language answer from query results
        """
        try:
            # Build context from execution results
            results_context = ""
            for result in execution_results:
                if result['success'] and result.get('data'):
                    results_context += f"Query: {result['query']}\n"
                    results_context += f"Results ({result['row_count']} rows):\n"
                    
                    # Show first few rows
                    for i, row in enumerate(result['data'][:5]):
                        results_context += f"  Row {i+1}: {row}\n"
                    
                    if result['row_count'] > 5:
                        results_context += f"  ... and {result['row_count'] - 5} more rows\n"
                    results_context += "\n"
                elif not result['success']:
                    results_context += f"Query failed: {result['query']}\nError: {result['error']}\n\n"
            
            # Build conversation context
            conversation_context = ""
            if previous_messages:
                conversation_context = "\n\nPrevious conversation:\n"
                for msg in reversed(previous_messages[-2:]):  # Last 2 messages
                    conversation_context += f"Q: {msg.question}\nA: {msg.answer}\n\n"
            
            # Check if visualization is planned
            visualization_info = ""
            if plan.get('visualization_needed', False):
                suggested_chart = plan.get('suggested_chart_type', 'bar')
                visualization_info = f"\n\nVisualization: The system will generate a {suggested_chart} chart to help visualize the results."
            
            answer_prompt = f"""
You are a database assistant. Create a helpful, natural language answer based on the query results.

Original Question: {question}

Execution Plan Analysis: {plan.get('analysis', '')}

Query Results:
{results_context}

{conversation_context}

{visualization_info}

Instructions:
1. Provide a clear, conversational answer to the user's question
2. Summarize the key findings from the data
3. If there are multiple results, organize them logically
4. If queries failed, explain what went wrong and suggest alternatives
5. Be concise but informative
6. Use natural language, not technical jargon
7. If no data was found, explain this clearly
8. If visualization is being generated, mention it briefly in your answer

Create a helpful response that directly answers the user's question.
"""
            
            response = self.client.chat.completions.create(
                model=self.deployment_name,
                messages=[
                    {"role": "system", "content": "You are a helpful database assistant. Provide clear, conversational answers based on query results."},
                    {"role": "user", "content": answer_prompt}
                ],
                temperature=0.3,
                max_tokens=1000
            )
            
            answer = response.choices[0].message.content.strip()
            
            return {
                'success': True,
                'answer': answer,
                'token_usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }
            
        except Exception as e:
            logger.error(f"Answer maker error: {str(e)}")
            return {
                'success': False,
                'error': f'Answer maker failed: {str(e)}',
                'answer': 'I encountered an error while generating the answer. Please try again.'
            }
    
    def qa_component(self, question: str, answer: str, execution_results: List) -> Dict[str, Any]:
        """
        QA component: Reviews the answer quality and suggests improvements
        """
        try:
            # Check if execution was successful
            successful_queries = [r for r in execution_results if r['success']]
            failed_queries = [r for r in execution_results if not r['success']]
            
            # Basic quality checks
            quality_score = 100
            feedback = []
            
            if failed_queries:
                quality_score -= 30
                feedback.append(f"Some queries failed: {len(failed_queries)} out of {len(execution_results)}")
            
            if not successful_queries:
                quality_score -= 50
                feedback.append("No queries executed successfully")
            
            if len(answer) < 50:
                quality_score -= 20
                feedback.append("Answer might be too brief")
            
            # Check if answer addresses the question
            if question.lower() in answer.lower() or any(word in answer.lower() for word in question.lower().split()[:3]):
                feedback.append("Answer appears to address the question")
            else:
                quality_score -= 25
                feedback.append("Answer might not fully address the question")
            
            # Determine if answer needs improvement
            needs_improvement = quality_score < 70
            
            return {
                'success': True,
                'quality_score': max(0, quality_score),
                'feedback': feedback,
                'needs_improvement': needs_improvement,
                'successful_queries': len(successful_queries),
                'failed_queries': len(failed_queries)
            }
            
        except Exception as e:
            logger.error(f"QA component error: {str(e)}")
            return {
                'success': False,
                'error': f'QA component failed: {str(e)}',
                'quality_score': 0,
                'needs_improvement': True
            }
    
    def _build_schema_context(self, schema: Dict) -> str:
        """Build a readable schema context for the AI"""
        context = ""
        
        # Tables
        if schema.get('tables'):
            context += "Tables:\n"
            for table_name, table_info in schema['tables'].items():
                context += f"  {table_name}:\n"
                for column in table_info.get('columns', []):
                    context += f"    - {column['name']} ({column['type']})\n"
                context += "\n"
        
        # Views
        if schema.get('views'):
            context += "Views:\n"
            for view_name, view_info in schema['views'].items():
                context += f"  {view_name}:\n"
                for column in view_info.get('columns', []):
                    context += f"    - {column['name']} ({column['type']})\n"
                context += "\n"
        
        return context

# Global instance
database_agent = DatabaseAgent()

def process_database_question(question: str, db_config: Dict, previous_messages: List, 
                            permission_info: Dict, user_id: int) -> Dict[str, Any]:
    """
    Main function to process a database question using the agentic system
    """
    try:
        # Get database schema
        schema_result = db_connection_manager.get_database_schema(db_config)
        if not schema_result.get('success'):
            return {
                'answer': 'I could not access the database schema. Please check the connection.',
                'error_message': schema_result.get('error')
            }
        
        schema = schema_result['schema']
        
        # Extract permission info
        permission_level = permission_info.get('permission_level', 'read')
        allowed_schemas = permission_info.get('allowed_schemas')
        
        # Step 1: Planning
        plan_result = database_agent.planner(question, schema, previous_messages, permission_level)
        if not plan_result['success']:
            return {
                'answer': 'I had trouble understanding your question. Could you please rephrase it?',
                'error_message': plan_result.get('error')
            }
        
        plan = plan_result['plan']
        
        # Step 2: Execution
        execution_result = database_agent.executor(plan, db_config, allowed_schemas, schema)
        if not execution_result['success']:
            return {
                'answer': 'I encountered an error while executing the database queries.',
                'error_message': execution_result.get('error'),
                'execution_plan': plan
            }
        
        execution_results = execution_result['execution_results']
        reloop_attempts = execution_result.get('reloop_attempts', 0)
        
        # Step 3: Answer Generation
        answer_result = database_agent.answer_maker(question, plan, execution_results, previous_messages)
        if not answer_result['success']:
            return {
                'answer': 'I found the data but had trouble generating a response. Please try again.',
                'error_message': answer_result.get('error'),
                'execution_plan': plan,
                'query_results': execution_results
            }
        
        # Step 4: Quality Assurance
        qa_result = database_agent.qa_component(question, answer_result['answer'], execution_results)
        
        # Step 5: Visualization Generation (NEW)
        visualization_result = None
        
        # Debug visualization decision
        plan_visualization_needed = plan.get('visualization_needed', False)
        service_visualization_needed = database_visualization_service.should_generate_visualization(question, execution_results)
        
        logger.info(f"Visualization decision - Plan says: {plan_visualization_needed}, Service says: {service_visualization_needed.get('should_generate', False) if isinstance(service_visualization_needed, dict) else service_visualization_needed}")
        
        if plan_visualization_needed or (isinstance(service_visualization_needed, dict) and service_visualization_needed.get('should_generate', False)):
            logger.info("Attempting to generate visualization...")
            try:
                visualization_result = database_visualization_service.generate_visualization(
                    question, 
                    execution_results,
                    previous_messages,
                    {'user_id': user_id, 'permission_level': permission_level, 'allowed_schemas': allowed_schemas}
                )
                logger.info(f"Visualization generation result: {visualization_result is not None}")
                if visualization_result:
                    logger.info(f"Visualization type: {visualization_result.get('visualization_type')}")
                else:
                    logger.warning("Visualization generation returned None")
            except Exception as e:
                logger.error(f"Error generating visualization: {str(e)}")
                visualization_result = None
        else:
            logger.info("Visualization not needed based on plan and service decision")
        
        # Combine token usage (including potential query fixer usage)
        total_token_usage = {
            'prompt_tokens': plan_result.get('token_usage', {}).get('prompt_tokens', 0) + 
                           answer_result.get('token_usage', {}).get('prompt_tokens', 0),
            'completion_tokens': plan_result.get('token_usage', {}).get('completion_tokens', 0) + 
                               answer_result.get('token_usage', {}).get('completion_tokens', 0),
            'total_tokens': plan_result.get('token_usage', {}).get('total_tokens', 0) + 
                          answer_result.get('token_usage', {}).get('total_tokens', 0)
        }
        
        # Add query fixer token usage if reloop was used
        if reloop_attempts > 0:
            # Note: We don't have direct access to query fixer tokens here,
            # but they would be included in the total if we tracked them
            logger.info(f"Query fixer was used {reloop_attempts} times to fix schema issues")
        
        # Prepare response with visualization data
        response = {
            'answer': answer_result['answer'],
            'sql_queries': execution_result.get('all_queries', []),
            'query_results': execution_results,
            'execution_plan': plan,
            'qa_feedback': qa_result,
            'token_usage': total_token_usage,
            'reloop_attempts': reloop_attempts
        }
        
        # Add visualization data if available
        if visualization_result:
            response.update({
                'visualization_data': visualization_result.get('visualization_data'),
                'visualization_type': visualization_result.get('visualization_type'),
                'visualization_image': visualization_result.get('visualization_image'),
                'visualization_metadata': visualization_result.get('visualization_metadata')
            })
        
        return response
        
    except Exception as e:
        logger.error(f"Database question processing error: {str(e)}")
        return {
            'answer': 'I encountered an unexpected error while processing your question. Please try again.',
            'error_message': str(e)
        }
