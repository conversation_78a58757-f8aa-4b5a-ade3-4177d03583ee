import os
import json
import logging
import base64
import urllib.parse
import traceback
from typing import Dict, Any, List, Optional, Tuple
from cryptography.fernet import Fernet
import sqlalchemy as sa
from sqlalchemy import create_engine, text, MetaData, inspect
from sqlalchemy.exc import SQLAlchemyError
try:
    import pymysql
except ImportError:
    pymysql = None
try:
    import psycopg2
except ImportError:
    psycopg2 = None
try:
    import pyodbc
except ImportError:
    pyodbc = None

logger = logging.getLogger(__name__)

class DatabaseConnectionManager:
    """Manages secure connections to external databases"""

    def __init__(self):
        # Initialize encryption for storing database credentials
        # Generate a key if not provided (for development only)
        try:
            from config.settings import DATABASE_ENCRYPTION_KEY
            if DATABASE_ENCRYPTION_KEY == 'your-32-byte-base64-encoded-key-here-for-database-encryption':
                # Generate a new key for development
                key = Fernet.generate_key()
                self.cipher_suite = Fernet(key)
                logger.warning("Using auto-generated encryption key. In production, set DATABASE_ENCRYPTION_KEY properly.")
            else:
                # Use provided key
                if isinstance(DATABASE_ENCRYPTION_KEY, str):
                    # If it's a string, try to decode it as base64, otherwise use as-is
                    try:
                        key = base64.urlsafe_b64decode(DATABASE_ENCRYPTION_KEY.encode())
                        self.cipher_suite = Fernet(base64.urlsafe_b64encode(key))
                    except:
                        # If decoding fails, generate a new key
                        key = Fernet.generate_key()
                        self.cipher_suite = Fernet(key)
                        logger.warning("Invalid encryption key format. Using auto-generated key.")
                else:
                    self.cipher_suite = Fernet(DATABASE_ENCRYPTION_KEY)
        except ImportError:
            # Fallback if settings not available
            key = Fernet.generate_key()
            self.cipher_suite = Fernet(key)
            logger.warning("DATABASE_ENCRYPTION_KEY not found in settings. Using auto-generated key.")
    
    def encrypt_password(self, password: str) -> str:
        """Encrypt database password for secure storage"""
        return self.cipher_suite.encrypt(password.encode()).decode()
    
    def decrypt_password(self, encrypted_password: str) -> str:
        """Decrypt database password for connection"""
        return self.cipher_suite.decrypt(encrypted_password.encode()).decode()
    
    def build_connection_string(self, db_config: Dict[str, Any]) -> str:
        """Build connection string based on database type"""
        db_type = db_config['db_type'].lower()
        host = db_config['host']
        port = db_config['port']
        database = db_config['database_name']
        username = db_config['username']
        password = self.decrypt_password(db_config['password_encrypted'])
        
        # URL-encode username and password to handle special characters
        encoded_username = urllib.parse.quote_plus(username)
        encoded_password = urllib.parse.quote_plus(password)
        
        additional_params = db_config.get('additional_params', {})
        
        # Handle SSL configuration
        ssl_enabled = db_config.get('ssl_enabled', False)
        
        # Filter out parameters that shouldn't be in connection string
        connection_params = {}
        excluded_params = [
            'schema', 'search_path', 'allowed_schemas', 'permission_level', 
            'expires_at', 'granted_by', 'created_at', 'updated_at'
        ]
        for k, v in additional_params.items():
            if k not in excluded_params:  # Exclude permission and schema-related params
                connection_params[k] = v
        
        # Handle SSL parameters properly to avoid duplication
        if db_type == 'postgresql':
            # For PostgreSQL, handle sslmode properly
            if ssl_enabled and 'sslmode' not in connection_params:
                connection_params['sslmode'] = 'require'
            elif not ssl_enabled and 'sslmode' not in connection_params:
                connection_params['sslmode'] = 'prefer'
        elif db_type == 'mysql':
            # For MySQL, handle ssl properly
            if ssl_enabled and 'ssl' not in connection_params:
                connection_params['ssl'] = 'true'
        
        extra_params = "&".join([f"{k}={v}" for k, v in connection_params.items()]) if connection_params else ""
        
        if db_type == 'postgresql':
            conn_str = f"postgresql://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
            if extra_params:
                conn_str += "?" + extra_params
        
        elif db_type == 'mysql':
            conn_str = f"mysql+pymysql://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
            if extra_params:
                conn_str += "?" + extra_params
        
        elif db_type == 'sqlserver':
            # SQL Server connection string
            driver = additional_params.get('driver', 'ODBC Driver 17 for SQL Server')
            conn_str = f"mssql+pyodbc://{encoded_username}:{encoded_password}@{host}:{port}/{database}?driver={driver}"
            if extra_params:
                conn_str += "&" + extra_params
        
        elif db_type == 'oracle':
            # Oracle connection string
            conn_str = f"oracle+cx_oracle://{encoded_username}:{encoded_password}@{host}:{port}/{database}"
            if extra_params:
                conn_str += "?" + extra_params
        
        elif db_type == 'sqlite':
            # SQLite connection (for testing)
            conn_str = f"sqlite:///{database}"
        
        else:
            raise ValueError(f"Unsupported database type: {db_type}")
        
        return conn_str
    
    def test_connection(self, db_config: Dict[str, Any]) -> Tuple[bool, Optional[str]]:
        """Test database connection and return success status and error message if any"""
        try:
            connection_string = self.build_connection_string(db_config)
            engine = create_engine(connection_string, pool_timeout=10, pool_recycle=3600)
            
            # Test the connection
            with engine.connect() as conn:
                # Set schema if specified (PostgreSQL specific)
                schema = db_config.get('additional_params', {}).get('schema')
                if schema and db_config['db_type'].lower() == 'postgresql':
                    try:
                        conn.execute(text(f"SET search_path TO {schema}, public"))
                        logger.debug(f"Set search_path to {schema} for PostgreSQL test connection")
                    except Exception as e:
                        logger.warning(f"Failed to set search_path to {schema}: {str(e)}")
                
                # Simple test query based on database type
                db_type = db_config['db_type'].lower()
                if db_type == 'postgresql':
                    result = conn.execute(text("SELECT 1"))
                elif db_type == 'mysql':
                    result = conn.execute(text("SELECT 1"))
                elif db_type == 'sqlserver':
                    result = conn.execute(text("SELECT 1"))
                elif db_type == 'oracle':
                    result = conn.execute(text("SELECT 1 FROM DUAL"))
                elif db_type == 'sqlite':
                    result = conn.execute(text("SELECT 1"))
                else:
                    result = conn.execute(text("SELECT 1"))
                
                # Fetch result to ensure query executed successfully
                result.fetchone()
            
            engine.dispose()
            return True, None
            
        except Exception as e:
            logger.error(f"Database connection test failed: {str(e)}")
            return False, str(e)
    
    def get_connection(self, db_config: Dict[str, Any]):
        """Get a database connection"""
        try:
            connection_string = self.build_connection_string(db_config)
            engine = create_engine(
                connection_string, 
                pool_timeout=30, 
                pool_recycle=3600,
                pool_pre_ping=True,  # Verify connections before use
                echo=False  # Set to True for SQL debugging
            )
            connection = engine.connect()
            
            # Set schema if specified (PostgreSQL specific)
            schema = db_config.get('additional_params', {}).get('schema')
            if schema and db_config['db_type'].lower() == 'postgresql':
                try:
                    connection.execute(text(f"SET search_path TO {schema}, public"))
                    logger.debug(f"Set search_path to {schema} for PostgreSQL connection")
                except Exception as e:
                    logger.warning(f"Failed to set search_path to {schema}: {str(e)}")
            
            return connection
        except Exception as e:
            logger.error(f"Failed to create database connection: {str(e)}")
            raise
    
    def execute_query(self, db_config: Dict[str, Any], query: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Execute a SQL query and return results"""
        connection = None
        try:
            connection = self.get_connection(db_config)
            
            # Execute the query
            if params:
                result = connection.execute(text(query), params)
            else:
                result = connection.execute(text(query))
            
            # Handle different types of queries
            if result.returns_rows:
                # SELECT query - fetch results
                columns = list(result.keys())
                rows = result.fetchall()
                data = [dict(zip(columns, row)) for row in rows]
                
                return {
                    'success': True,
                    'data': data,
                    'columns': columns,
                    'row_count': len(data),
                    'query': query
                }
            else:
                # INSERT/UPDATE/DELETE query
                return {
                    'success': True,
                    'affected_rows': result.rowcount,
                    'query': query
                }
                
        except Exception as e:
            logger.error(f"Query execution failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'query': query
            }
        finally:
            if connection:
                connection.close()
    
    def _serialize_schema_data(self, data):
        """Convert schema data to JSON-serializable format"""
        if isinstance(data, dict):
            return {k: self._serialize_schema_data(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._serialize_schema_data(item) for item in data]
        elif hasattr(data, '__str__'):
            # Convert SQLAlchemy types and other objects to strings
            return str(data)
        else:
            return data

    def get_database_schema(self, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """Get database schema information (tables, columns, etc.)"""
        connection = None
        try:
            logger.debug(f"Starting schema retrieval for {db_config['db_type']} database")
            
            # Test connection first
            success, error = self.test_connection(db_config)
            if not success:
                logger.error(f"Connection test failed before schema retrieval: {error}")
                return {
                    'success': False,
                    'error': f"Connection failed: {error}"
                }
            
            connection = self.get_connection(db_config)
            logger.debug("Database connection established")
            
            inspector = inspect(connection.engine)
            logger.debug("SQLAlchemy inspector created")
            
            schema_info = {
                'tables': {},
                'views': {}
            }
            
            # Check if specific schema is requested
            schema_name = db_config.get('additional_params', {}).get('schema')
            logger.debug(f"Schema name requested: {schema_name}")
            
            try:
                # Get table information
                if schema_name:
                    logger.debug(f"Retrieving tables from schema: {schema_name}")
                    # Get tables from specific schema
                    table_names = inspector.get_table_names(schema=schema_name)
                    logger.debug(f"Found {len(table_names)} tables in schema {schema_name}")
                    
                    for table_name in table_names:
                        try:
                            columns = inspector.get_columns(table_name, schema=schema_name)
                            primary_keys = inspector.get_pk_constraint(table_name, schema=schema_name)
                            foreign_keys = inspector.get_foreign_keys(table_name, schema=schema_name)
                            indexes = inspector.get_indexes(table_name, schema=schema_name)
                            
                            schema_info['tables'][table_name] = {
                                'schema': schema_name,
                                'columns': self._serialize_schema_data(columns),
                                'primary_keys': self._serialize_schema_data(primary_keys),
                                'foreign_keys': self._serialize_schema_data(foreign_keys),
                                'indexes': self._serialize_schema_data(indexes)
                            }
                        except Exception as table_error:
                            logger.warning(f"Failed to get table info for {table_name}: {str(table_error)}")
                            # Continue with other tables
                    
                    # Get views from specific schema
                    try:
                        view_names = inspector.get_view_names(schema=schema_name)
                        logger.debug(f"Found {len(view_names)} views in schema {schema_name}")
                        
                        for view_name in view_names:
                            try:
                                columns = inspector.get_columns(view_name, schema=schema_name)
                                schema_info['views'][view_name] = {
                                    'schema': schema_name,
                                    'columns': self._serialize_schema_data(columns)
                                }
                            except Exception as view_error:
                                logger.warning(f"Failed to get view info for {view_name}: {str(view_error)}")
                    except Exception as e:
                        logger.warning(f"Could not retrieve view information for schema {schema_name}: {str(e)}")
                else:
                    logger.debug("Retrieving tables from all schemas")
                    # Get all tables from all schemas
                    table_names = inspector.get_table_names()
                    logger.debug(f"Found {len(table_names)} tables total")
                    
                    for table_name in table_names:
                        try:
                            columns = inspector.get_columns(table_name)
                            primary_keys = inspector.get_pk_constraint(table_name)
                            foreign_keys = inspector.get_foreign_keys(table_name)
                            indexes = inspector.get_indexes(table_name)
                            
                            schema_info['tables'][table_name] = {
                                'columns': self._serialize_schema_data(columns),
                                'primary_keys': self._serialize_schema_data(primary_keys),
                                'foreign_keys': self._serialize_schema_data(foreign_keys),
                                'indexes': self._serialize_schema_data(indexes)
                            }
                        except Exception as table_error:
                            logger.warning(f"Failed to get table info for {table_name}: {str(table_error)}")
                            # Continue with other tables
                    
                    # Get all views
                    try:
                        view_names = inspector.get_view_names()
                        logger.debug(f"Found {len(view_names)} views total")
                        
                        for view_name in view_names:
                            try:
                                columns = inspector.get_columns(view_name)
                                schema_info['views'][view_name] = {
                                    'columns': self._serialize_schema_data(columns)
                                }
                            except Exception as view_error:
                                logger.warning(f"Failed to get view info for {view_name}: {str(view_error)}")
                    except Exception as e:
                        logger.warning(f"Could not retrieve view information: {str(e)}")
                
                logger.debug(f"Schema retrieval completed. Tables: {len(schema_info['tables'])}, Views: {len(schema_info['views'])}")
                
                return {
                    'success': True,
                    'schema': schema_info,
                    'schema_name': schema_name
                }
                
            except Exception as inspection_error:
                logger.error(f"SQLAlchemy inspection failed: {str(inspection_error)}")
                return {
                    'success': False,
                    'error': f"Schema inspection failed: {str(inspection_error)}"
                }
            
        except Exception as e:
            logger.error(f"Failed to retrieve database schema: {str(e)}")
            logger.error(f"Exception type: {type(e).__name__}")
            logger.error(f"Exception details: {traceback.format_exc()}")
            return {
                'success': False,
                'error': str(e),
                'error_type': type(e).__name__
            }
        finally:
            if connection:
                try:
                    connection.close()
                    logger.debug("Database connection closed")
                except Exception as close_error:
                    logger.warning(f"Error closing connection: {str(close_error)}")
    
    def get_available_schemas(self, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """Get list of available schemas in a database"""
        connection = None
        try:
            connection = self.get_connection(db_config)
            inspector = inspect(connection.engine)
            
            # Get available schemas
            try:
                schemas = inspector.get_schema_names()
                schema_info = []
                
                for schema_name in schemas:
                    # Get table count for this schema
                    try:
                        table_count = len(inspector.get_table_names(schema=schema_name))
                    except:
                        table_count = 0
                    
                    # Get view count for this schema
                    try:
                        view_count = len(inspector.get_view_names(schema=schema_name))
                    except:
                        view_count = 0
                    
                    schema_info.append({
                        'name': schema_name,
                        'table_count': table_count,
                        'view_count': view_count,
                        'total_objects': table_count + view_count
                    })
                
                # Sort by total objects (most populated schemas first)
                schema_info.sort(key=lambda x: x['total_objects'], reverse=True)
                
                return {
                    'success': True,
                    'schemas': schema_info,
                    'total': len(schema_info)
                }
                
            except Exception as e:
                # Some databases don't support schema listing
                logger.warning(f"Could not retrieve schema list: {str(e)}")
                return {
                    'success': True,
                    'schemas': [],
                    'total': 0,
                    'message': 'Schema listing not supported for this database type'
                }
            
        except Exception as e:
            logger.error(f"Failed to retrieve available schemas: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
        finally:
            if connection:
                connection.close()
    
    def validate_query_safety(self, query: str) -> Tuple[bool, Optional[str]]:
        """Basic validation to ensure query safety (only SELECT queries allowed)"""
        query_upper = query.upper().strip()

        # Only allow SELECT queries and CTEs - this is a read-only chat system
        if not (query_upper.startswith('SELECT') or query_upper.startswith('WITH')):
            return False, "Only SELECT queries and CTEs (WITH statements) are allowed in this chat system"

        # List of dangerous keywords/operations (comprehensive list)
        # Check for exact SQL keywords using regex patterns to avoid false positives
        import re
        
        # Define dangerous SQL keywords with proper word boundaries
        dangerous_patterns = [
            r'\bDROP\b', r'\bDELETE\b', r'\bTRUNCATE\b', r'\bALTER\b', r'\bCREATE\b', 
            r'\bINSERT\b', r'\bUPDATE\b', r'\bGRANT\b', r'\bREVOKE\b', r'\bEXEC\b', 
            r'\bEXECUTE\b', r'\bCALL\b', r'\bMERGE\b', r'\bREPLACE\b', r'\bLOAD\b', 
            r'\bBULK\b', r'\bCOPY\b', r'\bIMPORT\b', r'\bEXPORT\b'
        ]

        # Check for dangerous keywords using regex with word boundaries
        for pattern in dangerous_patterns:
            if re.search(pattern, query_upper):
                keyword = pattern.replace(r'\b', '')
                return False, f"Query contains forbidden operation: {keyword}. Only SELECT queries are allowed."

        # Additional checks for SQL injection patterns
        # Note: UNION SELECT is actually safe and useful, so we allow it
        injection_patterns = [
            ';--',      # Comment after semicolon
            '/*',       # Block comment start
            '*/',       # Block comment end
            'OR 1=1',   # Classic injection
            'AND 1=1',  # Classic injection
            '--',       # SQL comments (could be used maliciously)
            'EXEC ',    # Execute commands
            'EXECUTE ', # Execute commands
            'SP_',      # Stored procedures
            'XP_'       # Extended procedures
        ]

        for pattern in injection_patterns:
            if pattern.upper() in query_upper:
                return False, f"Query contains potentially malicious pattern: {pattern}"

        # Check for dangerous multiple statement patterns (semicolon-separated)
        # Allow multiple SELECTs in unions, subqueries, and CTEs
        if ';' in query and query.strip().count(';') > 0:
            # Remove trailing semicolon if it's the only one
            if query.strip().endswith(';') and query.count(';') == 1:
                pass  # Single trailing semicolon is OK
            else:
                return False, "Multiple statements separated by semicolons are not allowed"

        return True, None

# Global instance
db_connection_manager = DatabaseConnectionManager()
