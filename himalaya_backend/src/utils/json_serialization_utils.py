"""
JSON Serialization Utilities

This module provides utilities for safely serializing Python objects to JSON,
handling datetime objects and other non-serializable types.
"""

import json
from datetime import datetime, date
from typing import Any, Dict, List, Union
import logging

logger = logging.getLogger(__name__)

def serialize_for_json(obj: Any) -> Any:
    """
    Recursively serialize an object for JSON storage, handling datetime objects
    and other non-serializable types.
    
    Args:
        obj: The object to serialize
        
    Returns:
        JSON-serializable version of the object
    """
    if obj is None:
        return None
    elif isinstance(obj, (str, int, float, bool)):
        return obj
    elif isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, date):
        return obj.isoformat()
    elif isinstance(obj, list):
        return [serialize_for_json(item) for item in obj]
    elif isinstance(obj, dict):
        return {key: serialize_for_json(value) for key, value in obj.items()}
    elif hasattr(obj, '__dict__'):
        # Handle objects with __dict__ attribute
        return serialize_for_json(obj.__dict__)
    else:
        # Convert to string for other types
        try:
            return str(obj)
        except Exception as e:
            logger.warning(f"Could not serialize object of type {type(obj)}: {e}")
            return None

def safe_json_dumps(obj: Any, **kwargs) -> str:
    """
    Safely convert an object to JSON string, handling non-serializable types.
    
    Args:
        obj: The object to convert to JSON
        **kwargs: Additional arguments to pass to json.dumps
        
    Returns:
        JSON string representation of the object
    """
    serialized_obj = serialize_for_json(obj)
    return json.dumps(serialized_obj, **kwargs)

def clean_response_for_storage(response: Dict[str, Any]) -> Dict[str, Any]:
    """
    Clean a response dictionary for database storage by ensuring all values
    are JSON serializable.
    
    Args:
        response: The response dictionary to clean
        
    Returns:
        Cleaned response dictionary with serializable values
    """
    try:
        cleaned = serialize_for_json(response)
        return cleaned
    except Exception as e:
        logger.error(f"Error cleaning response for storage: {e}")
        # Return a minimal cleaned version
        return {
            'answer': response.get('answer', 'Error processing response'),
            'error_message': f'Serialization error: {str(e)}',
            'sql_queries': [],
            'query_results': [],
            'execution_plan': None,
            'qa_feedback': None,
            'token_usage': None,
            'visualization_data': None,
            'visualization_type': None,
            'visualization_image': None,
            'visualization_metadata': None
        }

def validate_json_serializable(obj: Any) -> bool:
    """
    Check if an object is JSON serializable.
    
    Args:
        obj: The object to check
        
    Returns:
        True if the object is JSON serializable, False otherwise
    """
    try:
        json.dumps(obj)
        return True
    except (TypeError, ValueError):
        return False

def get_non_serializable_keys(obj: Dict[str, Any]) -> List[str]:
    """
    Find keys in a dictionary that contain non-serializable values.
    
    Args:
        obj: The dictionary to check
        
    Returns:
        List of keys with non-serializable values
    """
    non_serializable = []
    
    for key, value in obj.items():
        if not validate_json_serializable(value):
            non_serializable.append(key)
        elif isinstance(value, dict):
            # Recursively check nested dictionaries
            nested_non_serializable = get_non_serializable_keys(value)
            if nested_non_serializable:
                non_serializable.extend([f"{key}.{nested_key}" for nested_key in nested_non_serializable])
        elif isinstance(value, list):
            # Check list items
            for i, item in enumerate(value):
                if not validate_json_serializable(item):
                    non_serializable.append(f"{key}[{i}]")
    
    return non_serializable 