"""
Utility functions for handling HTTP requests more robustly
"""

from flask import request
import logging

logger = logging.getLogger(__name__)

def get_request_json(fallback=None):
    """
    Safely get JSON data from request, handling cases where Content-Type is not application/json
    
    Args:
        fallback: Default value to return if JSON parsing fails
        
    Returns:
        dict: Parsed JSON data or fallback value
    """
    try:
        return request.json or fallback or {}
    except Exception as e:
        # Handle case where Content-Type is not application/json
        logger.warning(f"Could not parse JSON from request: {str(e)}")
        logger.debug(f"Request Content-Type: {request.content_type}")
        logger.debug(f"Request headers: {dict(request.headers)}")
        return fallback or {}

def get_request_data():
    """
    Get request data in a flexible way - tries JSON first, then form data
    
    Returns:
        dict: Request data from JSON or form
    """
    # Try JSON first
    json_data = get_request_json()
    if json_data:
        return json_data
    
    # Fallback to form data
    try:
        form_data = request.form.to_dict()
        if form_data:
            return form_data
    except Exception as e:
        logger.warning(f"Could not parse form data: {str(e)}")
    
    # Final fallback
    return {}

def validate_required_fields(data, required_fields):
    """
    Validate that required fields are present in request data
    
    Args:
        data (dict): Request data
        required_fields (list): List of required field names
        
    Returns:
        tuple: (is_valid, missing_fields, error_message)
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or (isinstance(data[field], str) and not data[field].strip()):
            missing_fields.append(field)
    
    if missing_fields:
        error_message = f"Missing required fields: {', '.join(missing_fields)}"
        return False, missing_fields, error_message
    
    return True, [], None

def get_pagination_params():
    """
    Get pagination parameters from request with sensible defaults
    
    Returns:
        tuple: (limit, offset)
    """
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        # Ensure reasonable limits
        if limit < 1:
            limit = 50
        elif limit > 1000:
            limit = 1000
            
        if offset < 0:
            offset = 0
            
        return limit, offset
    except (ValueError, TypeError):
        return 50, 0 