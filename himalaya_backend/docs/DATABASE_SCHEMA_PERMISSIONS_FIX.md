# Database Schema Permissions - JSON Column Fix

## Issue Description

When trying to add the `allowed_schemas` column to the `user_database_permissions` table, you encountered this error:

```
AttributeError: JSONB
```

This error occurs because Flask-SQLAlchemy doesn't support `db.JSONB` directly. Flask-SQLAlchemy uses `db.JSON` for JSON columns.

## Root Cause

The original migration and model tried to use `db.JSONB` which is not available in Flask-SQLAlchemy:

```python
db.Column('allowed_schemas', db.JSONB, nullable=True)  # This doesn't work
```

Flask-SQLAlchemy only supports `db.JSON`:

```python
db.Column('allowed_schemas', db.JSON, nullable=True)  # This works
```

## Solutions

### Option 1: Use JSON with Simple Index (Recommended)

**Benefits:**
- Compatible with Flask-SQLAlchemy
- Simple setup
- No data type conversion needed
- Works with existing JSON columns

**Migration Files:**
1. `migrations/add_schema_permissions_final.sql` (updated)
2. `migrations/add_schema_permissions.sql` (updated)

**Steps:**
1. Run the updated `add_schema_permissions_final.sql` migration
2. The model already uses `db.JSON` which is correct

### Option 2: Use JSONB with Custom Type (Advanced)

**Benefits:**
- Better performance for JSON operations
- Full PostgreSQL JSONB features
- More efficient storage

**Implementation:**
- Requires custom SQLAlchemy type definition
- More complex setup
- Not recommended for this use case

## Implementation Details

### JSON Solution (Option 1)

**Migration:**
```sql
-- Add column as JSON
ALTER TABLE user_database_permissions 
ADD COLUMN allowed_schemas JSON DEFAULT NULL;

-- Create simple index
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
ON user_database_permissions (allowed_schemas);
```

**Model Definition:**
```python
db.Column('allowed_schemas', db.JSON, nullable=True)
```

### JSONB Solution (Option 2)

**Custom Type Definition:**
```python
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.types import TypeDecorator

class JSONBType(TypeDecorator):
    impl = JSONB

db.Column('allowed_schemas', JSONBType, nullable=True)
```

## Performance Comparison

| Feature | JSON | JSONB |
|---------|------|-------|
| Storage | Text format | Binary format |
| Indexing | Simple B-tree | Full GIN support |
| Query Performance | Good | Better |
| Storage Size | Larger | Smaller |
| Parsing | Slower | Faster |
| Flask-SQLAlchemy | Native support | Requires custom type |

## Usage Examples

### Setting Schema Permissions

```python
# Allow access to specific schemas
allowed_schemas = ['public', 'analytics', 'reporting']

# Allow access to all schemas (NULL)
allowed_schemas = None
```

### Querying Schema Permissions

```python
# Check if user has access to specific schema
def has_schema_access(user_id, database_id, schema_name):
    permission = db.session.query(user_database_permissions).filter_by(
        user_id=user_id,
        external_database_id=database_id
    ).first()
    
    if not permission:
        return False
    
    # NULL means all schemas are allowed
    if permission.allowed_schemas is None:
        return True
    
    # Check if schema is in allowed list
    return schema_name in permission.allowed_schemas
```

## Migration Steps

### If Column Doesn't Exist Yet

1. **For JSON (Recommended):**
   ```bash
   psql -d your_database -f migrations/add_schema_permissions_final.sql
   ```

### If Column Already Exists as JSONB

1. **Convert to JSON:**
   ```sql
   -- Convert JSONB to JSON
   ALTER TABLE user_database_permissions 
   ALTER COLUMN allowed_schemas TYPE JSON USING allowed_schemas::json;
   
   -- Update comment
   COMMENT ON COLUMN user_database_permissions.allowed_schemas IS 'JSON array of schema names that the user is allowed to access. NULL means all schemas are allowed.';
   
   -- Drop GIN index and create simple index
   DROP INDEX IF EXISTS idx_user_database_permissions_schemas;
   CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
   ON user_database_permissions (allowed_schemas);
   ```

2. **Update model:**
   ```python
   # In models.py, ensure it's:
   db.Column('allowed_schemas', db.JSON, nullable=True)
   ```

## Testing

### Test Schema Permissions

```python
# Test creating permission with specific schemas
def test_schema_permissions():
    # Create permission with specific schemas
    permission_data = {
        'user_id': 1,
        'external_database_id': 1,
        'permission_level': 'read',
        'allowed_schemas': ['public', 'analytics']
    }
    
    # Insert permission
    db.session.execute(user_database_permissions.insert().values(**permission_data))
    db.session.commit()
    
    # Verify permission
    permission = db.session.query(user_database_permissions).filter_by(
        user_id=1, external_database_id=1
    ).first()
    
    assert permission.allowed_schemas == ['public', 'analytics']
```

## Troubleshooting

### Common Issues

1. **AttributeError: JSONB:**
   - Ensure you're using `db.JSON` instead of `db.JSONB`
   - Flask-SQLAlchemy doesn't support JSONB directly

2. **Migration Already Applied:**
   - Check if the column already exists
   - Use the fix migration if needed

3. **Permission Denied:**
   - Ensure database user has ALTER TABLE permissions
   - Check if table is locked by other operations

### Rollback Plan

If you need to rollback:

```sql
-- Remove the column
ALTER TABLE user_database_permissions DROP COLUMN allowed_schemas;

-- Remove the index
DROP INDEX IF EXISTS idx_user_database_permissions_schemas;

-- Remove the constraint
ALTER TABLE user_database_permissions DROP CONSTRAINT IF EXISTS check_allowed_schemas;
```

## Recommendations

1. **Use JSON (Option 1)** for Flask-SQLAlchemy compatibility
2. **Test thoroughly** before applying to production
3. **Backup database** before running migrations
4. **Monitor performance** after index creation
5. **Consider data volume** when choosing indexing strategy

## Support

If you encounter issues:

1. Check Flask-SQLAlchemy documentation for JSON column usage
2. Verify database user permissions
3. Ensure sufficient disk space and memory
4. Test migrations on a copy of production data first 