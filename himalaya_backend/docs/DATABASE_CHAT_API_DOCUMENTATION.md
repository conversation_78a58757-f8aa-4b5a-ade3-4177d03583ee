# Database Chat API Documentation

This document provides comprehensive API documentation for the database chat functionality, including session management, message handling, and visualization features.

## Table of Contents

1. [Authentication & Authorization](#authentication--authorization)
2. [Database Chat Sessions](#database-chat-sessions)
3. [Database Chat Messages](#database-chat-messages)
4. [Visualization Features](#visualization-features)
5. [Error Handling](#error-handling)
6. [Response Formats](#response-formats)

## Authentication & Authorization

All endpoints require authentication and the `database_chat` scope:

```http
Authorization: Bearer <your_jwt_token>
```

Required scope: `database_chat`

## Database Chat Sessions

### Get Session Messages

Retrieve all messages for a specific database chat session.

**Endpoint:** `GET /api/database-chat/sessions/{session_id}/messages`

**Parameters:**
- `session_id` (path, required): The ID of the database chat session

**Response Structure:**
```json
{
  "session_id": 123,
  "session_name": "Sales Analysis Session",
  "database_name": "sales_db",
  "messages": [
    {
      "id": 456,
      "question": "Show me the top 10 customers by revenue",
      "answer": "Here are the top 10 customers by revenue...",
      "sql_queries": ["SELECT customer_name, revenue FROM customers ORDER BY revenue DESC LIMIT 10"],
      "query_results": [
        {
          "step": 1,
          "description": "Get top customers by revenue",
          "query": "SELECT customer_name, revenue FROM customers ORDER BY revenue DESC LIMIT 10",
          "success": true,
          "data": [
            {"customer_name": "Acme Corp", "revenue": 1500000},
            {"customer_name": "TechStart", "revenue": 1200000}
          ],
          "row_count": 10,
          "error": null
        }
      ],
      "execution_plan": {
        "analysis": "User wants to see top customers by revenue",
        "tables_needed": ["customers"],
        "execution_steps": [
          {
            "step": 1,
            "description": "Get top customers by revenue",
            "sql_query": "SELECT customer_name, revenue FROM customers ORDER BY revenue DESC LIMIT 10",
            "purpose": "Retrieve customer data sorted by revenue"
          }
        ],
        "expected_output": "List of top 10 customers with their revenue",
        "complexity": "simple",
        "visualization_needed": true,
        "suggested_chart_type": "bar",
        "visualization_reasoning": "Bar chart would effectively show revenue comparison between customers"
      },
      "qa_feedback": {
        "quality_score": 95,
        "feedback": ["Answer appears to address the question", "Data is well-structured"],
        "needs_improvement": false,
        "successful_queries": 1,
        "failed_queries": 0
      },
      "token_usage": {
        "prompt_tokens": 1250,
        "completion_tokens": 450,
        "total_tokens": 1700
      },
      "execution_time_ms": 2340,
      "error_message": null,
      "visualization_data": {
        "type": "bar",
        "title": "Top 10 Customers by Revenue",
        "labels": ["Acme Corp", "TechStart", "Global Inc"],
        "datasets": [
          {
            "label": "Revenue",
            "data": [1500000, 1200000, 980000]
          }
        ]
      },
      "visualization_type": "bar",
      "visualization_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
      "visualization_metadata": {
        "chart_type": "bar",
        "data_points": 10,
        "generated_at": "2025-07-16T09:03:24.428876",
        "ai_decision": {
          "should_generate": true,
          "chart_type": "bar",
          "confidence": 0.85,
          "reasoning": "Bar chart is ideal for comparing revenue values across customers"
        }
      },
      "created_at": "2025-07-16T09:03:24.428876"
    }
  ],
  "total": 1
}
```

**Sample cURL:**
```bash
curl -X GET "http://localhost:5000/api/database-chat/sessions/123/messages" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json"
```

## Database Chat Messages

### Add Database Message

Send a new question to the database and get an AI-powered response with optional visualization.

**Endpoint:** `POST /api/database-chat/sessions/{session_id}/messages`

**Parameters:**
- `session_id` (path, required): The ID of the database chat session

**Request Body:**
```json
{
  "question": "Can you give me trend of chat consumption for current month day wise with graph?"
}
```

**Response Structure:**
```json
{
  "id": 789,
  "question": "Can you give me trend of chat consumption for current month day wise with graph?",
  "answer": "Here's the day-wise trend of chat consumption for the current month (July 2025):\n\n- **July 4:** 2 messages\n- **July 5:** 3 messages\n- **July 6:** 14 messages\n- **July 7:** 40 messages\n- **July 8:** 29 messages\n\nThe data shows a steady increase in chat activity, peaking on July 7 with 40 messages. This could indicate higher engagement or specific events driving usage on that day.\n\nTo help you visualize this trend, I've generated a line chart that maps the daily message counts across the month. Let me know if you'd like me to share or refine the graph further!",
  "sql_queries": [
    "SELECT DATE(created_at) AS message_date, COUNT(*) AS message_count FROM public.chat_messages WHERE DATE_PART('month', created_at) = DATE_PART('month', CURRENT_DATE) AND DATE_PART('year', created_at) = DATE_PART('year', CURRENT_DATE) GROUP BY DATE(created_at) ORDER BY message_date;"
  ],
  "query_results": [
    {
      "step": 1,
      "description": "Identify the current month and filter chat messages created within this month.",
      "query": "SELECT DATE(created_at) AS message_date, COUNT(*) AS message_count FROM public.chat_messages WHERE DATE_PART('month', created_at) = DATE_PART('month', CURRENT_DATE) AND DATE_PART('year', created_at) = DATE_PART('year', CURRENT_DATE) GROUP BY DATE(created_at) ORDER BY message_date;",
      "success": true,
      "data": [
        {"message_count": 2, "message_date": "2025-07-04"},
        {"message_count": 3, "message_date": "2025-07-05"},
        {"message_count": 14, "message_date": "2025-07-06"},
        {"message_count": 40, "message_date": "2025-07-07"},
        {"message_count": 29, "message_date": "2025-07-08"}
      ],
      "row_count": 11,
      "error": null
    }
  ],
  "execution_plan": {
    "analysis": "The user is asking for a day-wise trend of chat consumption for the current month, along with a graph to visualize the data.",
    "complexity": "medium",
    "execution_steps": [
      {
        "description": "Identify the current month and filter chat messages created within this month.",
        "purpose": "To retrieve the daily count of chat messages for the current month.",
        "sql_query": "SELECT DATE(created_at) AS message_date, COUNT(*) AS message_count FROM public.chat_messages WHERE DATE_PART('month', created_at) = DATE_PART('month', CURRENT_DATE) AND DATE_PART('year', created_at) = DATE_PART('year', CURRENT_DATE) GROUP BY DATE(created_at) ORDER BY message_date;",
        "step": 1
      }
    ],
    "expected_output": "A list of dates for the current month and the corresponding number of chat messages sent on each date.",
    "suggested_chart_type": "line",
    "tables_needed": ["chat_messages"],
    "visualization_needed": true,
    "visualization_reasoning": "A line chart would effectively show the day-wise trend of chat consumption, making it easy to identify patterns or spikes in activity over the month."
  },
  "qa_feedback": {
    "failed_queries": 0,
    "feedback": ["Answer appears to address the question"],
    "needs_improvement": false,
    "quality_score": 100,
    "success": true,
    "successful_queries": 1
  },
  "token_usage": {
    "completion_tokens": 415,
    "prompt_tokens": 4162,
    "total_tokens": 4577
  },
  "execution_time_ms": 32097,
  "error_message": null,
  "visualization_data": {
    "type": "line",
    "title": "Chat Consumption Trend - Current Month",
    "labels": ["2025-07-04", "2025-07-05", "2025-07-06", "2025-07-07", "2025-07-08"],
    "datasets": [
      {
        "label": "Message Count",
        "data": [2, 3, 14, 40, 29]
      }
    ]
  },
  "visualization_type": "line",
  "visualization_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "visualization_metadata": {
    "chart_type": "line",
    "data_points": 11,
    "generated_at": "2025-07-16T09:03:24.428876",
    "ai_decision": {
      "should_generate": true,
      "chart_type": "line",
      "confidence": 0.9,
      "reasoning": "Line chart is perfect for showing trends over time",
      "alternative_types": ["area", "bar"],
      "user_benefit": "Clear visualization of daily chat activity patterns"
    },
    "question_analysis": {
      "original_question": "Can you give me trend of chat consumption for current month day wise with graph?",
      "detected_chart_type": "line",
      "confidence": 0.9,
      "reasoning": "User explicitly requested trend analysis with graph",
      "user_benefit": "Visual representation of daily chat consumption patterns"
    }
  },
  "created_at": "2025-07-16T09:03:24.428876",
  "reloop_attempts": 1
}
```

**Sample cURL:**
```bash
curl -X POST "http://localhost:5000/api/database-chat/sessions/123/messages" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "Can you give me trend of chat consumption for current month day wise with graph?"
  }'
```

**JavaScript Example:**
```javascript
const response = await fetch('/api/database-chat/sessions/123/messages', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your_jwt_token',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    question: 'Can you give me trend of chat consumption for current month day wise with graph?'
  })
});

const result = await response.json();
console.log('Answer:', result.answer);
console.log('Visualization type:', result.visualization_type);
if (result.visualization_image) {
  console.log('Chart image available');
}
```

**Python Example:**
```python
import requests

url = "http://localhost:5000/api/database-chat/sessions/123/messages"
headers = {
    "Authorization": "Bearer your_jwt_token",
    "Content-Type": "application/json"
}
data = {
    "question": "Can you give me trend of chat consumption for current month day wise with graph?"
}

response = requests.post(url, headers=headers, json=data)
result = response.json()

print(f"Answer: {result['answer']}")
print(f"Visualization type: {result['visualization_type']}")
if result.get('visualization_image'):
    print("Chart image generated successfully")
```

## Visualization Features

### Get Visualization Suggestions

Get alternative visualization suggestions for a specific message.

**Endpoint:** `GET /api/database-chat/messages/{message_id}/visualization-suggestions`

**Parameters:**
- `message_id` (path, required): The ID of the database chat message

**Response Structure:**
```json
{
  "message_id": 789,
  "suggestions": [
    "line - For showing trends over time",
    "bar - For easier comparison of values",
    "area - For showing cumulative trends",
    "pie - For showing proportions and percentages"
  ],
  "current_visualization_type": "line"
}
```

**Sample cURL:**
```bash
curl -X GET "http://localhost:5000/api/database-chat/messages/789/visualization-suggestions" \
  -H "Authorization: Bearer your_jwt_token"
```

### Regenerate Visualization

Regenerate visualization for a message with a different chart type.

**Endpoint:** `POST /api/database-chat/messages/{message_id}/regenerate-visualization`

**Parameters:**
- `message_id` (path, required): The ID of the database chat message

**Request Body:**
```json
{
  "chart_type": "bar"
}
```

**Response Structure:**
```json
{
  "message_id": 789,
  "visualization_data": {
    "type": "bar",
    "title": "Chat Consumption Trend - Current Month (Bar Chart)",
    "labels": ["2025-07-04", "2025-07-05", "2025-07-06", "2025-07-07", "2025-07-08"],
    "datasets": [
      {
        "label": "Message Count",
        "data": [2, 3, 14, 40, 29]
      }
    ]
  },
  "visualization_type": "bar",
  "visualization_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "visualization_metadata": {
    "chart_type": "bar",
    "data_points": 11,
    "generated_at": "2025-07-16T09:15:30.123456",
    "ai_decision": {
      "should_generate": true,
      "chart_type": "bar",
      "confidence": 0.8,
      "reasoning": "Bar chart requested by user for better value comparison"
    }
  }
}
```

**Sample cURL:**
```bash
curl -X POST "http://localhost:5000/api/database-chat/messages/789/regenerate-visualization" \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{
    "chart_type": "bar"
  }'
```

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "No question provided"
}
```

**400 Bad Request - Query Safety:**
```json
{
  "error": "Query not allowed: Only SELECT queries are permitted"
}
```

**403 Forbidden - Permission Denied:**
```json
{
  "error": "You do not have permission to access this database"
}
```

**403 Forbidden - Expired Permission:**
```json
{
  "error": "Your permission to access this database has expired"
}
```

**404 Not Found:**
```json
{
  "error": "Database session not found"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to process database message"
}
```

## Response Formats

### Message Response Fields

| Field | Type | Description |
|-------|------|-------------|
| `id` | integer | Unique message ID |
| `question` | string | User's original question |
| `answer` | string | AI-generated answer |
| `sql_queries` | array | List of SQL queries executed |
| `query_results` | array | Detailed results from each query |
| `execution_plan` | object | AI-generated execution plan |
| `qa_feedback` | object | Quality assurance feedback |
| `token_usage` | object | OpenAI token usage statistics |
| `execution_time_ms` | integer | Total execution time in milliseconds |
| `error_message` | string/null | Error message if any |
| `visualization_data` | object/null | Chart data structure |
| `visualization_type` | string/null | Type of chart generated |
| `visualization_image` | string/null | Base64 encoded chart image |
| `visualization_metadata` | object/null | Detailed visualization metadata |
| `created_at` | string | ISO timestamp of message creation |
| `reloop_attempts` | integer | Number of query fix attempts |

### Execution Plan Structure

```json
{
  "analysis": "What the user is asking for",
  "tables_needed": ["table1", "table2"],
  "execution_steps": [
    {
      "step": 1,
      "description": "Description of what this step does",
      "sql_query": "SELECT statement",
      "purpose": "Why this query is needed"
    }
  ],
  "expected_output": "Description of expected results",
  "complexity": "simple|medium|complex",
  "visualization_needed": true/false,
  "suggested_chart_type": "bar|line|pie|scatter|area",
  "visualization_reasoning": "Why visualization would be helpful"
}
```

### QA Feedback Structure

```json
{
  "quality_score": 95,
  "feedback": ["Answer appears to address the question"],
  "needs_improvement": false,
  "successful_queries": 1,
  "failed_queries": 0
}
```

### Visualization Data Structure

```json
{
  "type": "line|bar|pie|scatter|area",
  "title": "Chart title",
  "labels": ["label1", "label2", "label3"],
  "datasets": [
    {
      "label": "Dataset name",
      "data": [value1, value2, value3]
    }
  ]
}
```

### Visualization Metadata Structure

```json
{
  "chart_type": "line",
  "data_points": 11,
  "generated_at": "2025-07-16T09:03:24.428876",
  "ai_decision": {
    "should_generate": true,
    "chart_type": "line",
    "confidence": 0.9,
    "reasoning": "Line chart is perfect for showing trends over time",
    "alternative_types": ["area", "bar"],
    "user_benefit": "Clear visualization of daily chat activity patterns"
  },
  "question_analysis": {
    "original_question": "User's original question",
    "detected_chart_type": "line",
    "confidence": 0.9,
    "reasoning": "AI reasoning for chart type selection",
    "user_benefit": "How visualization helps user understand data"
  }
}
```

## Frontend Integration Examples

### React Hook for Database Chat

```javascript
import { useState, useEffect } from 'react';

const useDatabaseChat = (sessionId) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const sendMessage = async (question) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/database-chat/sessions/${sessionId}/messages`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ question })
      });
      
      if (!response.ok) {
        throw new Error('Failed to send message');
      }
      
      const result = await response.json();
      setMessages(prev => [...prev, result]);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const regenerateVisualization = async (messageId, chartType) => {
    try {
      const response = await fetch(`/api/database-chat/messages/${messageId}/regenerate-visualization`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ chart_type: chartType })
      });
      
      if (!response.ok) {
        throw new Error('Failed to regenerate visualization');
      }
      
      const result = await response.json();
      
      // Update the message with new visualization
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, ...result }
          : msg
      ));
      
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    }
  };

  return {
    messages,
    loading,
    error,
    sendMessage,
    regenerateVisualization
  };
};
```

### Displaying Visualizations

```javascript
const MessageVisualization = ({ message }) => {
  if (!message.visualization_data) {
    return null;
  }

  return (
    <div className="visualization-container">
      <h4>{message.visualization_data.title}</h4>
      
      {/* Display chart image if available */}
      {message.visualization_image && (
        <img 
          src={message.visualization_image} 
          alt="Data visualization"
          className="chart-image"
        />
      )}
      
      {/* Display chart data for debugging */}
      <details>
        <summary>Chart Data</summary>
        <pre>{JSON.stringify(message.visualization_data, null, 2)}</pre>
      </details>
      
      {/* Visualization metadata */}
      {message.visualization_metadata && (
        <div className="viz-metadata">
          <small>
            Chart type: {message.visualization_metadata.chart_type} | 
            Confidence: {message.visualization_metadata.ai_decision?.confidence} | 
            Data points: {message.visualization_metadata.data_points}
          </small>
        </div>
      )}
    </div>
  );
};
```

## Security Considerations

1. **Read-Only Access**: Only SELECT queries are allowed
2. **Query Safety**: All queries are validated for safety
3. **Schema Permissions**: Users can only access allowed schemas
4. **Session Ownership**: Users can only access their own sessions
5. **Permission Expiry**: Database access permissions can expire

## Rate Limiting

Consider implementing rate limiting for:
- Message creation (e.g., 10 messages per minute per user)
- Visualization regeneration (e.g., 5 regenerations per minute per user)
- Session access (e.g., 100 requests per hour per user)

## Best Practices

1. **Question Formulation**: Be specific and clear in your questions
2. **Visualization Requests**: Include keywords like "graph", "chart", "trend", "compare"
3. **Error Handling**: Always check for error responses
4. **Token Usage**: Monitor token usage for cost optimization
5. **Session Management**: Use appropriate session timeouts
6. **Data Privacy**: Ensure sensitive data is not exposed in visualizations
