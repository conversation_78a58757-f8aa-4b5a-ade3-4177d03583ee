# Database Admin API Documentation

This document provides comprehensive API documentation for the Database Admin routes, including all endpoints, request/response formats, and usage examples.

## Table of Contents

1. [Authentication & Authorization](#authentication--authorization)
2. [Database Types](#database-types)
3. [Database Management](#database-management)
4. [Database Testing & Schema](#database-testing--schema)
5. [User Permissions](#user-permissions)
6. [<PERSON><PERSON><PERSON>ling](#error-handling)

## Authentication & Authorization

All endpoints require:
- **Authentication**: Valid JWT token in Authorization header
- **Authorization**: `DATABASE_ADMIN` scope
- **Headers**: `Content-Type: application/json` (for POST/PUT requests)

```bash
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json
```

---

## Database Types

### Get Available Database Types

**Endpoint:** `GET /api/admin/databases/types`

**Description:** Get all supported database types and their configurations

**Response:**
```json
{
  "database_types": {
    "postgresql": {
      "name": "PostgreSQL",
      "default_port": 5432,
      "required_fields": ["name", "host", "port", "database_name", "username", "password"],
      "optional_fields": ["ssl_enabled", "ssl_cert_path", "connection_string_template", "schema"],
      "additional_params": {
        "sslmode": "prefer",
        "connect_timeout": 10,
        "application_name": "himalaya_chat"
      },
      "schema_support": true,
      "default_schema": "public",
      "ssl_options": ["disable", "allow", "prefer", "require", "verify-ca", "verify-full"],
      "description": "PostgreSQL database with advanced features and ACID compliance"
    },
    "mysql": {
      "name": "MySQL",
      "default_port": 3306,
      "required_fields": ["name", "host", "port", "database_name", "username", "password"],
      "optional_fields": ["ssl_enabled", "connection_string_template", "schema"],
      "additional_params": {
        "charset": "utf8mb4",
        "autocommit": true,
        "connect_timeout": 10
      },
      "schema_support": true,
      "default_schema": null,
      "description": "MySQL database with high performance and reliability"
    },
    "sqlserver": {
      "name": "SQL Server",
      "default_port": 1433,
      "required_fields": ["name", "host", "port", "database_name", "username", "password"],
      "optional_fields": ["ssl_enabled", "connection_string_template", "schema"],
      "additional_params": {
        "driver": "ODBC Driver 17 for SQL Server",
        "timeout": 30,
        "encrypt": "yes",
        "trust_server_certificate": "yes"
      },
      "schema_support": true,
      "default_schema": "dbo",
      "description": "Microsoft SQL Server with enterprise features"
    },
    "oracle": {
      "name": "Oracle",
      "default_port": 1521,
      "required_fields": ["name", "host", "port", "database_name", "username", "password"],
      "optional_fields": ["ssl_enabled", "connection_string_template", "schema"],
      "additional_params": {
        "encoding": "UTF-8",
        "nencoding": "UTF-8",
        "threaded": true
      },
      "schema_support": true,
      "default_schema": null,
      "description": "Oracle Database with enterprise-grade features"
    }
  },
  "total": 4
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/admin/databases/types" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Database Management

### Get All Databases

**Endpoint:** `GET /api/admin/databases`

**Description:** Get all external database configurations

**Response:**
```json
{
  "databases": [
    {
      "id": 1,
      "name": "Production PostgreSQL",
      "db_type": "postgresql",
      "host": "db.example.com",
      "port": 5432,
      "database_name": "production_db",
      "username": "db_user",
      "schema": "public",
      "ssl_enabled": true,
      "is_active": true,
      "created_by": "admin_user",
      "created_at": "2024-01-15T10:00:00Z",
      "updated_at": "2024-01-15T10:00:00Z",
      "last_tested_at": "2024-01-15T10:30:00Z",
      "test_status": "success",
      "test_error_message": null,
      "authorized_users_count": 5
    }
  ],
  "total": 1
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/admin/databases" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Create Database

**Endpoint:** `POST /api/admin/databases`

**Description:** Create a new external database configuration

**Request Body:**
```json
{
  "name": "Production PostgreSQL",
  "db_type": "postgresql",
  "host": "db.example.com",
  "port": 5432,
  "database_name": "production_db",
  "username": "db_user",
  "password": "secure_password",
  "schema": "public",
  "ssl_enabled": true,
  "ssl_cert_path": "/path/to/cert.pem",
  "additional_params": {
    "sslmode": "require",
    "connect_timeout": 15
  },
  "is_active": true
}
```

**Required Fields by Database Type:**

| Database Type | Required Fields | Default Port |
|---------------|----------------|--------------|
| PostgreSQL | name, host, port, database_name, username, password | 5432 |
| MySQL | name, host, port, database_name, username, password | 3306 |
| SQL Server | name, host, port, database_name, username, password | 1433 |
| Oracle | name, host, port, database_name, username, password | 1521 |

**Optional Fields:**
- `schema`: Database schema name (max 100 characters)
- `ssl_enabled`: Enable SSL connection (boolean)
- `ssl_cert_path`: Path to SSL certificate file
- `additional_params`: Additional connection parameters
- `is_active`: Set database as active (boolean, default: true)

**Security Restrictions:**
- Localhost connections are not allowed
- Host must be a valid remote database server
- Port must be between 1 and 65535

**Response:**
```json
{
  "message": "Database configuration created successfully",
  "database": {
    "id": 1,
    "name": "Production PostgreSQL",
    "db_type": "postgresql",
    "host": "db.example.com",
    "port": 5432,
    "database_name": "production_db",
    "schema": "public",
    "is_active": true
  }
}
```

**cURL Example:**
```bash
curl -X POST "http://localhost:5000/api/admin/databases" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Production PostgreSQL",
    "db_type": "postgresql",
    "host": "db.example.com",
    "port": 5432,
    "database_name": "production_db",
    "username": "db_user",
    "password": "secure_password",
    "schema": "public",
    "ssl_enabled": true
  }'
```

### Update Database

**Endpoint:** `PUT /api/admin/databases/{db_id}`

**Description:** Update an existing database configuration

**Request Body (Partial Updates Supported):**
```json
{
  "name": "Updated Database Name",
  "host": "new-host.example.com",
  "port": 5433,
  "database_name": "new_database",
  "username": "new_user",
  "password": "new_password",
  "schema": "new_schema",
  "ssl_enabled": false,
  "additional_params": {
    "new_param": "value"
  },
  "is_active": false
}
```

**Response:**
```json
{
  "message": "Database configuration updated successfully",
  "database": {
    "id": 1,
    "name": "Updated Database Name",
    "db_type": "postgresql",
    "host": "new-host.example.com",
    "port": 5433,
    "database_name": "new_database",
    "schema": "new_schema",
    "is_active": false
  }
}
```

**cURL Example:**
```bash
curl -X PUT "http://localhost:5000/api/admin/databases/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Updated Database Name",
    "ssl_enabled": false
  }'
```

### Delete Database

**Endpoint:** `DELETE /api/admin/databases/{db_id}`

**Description:** Delete a database configuration (only if no active sessions)

**Response:**
```json
{
  "message": "Database configuration deleted successfully"
}
```

**cURL Example:**
```bash
curl -X DELETE "http://localhost:5000/api/admin/databases/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Database Testing & Schema

### Test Database Connection

**Endpoint:** `POST /api/admin/databases/{db_id}/test`

**Description:** Test connection to a database and update test status

**Response:**
```json
{
  "success": true,
  "message": "Connection successful",
  "tested_at": "2024-01-15T10:30:00Z"
}
```

**Error Response:**
```json
{
  "success": false,
  "message": "Connection failed: authentication failed",
  "tested_at": "2024-01-15T10:30:00Z"
}
```

**cURL Example:**
```bash
curl -X POST "http://localhost:5000/api/admin/databases/1/test" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Database Schema

**Endpoint:** `GET /api/admin/databases/{db_id}/schema`

**Description:** Get schema information for a database

**Query Parameters:**
- `schema` (optional): Specific schema name to retrieve

**Response:**
```json
{
  "success": true,
  "schema": {
    "tables": {
      "users": {
        "schema": "public",
        "columns": [
          {
            "name": "id",
            "type": "INTEGER",
            "nullable": false,
            "default": null
          },
          {
            "name": "email",
            "type": "VARCHAR(255)",
            "nullable": false,
            "default": null
          }
        ],
        "primary_keys": {
          "constrained_columns": ["id"],
          "name": "pk_users"
        },
        "foreign_keys": [],
        "indexes": [
          {
            "name": "idx_users_email",
            "column_names": ["email"],
            "unique": true
          }
        ]
      }
    },
    "views": {
      "user_summary": {
        "schema": "public",
        "columns": [
          {
            "name": "user_id",
            "type": "INTEGER",
            "nullable": true
          }
        ]
      }
    }
  },
  "schema_name": "public"
}
```

**cURL Examples:**
```bash
# Get all schemas
curl -X GET "http://localhost:5000/api/admin/databases/1/schema" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get specific schema
curl -X GET "http://localhost:5000/api/admin/databases/1/schema?schema=public" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Available Schemas

**Endpoint:** `GET /api/admin/databases/{db_id}/schemas`

**Description:** Get list of available schemas in a database

**Response:**
```json
{
  "success": true,
  "schemas": [
    {
      "name": "public",
      "table_count": 15,
      "view_count": 3,
      "total_objects": 18
    },
    {
      "name": "analytics",
      "table_count": 8,
      "view_count": 2,
      "total_objects": 10
    }
  ],
  "total": 2
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/admin/databases/1/schemas" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Databases for Connections

**Endpoint:** `GET /api/admin/databases/available-for-connections`

**Description:** Get list of databases that can be used for connections (tested and active)

**Response:**
```json
{
  "databases": [
    {
      "id": 1,
      "name": "Production PostgreSQL",
      "db_type": "postgresql",
      "database_name": "production_db",
      "host": "db.example.com",
      "port": 5432,
      "schema": "public",
      "last_tested_at": "2024-01-15T10:30:00Z",
      "authorized_users_count": 5,
      "created_by": "admin_user",
      "created_at": "2024-01-15T10:00:00Z"
    }
  ],
  "total": 1
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/admin/databases/available-for-connections" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## User Permissions

### Get Database Permissions

**Endpoint:** `GET /api/admin/databases/{db_id}/permissions`

**Description:** Get all user permissions for a database

**Response:**
```json
{
  "database_name": "Production PostgreSQL",
  "permissions": [
    {
      "user_id": 1,
      "user_name": "john_doe",
      "user_email": "<EMAIL>",
      "permission_level": "read",
      "allowed_schemas": ["public", "analytics"],
      "granted_by": "admin_user",
      "created_at": "2024-01-15T10:00:00Z",
      "expires_at": "2024-12-31T23:59:59Z"
    }
  ],
  "total": 1
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/admin/databases/1/permissions" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Grant Database Permission

**Endpoint:** `POST /api/admin/databases/{db_id}/permissions`

**Description:** Grant database access permission to a user

**Request Body:**
```json
{
  "user_id": 1,
  "permission_level": "read",
  "allowed_schemas": ["public", "analytics"],
  "expires_at": "2024-12-31T23:59:59"
}
```

**Required Fields:**
- `user_id`: ID of the user to grant permission to

**Optional Fields:**
- `permission_level`: "read" or "admin" (default: "read")
- `allowed_schemas`: Array of schema names (null = all schemas)
- `expires_at`: Expiration date in ISO format (null = no expiration)

**Response:**
```json
{
  "message": "Database permission granted successfully",
  "permission": {
    "user_name": "john_doe",
    "user_email": "<EMAIL>",
    "permission_level": "read",
    "database_name": "Production PostgreSQL",
    "allowed_schemas": ["public", "analytics"],
    "expires_at": "2024-12-31T23:59:59"
  }
}
```

**cURL Examples:**

**Basic Permission:**
```bash
curl -X POST "http://localhost:5000/api/admin/databases/1/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": 1,
    "permission_level": "read"
  }'
```

**Permission with Schema Restrictions:**
```bash
curl -X POST "http://localhost:5000/api/admin/databases/1/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": 1,
    "permission_level": "read",
    "allowed_schemas": ["public", "analytics"],
    "expires_at": "2024-12-31T23:59:59"
  }'
```

**Admin Permission:**
```bash
curl -X POST "http://localhost:5000/api/admin/databases/1/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": 1,
    "permission_level": "admin"
  }'
```

### Update Database Permission

**Endpoint:** `PUT /api/admin/databases/{db_id}/permissions/{user_id}`

**Description:** Update database access permission for a user

**Request Body (Partial Updates Supported):**
```json
{
  "permission_level": "admin",
  "allowed_schemas": ["public", "analytics", "reports"],
  "expires_at": "2025-12-31T23:59:59"
}
```

**Response:**
```json
{
  "message": "Database permission updated successfully"
}
```

**cURL Example:**
```bash
curl -X PUT "http://localhost:5000/api/admin/databases/1/permissions/1" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "permission_level": "admin",
    "allowed_schemas": ["public", "analytics"]
  }'
```

### Revoke Database Permission

**Endpoint:** `DELETE /api/admin/databases/{db_id}/permissions/{user_id}`

**Description:** Revoke database access permission from a user

**Response:**
```json
{
  "message": "Database permission revoked successfully"
}
```

**cURL Example:**
```bash
curl -X DELETE "http://localhost:5000/api/admin/databases/1/permissions/1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "Missing required field: host"
}
```

**401 Unauthorized:**
```json
{
  "error": "Authentication required"
}
```

**403 Forbidden:**
```json
{
  "error": "Insufficient permissions"
}
```

**404 Not Found:**
```json
{
  "error": "Database not found"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to create database configuration"
}
```

### Validation Errors

**Database Type Validation:**
```json
{
  "error": "Unsupported database type. Supported types: postgresql, mysql, sqlserver, oracle"
}
```

**Localhost Restriction:**
```json
{
  "error": "Localhost connections are not allowed for security reasons. Please use a remote database server."
}
```

**Port Validation:**
```json
{
  "error": "Port must be between 1 and 65535"
}
```

**Schema Validation:**
```json
{
  "error": "Schema name must be 100 characters or less"
}
```

**Permission Validation:**
```json
{
  "error": "User already has permission for this database"
}
```

---

## JavaScript/Fetch Examples

### Create Database
```javascript
const createDatabase = async (databaseConfig) => {
  const response = await fetch('/api/admin/databases', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(databaseConfig)
  });
  
  return await response.json();
};

// Usage
const newDb = await createDatabase({
  name: 'Production PostgreSQL',
  db_type: 'postgresql',
  host: 'db.example.com',
  port: 5432,
  database_name: 'production_db',
  username: 'db_user',
  password: 'secure_password',
  schema: 'public',
  ssl_enabled: true
});
```

### Grant Permission
```javascript
const grantPermission = async (dbId, userId, permissionData) => {
  const response = await fetch(`/api/admin/databases/${dbId}/permissions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      user_id: userId,
      permission_level: 'read',
      allowed_schemas: ['public', 'analytics'],
      expires_at: '2024-12-31T23:59:59'
    })
  });
  
  return await response.json();
};
```

---

## Python Requests Examples

### Create Database
```python
import requests

def create_database(token, database_config):
    url = "http://localhost:5000/api/admin/databases"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(url, headers=headers, json=database_config)
    return response.json()

# Usage
database_config = {
    'name': 'Production PostgreSQL',
    'db_type': 'postgresql',
    'host': 'db.example.com',
    'port': 5432,
    'database_name': 'production_db',
    'username': 'db_user',
    'password': 'secure_password',
    'schema': 'public',
    'ssl_enabled': True
}

result = create_database('your_token', database_config)
print(result)
```

### Test Connection
```python
def test_database_connection(token, db_id):
    url = f"http://localhost:5000/api/admin/databases/{db_id}/test"
    headers = {
        'Authorization': f'Bearer {token}'
    }
    
    response = requests.post(url, headers=headers)
    return response.json()

# Usage
result = test_database_connection('your_token', 1)
if result['success']:
    print("Connection successful!")
else:
    print(f"Connection failed: {result['message']}")
```

---

## React Hook Examples

### Database Management Hook
```javascript
import { useState } from 'react';

const useDatabaseAdmin = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const createDatabase = async (databaseConfig) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch('/api/admin/databases', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify(databaseConfig)
      });
      
      if (!response.ok) {
        throw new Error('Failed to create database');
      }
      
      return await response.json();
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const testConnection = async (dbId) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/admin/databases/${dbId}/test`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      return await response.json();
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return { createDatabase, testConnection, loading, error };
};

// Usage in component
const { createDatabase, testConnection, loading, error } = useDatabaseAdmin();

const handleCreateDatabase = async () => {
  try {
    const result = await createDatabase({
      name: 'New Database',
      db_type: 'postgresql',
      host: 'db.example.com',
      port: 5432,
      database_name: 'new_db',
      username: 'user',
      password: 'password'
    });
    console.log('Database created:', result);
  } catch (err) {
    console.error('Error:', err);
  }
};
```

---

## Notes

### Security Features
- **Localhost Prevention**: Localhost connections are blocked for security
- **Password Encryption**: All passwords are encrypted before storage
- **Schema-Level Permissions**: Granular access control at schema level
- **Read-Only Chat**: Database chat system only allows SELECT queries

### Database Type Support
- **PostgreSQL**: Full support with schema, SSL, and advanced features
- **MySQL**: Full support with charset and autocommit options
- **SQL Server**: Full support with ODBC driver and encryption
- **Oracle**: Full support with UTF-8 encoding and threading

### Best Practices
1. Always test database connections after creation
2. Use schema-level permissions for security
3. Set appropriate expiration dates for user permissions
4. Monitor database performance and connection status
5. Regularly review and update user permissions

### Rate Limiting
- Standard API rate limiting applies
- Database connection tests may be rate-limited
- Schema retrieval operations are cached when possible

### Monitoring
- All database operations are logged
- Connection test results are stored
- User permission changes are tracked
- Error messages provide detailed debugging information
