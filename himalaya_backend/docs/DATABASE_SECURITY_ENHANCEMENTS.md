# Database Security Enhancements

## Overview

The database admin routes have been enhanced with additional security measures to prevent unauthorized access and provide granular schema-level permissions. These enhancements ensure that only secure, remote database connections are allowed and that users can be restricted to specific schemas.

## Security Enhancements

### 1. Localhost Connection Restrictions

**Problem**: Localhost connections pose security risks as they can access local databases that may contain sensitive information.

**Solution**: All localhost and local network connections are now blocked.

**Blocked Patterns**:
- `localhost`
- `127.0.0.1`
- `::1` (IPv6 localhost)
- `0.0.0.0`
- `localhost.localdomain`
- `localhost.local`
- `*********/8` (localhost subnet)
- `::1/128` (IPv6 localhost subnet)

**Implementation**:
```python
# SECURITY: Prevent localhost connections
host = data.get('host', '').lower().strip()
localhost_patterns = [
    'localhost', '127.0.0.1', '::1', '0.0.0.0',
    'localhost.localdomain', 'localhost.local',
    '*********/8', '::1/128'
]

if any(pattern in host for pattern in localhost_patterns):
    return jsonify({
        'error': 'Localhost connections are not allowed for security reasons. Please use a remote database server.'
    }), 400
```

**Error Response**:
```json
{
  "error": "Localhost connections are not allowed for security reasons. Please use a remote database server."
}
```

### 2. SQLite Database Removal

**Problem**: SQLite databases are file-based and often contain local data, which poses security risks.

**Solution**: SQLite support has been completely removed from the system.

**Impact**:
- SQLite is no longer a supported database type
- All SQLite-related configurations have been removed
- Database type validation now only allows: PostgreSQL, MySQL, SQL Server, Oracle

### 3. Schema-Level Permissions

**Problem**: Users with database access could potentially access all schemas, including sensitive ones.

**Solution**: Admins can now restrict users to specific schemas within a database.

**Database Schema Changes**:
```sql
-- Add schema permissions column
ALTER TABLE user_database_permissions 
ADD COLUMN allowed_schemas JSON DEFAULT NULL;

-- Add index for performance
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
ON user_database_permissions USING GIN (allowed_schemas);

-- Add constraint
ALTER TABLE user_database_permissions 
ADD CONSTRAINT check_allowed_schemas 
CHECK (allowed_schemas IS NULL OR jsonb_typeof(allowed_schemas::jsonb) = 'array');
```

**Permission Levels**:
- `NULL` (default): User has access to all schemas
- `[]` (empty array): User has no schema access
- `['public', 'analytics']`: User has access only to specified schemas

## API Changes

### 1. Grant Database Permission

**Enhanced Endpoint**: `POST /api/admin/databases/{db_id}/permissions`

**New Request Body**:
```json
{
  "user_id": 1,
  "permission_level": "read",
  "allowed_schemas": ["public", "analytics"],
  "expires_at": "2024-12-31T23:59:59Z"
}
```

**Response**:
```json
{
  "message": "Database permission granted successfully",
  "permission": {
    "user_name": "john_doe",
    "user_email": "<EMAIL>",
    "permission_level": "read",
    "database_name": "Production PostgreSQL",
    "allowed_schemas": ["public", "analytics"],
    "expires_at": "2024-12-31T23:59:59Z"
  }
}
```

### 2. Update Database Permission

**Enhanced Endpoint**: `PUT /api/admin/databases/{db_id}/permissions/{user_id}`

**Request Body**:
```json
{
  "permission_level": "admin",
  "allowed_schemas": ["public", "analytics", "reports"],
  "expires_at": "2025-12-31T23:59:59Z"
}
```

### 3. Get Database Permissions

**Enhanced Response**:
```json
{
  "database_name": "Production PostgreSQL",
  "permissions": [
    {
      "user_id": 1,
      "user_name": "john_doe",
      "user_email": "<EMAIL>",
      "permission_level": "read",
      "allowed_schemas": ["public", "analytics"],
      "granted_by": "admin_user",
      "created_at": "2024-01-15T10:30:00Z",
      "expires_at": "2024-12-31T23:59:59Z"
    }
  ],
  "total": 1
}
```

## User Experience Changes

### 1. Schema Access Enforcement

**Schema-Specific Queries**:
- Users with schema restrictions can only access allowed schemas
- Queries to unauthorized schemas are blocked
- Schema violations return clear error messages

**Example Error**:
```json
{
  "error": "Schema permission denied: Access to schema 'private' not allowed. Allowed schemas: ['public', 'analytics']"
}
```

### 2. Schema Listing

**Enhanced Response**: `GET /databases/{db_id}/schemas`

```json
{
  "success": true,
  "database_name": "Production PostgreSQL",
  "schemas": [
    {
      "name": "public",
      "table_count": 10,
      "view_count": 2,
      "total_objects": 12
    },
    {
      "name": "analytics",
      "table_count": 5,
      "view_count": 1,
      "total_objects": 6
    }
  ],
  "total": 2,
  "total_available": 5,
  "has_restricted_access": true
}
```

### 3. Database Chat Enforcement

**Query Validation**:
- All SQL queries are validated against schema permissions
- Schema violations are caught before execution
- Users receive clear feedback about permission issues

**Example Query Blocking**:
```sql
-- This query would be blocked if user doesn't have access to 'private' schema
SELECT * FROM private.users;
```

**Error Response**:
```json
{
  "error": "Schema permission denied: Access to schema 'private' not allowed. Allowed schemas: ['public', 'analytics']"
}
```

## Migration Guide

### 1. Database Migration

**Run the migration script**:
```bash
psql -d your_database -f migrations/add_schema_permissions.sql
```

**Verify the migration**:
```sql
-- Check if the column was added
\d user_database_permissions

-- Verify existing permissions (should be NULL for all schemas)
SELECT user_id, external_database_id, allowed_schemas 
FROM user_database_permissions;
```

### 2. Application Updates

**Update your application to handle**:
- Localhost connection errors
- Schema permission errors
- Enhanced permission responses

**Frontend Changes**:
```javascript
// Handle localhost restriction errors
if (error.message.includes('Localhost connections are not allowed')) {
  showError('Please use a remote database server. Localhost connections are not allowed for security reasons.');
}

// Handle schema permission errors
if (error.message.includes('Schema permission denied')) {
  showError('You do not have permission to access this schema. Contact your administrator.');
}
```

### 3. Testing

**Test localhost restrictions**:
```bash
curl -X POST "http://localhost:5000/api/admin/databases" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "name": "Test DB",
    "db_type": "postgresql",
    "host": "localhost",
    "port": 5432,
    "database_name": "test_db",
    "username": "test_user",
    "password": "test_password"
  }'
```

**Test schema permissions**:
```bash
# Grant permission with schema restrictions
curl -X POST "http://localhost:5000/api/admin/databases/1/permissions" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "user_id": 1,
    "permission_level": "read",
    "allowed_schemas": ["public", "analytics"]
  }'
```

## Security Best Practices

### 1. Database Configuration

**Use Remote Servers**:
- Always use remote database servers
- Avoid localhost, 127.0.0.1, or local network addresses
- Use proper DNS names or public IP addresses

**Example Valid Hosts**:
- `db.example.com`
- `*************` (remote network)
- `*********` (remote network)
- `database.company.com`

### 2. Schema Management

**Principle of Least Privilege**:
- Grant users access only to schemas they need
- Use specific schema lists instead of NULL (all schemas)
- Regularly review and update schema permissions

**Example Schema Strategy**:
```json
{
  "analysts": ["public", "analytics", "reports"],
  "developers": ["public", "development"],
  "readers": ["public"],
  "admins": null  // All schemas
}
```

### 3. Permission Management

**Regular Reviews**:
- Review database permissions monthly
- Remove expired permissions
- Update schema access as user roles change

**Audit Trail**:
- All permission changes are logged
- Track who granted permissions and when
- Monitor schema access patterns

## Error Handling

### Common Error Scenarios

**1. Localhost Connection Attempt**:
```json
{
  "error": "Localhost connections are not allowed for security reasons. Please use a remote database server."
}
```

**2. Schema Permission Violation**:
```json
{
  "error": "Schema permission denied: Access to schema 'private' not allowed. Allowed schemas: ['public', 'analytics']"
}
```

**3. Invalid Schema Data**:
```json
{
  "error": "allowed_schemas must be a list of schema names"
}
```

**4. Schema Name Too Long**:
```json
{
  "error": "Schema names must be 100 characters or less"
}
```

## Testing

### Security Test Cases

**1. Localhost Restriction Tests**:
```python
def test_localhost_restrictions():
    localhost_patterns = [
        'localhost', '127.0.0.1', '::1', '0.0.0.0',
        'localhost.localdomain', 'localhost.local'
    ]
    
    for pattern in localhost_patterns:
        response = create_database(host=pattern)
        assert response.status_code == 400
        assert 'Localhost connections are not allowed' in response.json['error']
```

**2. Schema Permission Tests**:
```python
def test_schema_permissions():
    # Grant restricted permission
    grant_permission(user_id=1, allowed_schemas=['public'])
    
    # Try to access unauthorized schema
    response = query_database("SELECT * FROM private.users")
    assert response.status_code == 403
    assert 'Schema permission denied' in response.json['error']
```

**3. SQLite Removal Tests**:
```python
def test_sqlite_not_supported():
    response = create_database(db_type='sqlite')
    assert response.status_code == 400
    assert 'Unsupported database type' in response.json['error']
```

## Monitoring and Alerts

### Security Monitoring

**1. Connection Attempts**:
- Monitor for localhost connection attempts
- Alert on repeated failed connection attempts
- Track schema permission violations

**2. Permission Changes**:
- Log all permission modifications
- Alert on admin permission grants
- Monitor schema access patterns

**3. Query Analysis**:
- Track schema access patterns
- Identify unusual query patterns
- Monitor for potential security violations

## Future Enhancements

### Planned Security Features

**1. IP Whitelisting**:
- Allow specific IP ranges for database connections
- Network-level access control
- Geographic restrictions

**2. Advanced Schema Security**:
- Column-level permissions
- Row-level security integration
- Dynamic schema access based on user context

**3. Audit and Compliance**:
- Comprehensive audit logging
- Compliance reporting
- Automated security assessments

**4. Encryption Enhancements**:
- End-to-end encryption for database connections
- Encrypted query results
- Secure credential management

## Conclusion

These security enhancements provide a robust foundation for secure database access management. The combination of localhost restrictions, SQLite removal, and schema-level permissions ensures that:

1. **Only secure remote connections are allowed**
2. **Users have minimal required access**
3. **All access is properly audited and controlled**
4. **Security violations are caught and prevented**

The system now provides enterprise-grade security while maintaining usability and performance. 