# Database User Filtering Guide

This document explains how user filtering works in the database chat system and all available filtering options.

## Overview

The database chat system implements comprehensive user-based filtering to ensure users can only access their own data and databases they have permission to use. All routes are protected by authentication and authorization middleware.

## Authentication & Authorization

### Base Protection
All database user routes are protected with:
```python
@login_required
@require_scope(SCOPE_DATABASE_CHAT)
```

### User ID Extraction
The system automatically extracts the current user's ID from the authenticated request:
```python
user_id = request.user.id
```

## Available Filtering Options

### 1. Database Sessions Filtering

**Endpoint:** `GET /api/database-user/databases/sessions`

**Available Query Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `database_type` | string | Filter by database type | `postgresql`, `mysql`, `mssql` |
| `is_connected` | boolean | Filter by connection status | `true`, `false` |
| `date_from` | ISO datetime | Filter sessions created after this date | `2024-01-01T00:00:00` |
| `date_to` | ISO datetime | Filter sessions created before this date | `2024-12-31T23:59:59` |
| `limit` | integer | Number of results per page (default: 50) | `10`, `100` |
| `offset` | integer | Number of results to skip (default: 0) | `0`, `50` |

**Example Requests:**

```bash
# Get all PostgreSQL sessions
GET /api/database-user/databases/sessions?database_type=postgresql

# Get only connected sessions
GET /api/database-user/databases/sessions?is_connected=true

# Get sessions from last 30 days
GET /api/database-user/databases/sessions?date_from=2024-01-01T00:00:00

# Paginated results
GET /api/database-user/databases/sessions?limit=10&offset=20

# Combined filters
GET /api/database-user/databases/sessions?database_type=mysql&is_connected=true&limit=25
```

**Response Format:**
```json
{
  "sessions": [
    {
      "id": 1,
      "session_name": "Chat with Production DB",
      "database_name": "Production Database",
      "database_type": "postgresql",
      "is_connected": true,
      "connection_established_at": "2024-01-15T10:30:00",
      "created_at": "2024-01-15T10:30:00",
      "updated_at": "2024-01-15T11:45:00",
      "message_count": 15
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0,
  "filters_applied": {
    "database_type": "postgresql",
    "is_connected": "true",
    "date_from": null,
    "date_to": null
  }
}
```

### 2. Accessible Databases Filtering

**Endpoint:** `GET /api/database-user/databases/accessible`

**Available Query Parameters:**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `database_type` | string | Filter by database type | `postgresql`, `mysql`, `mssql` |
| `permission_level` | string | Filter by permission level | `read`, `write`, `admin` |
| `test_status` | string | Filter by connection test status | `success`, `failed`, `pending` |
| `include_expired` | boolean | Include expired permissions (default: false) | `true`, `false` |
| `limit` | integer | Number of results per page (default: 50) | `10`, `100` |
| `offset` | integer | Number of results to skip (default: 0) | `0`, `50` |

**Example Requests:**

```bash
# Get only PostgreSQL databases
GET /api/database-user/databases/accessible?database_type=postgresql

# Get databases with write permission
GET /api/database-user/databases/accessible?permission_level=write

# Get only successfully tested databases
GET /api/database-user/databases/accessible?test_status=success

# Include expired permissions
GET /api/database-user/databases/accessible?include_expired=true

# Combined filters
GET /api/database-user/databases/accessible?database_type=mysql&permission_level=read&test_status=success
```

**Response Format:**
```json
{
  "databases": [
    {
      "id": 1,
      "name": "Production Database",
      "db_type": "postgresql",
      "host": "db.example.com",
      "port": 5432,
      "database_name": "production",
      "permission_level": "read",
      "last_tested_at": "2024-01-15T10:30:00",
      "test_status": "success",
      "expires_at": "2024-12-31T23:59:59",
      "is_expired": false
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0,
  "filters_applied": {
    "database_type": "postgresql",
    "permission_level": "read",
    "test_status": "success",
    "include_expired": false
  }
}
```

## Frontend Integration Examples

### React Hook for Database Sessions

```javascript
import { useState, useEffect } from 'react';

const useDatabaseSessions = (filters = {}) => {
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  const fetchSessions = async (newFilters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams({
        ...filters,
        ...newFilters
      });
      
      const response = await fetch(`/api/database-user/databases/sessions?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch sessions');
      }
      
      const data = await response.json();
      setSessions(data.sessions);
      setPagination({
        total: data.total,
        limit: data.limit,
        offset: data.offset
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSessions();
  }, []);

  return {
    sessions,
    loading,
    error,
    pagination,
    refetch: fetchSessions
  };
};
```

### React Hook for Accessible Databases

```javascript
import { useState, useEffect } from 'react';

const useAccessibleDatabases = (filters = {}) => {
  const [databases, setDatabases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  const fetchDatabases = async (newFilters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const queryParams = new URLSearchParams({
        ...filters,
        ...newFilters
      });
      
      const response = await fetch(`/api/database-user/databases/accessible?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error('Failed to fetch databases');
      }
      
      const data = await response.json();
      setDatabases(data.databases);
      setPagination({
        total: data.total,
        limit: data.limit,
        offset: data.offset
      });
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDatabases();
  }, []);

  return {
    databases,
    loading,
    error,
    pagination,
    refetch: fetchDatabases
  };
};
```

### React Component Example

```jsx
import React, { useState } from 'react';
import { useDatabaseSessions } from './hooks/useDatabaseSessions';

const DatabaseSessionsList = () => {
  const [filters, setFilters] = useState({
    database_type: '',
    is_connected: '',
    date_from: '',
    date_to: ''
  });

  const { sessions, loading, error, pagination, refetch } = useDatabaseSessions(filters);

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    refetch(newFilters);
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <div className="filters">
        <select 
          value={filters.database_type} 
          onChange={(e) => handleFilterChange('database_type', e.target.value)}
        >
          <option value="">All Database Types</option>
          <option value="postgresql">PostgreSQL</option>
          <option value="mysql">MySQL</option>
          <option value="mssql">SQL Server</option>
        </select>
        
        <select 
          value={filters.is_connected} 
          onChange={(e) => handleFilterChange('is_connected', e.target.value)}
        >
          <option value="">All Sessions</option>
          <option value="true">Connected</option>
          <option value="false">Disconnected</option>
        </select>
      </div>

      <div className="sessions-list">
        {sessions.map(session => (
          <div key={session.id} className="session-item">
            <h3>{session.session_name}</h3>
            <p>Database: {session.database_name} ({session.database_type})</p>
            <p>Status: {session.is_connected ? 'Connected' : 'Disconnected'}</p>
            <p>Messages: {session.message_count}</p>
          </div>
        ))}
      </div>

      <div className="pagination">
        <p>Showing {sessions.length} of {pagination.total} sessions</p>
      </div>
    </div>
  );
};
```

## Security Considerations

### User Isolation
- All queries automatically filter by `user_id` from the authenticated request
- Users cannot access other users' data
- Database permissions are enforced at the application level

### Permission Validation
- Database access is validated against `user_database_permissions` table
- Expired permissions are automatically filtered out (unless explicitly requested)
- Schema-level permissions are enforced for database operations

### Input Validation
- All query parameters are validated and sanitized
- Date formats are validated before database queries
- Pagination limits are enforced to prevent excessive data retrieval

## Best Practices

1. **Use Pagination**: Always implement pagination for large datasets
2. **Validate Inputs**: Validate and sanitize all filter parameters
3. **Cache Results**: Consider caching frequently accessed filtered results
4. **Monitor Performance**: Monitor query performance with different filter combinations
5. **Error Handling**: Provide clear error messages for invalid filter values

## Troubleshooting

### Common Issues

1. **Date Format Errors**: Ensure dates are in ISO format (YYYY-MM-DDTHH:MM:SS)
2. **Permission Denied**: Check if user has the required database permissions
3. **Empty Results**: Verify filter values match available data
4. **Performance Issues**: Use appropriate database indexes for filtered columns

### Debug Tips

1. Check the `filters_applied` field in responses to verify applied filters
2. Use the `include_expired=true` parameter to see expired permissions
3. Test with minimal filters first, then add complexity
4. Monitor database query logs for performance insights 