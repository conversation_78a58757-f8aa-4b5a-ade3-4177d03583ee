# Database Visualization Feature Documentation

## Overview

The Database Visualization Feature enhances the database chat system by automatically generating charts and infographics for database queries. Users can now see visual representations of their data, making it easier to understand trends, comparisons, and patterns.

## Features

### Automatic Visualization Detection
- **Intelligent Chart Type Selection**: The system automatically determines the most appropriate chart type based on the user's question and data structure
- **Smart Data Processing**: Automatically processes query results to extract meaningful visualization data
- **Context-Aware Generation**: Considers the user's question context to generate relevant visualizations

### Supported Chart Types
1. **Bar Charts**: For comparisons and categorical data
2. **Line Charts**: For trends and time-series data
3. **Pie Charts**: For distributions and proportions
4. **Scatter Plots**: For correlations and relationships
5. **Area Charts**: For cumulative trends and stacked data

### Interactive Features
- **Visualization Suggestions**: Get alternative chart type recommendations
- **Chart Regeneration**: Regenerate visualizations with different chart types
- **Metadata Tracking**: Store visualization generation metadata and reasoning

## API Endpoints

### 1. Enhanced Message Response
The existing message endpoints now include visualization data in their responses.

**Endpoint**: `POST /api/database-chat/sessions/{session_id}/messages`

**Enhanced Response**:
```json
{
  "id": 123,
  "question": "Show me sales by region",
  "answer": "Here are the sales figures by region...",
  "sql_queries": ["SELECT region, SUM(sales) FROM sales_data GROUP BY region"],
  "query_results": [...],
  "execution_time_ms": 150,
  "visualization_data": {
    "type": "bar",
    "title": "Sales by Region - Comparison",
    "labels": ["North", "South", "East", "West"],
    "datasets": [{
      "label": "Sales",
      "data": [150000, 120000, 180000, 90000]
    }]
  },
  "visualization_type": "bar",
  "visualization_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "visualization_metadata": {
    "chart_type": "bar",
    "data_points": 4,
    "generated_at": "2024-01-15T14:30:00Z",
    "question_analysis": {
      "original_question": "Show me sales by region",
      "detected_chart_type": "bar",
      "reasoning": "Generated bar chart based on question analysis and data structure"
    }
  },
  "created_at": "2024-01-15T14:30:00Z"
}
```

### 2. Get Visualization Suggestions
Get alternative visualization suggestions for a message.

**Endpoint**: `GET /api/database-chat/messages/{message_id}/visualization-suggestions`

**Response**:
```json
{
  "message_id": 123,
  "suggestions": [
    "pie - For showing proportions and percentages",
    "line - For showing trends over time",
    "area - For showing cumulative trends"
  ],
  "current_visualization_type": "bar"
}
```

### 3. Regenerate Visualization
Regenerate visualization with a different chart type.

**Endpoint**: `POST /api/database-chat/messages/{message_id}/regenerate-visualization`

**Request Body**:
```json
{
  "chart_type": "pie"
}
```

**Response**:
```json
{
  "message_id": 123,
  "visualization_data": {
    "type": "pie",
    "title": "Sales by Region - Distribution",
    "labels": ["North", "South", "East", "West"],
    "datasets": [{
      "label": "Sales",
      "data": [150000, 120000, 180000, 90000]
    }]
  },
  "visualization_type": "pie",
  "visualization_image": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  "visualization_metadata": {
    "chart_type": "pie",
    "data_points": 4,
    "generated_at": "2024-01-15T14:35:00Z",
    "question_analysis": {
      "original_question": "Show me sales by region (show as pie chart)",
      "detected_chart_type": "pie",
      "reasoning": "Generated pie chart based on explicit request"
    }
  }
}
```

## Usage Examples

### Example 1: Sales Comparison
**Question**: "Compare sales performance across different regions"

**Generated Visualization**: Bar chart showing sales by region
- **Chart Type**: Bar
- **Reasoning**: Comparison of categorical data (regions) with numeric values (sales)

### Example 2: Time Series Analysis
**Question**: "Show me sales trends over the last 12 months"

**Generated Visualization**: Line chart showing sales over time
- **Chart Type**: Line
- **Reasoning**: Time-series data showing trends over time

### Example 3: Distribution Analysis
**Question**: "What is the market share breakdown by product category?"

**Generated Visualization**: Pie chart showing market share percentages
- **Chart Type**: Pie
- **Reasoning**: Distribution/proportion data with percentage-like values

### Example 4: Correlation Analysis
**Question**: "Is there a relationship between advertising spend and sales?"

**Generated Visualization**: Scatter plot showing correlation
- **Chart Type**: Scatter
- **Reasoning**: Relationship analysis between two numeric variables

## Frontend Integration

### React Component Example

```jsx
import React, { useState } from 'react';

const DatabaseChatMessage = ({ message }) => {
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState([]);

  const handleRegenerateVisualization = async (chartType) => {
    try {
      const response = await fetch(`/api/database-chat/messages/${message.id}/regenerate-visualization`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ chart_type: chartType })
      });
      
      const result = await response.json();
      if (response.ok) {
        // Update the message with new visualization
        message.visualization_data = result.visualization_data;
        message.visualization_type = result.visualization_type;
        message.visualization_image = result.visualization_image;
        message.visualization_metadata = result.visualization_metadata;
      }
    } catch (error) {
      console.error('Error regenerating visualization:', error);
    }
  };

  const getVisualizationSuggestions = async () => {
    try {
      const response = await fetch(`/api/database-chat/messages/${message.id}/visualization-suggestions`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const result = await response.json();
      if (response.ok) {
        setSuggestions(result.suggestions);
        setShowSuggestions(true);
      }
    } catch (error) {
      console.error('Error getting suggestions:', error);
    }
  };

  return (
    <div className="database-chat-message">
      <div className="message-content">
        <div className="question">{message.question}</div>
        <div className="answer">{message.answer}</div>
        
        {/* Visualization Display */}
        {message.visualization_image && (
          <div className="visualization-container">
            <div className="visualization-header">
              <h4>Data Visualization</h4>
              <div className="visualization-actions">
                <button onClick={getVisualizationSuggestions}>
                  Get Suggestions
                </button>
                <span className="chart-type-badge">
                  {message.visualization_type}
                </span>
              </div>
            </div>
            
            <div className="visualization-image">
              <img 
                src={message.visualization_image} 
                alt={`${message.visualization_type} chart`}
                className="chart-image"
              />
            </div>
            
            {/* Visualization Suggestions */}
            {showSuggestions && suggestions.length > 0 && (
              <div className="visualization-suggestions">
                <h5>Alternative Visualizations:</h5>
                <div className="suggestion-buttons">
                  {suggestions.map((suggestion, index) => {
                    const chartType = suggestion.split(' - ')[0];
                    const reason = suggestion.split(' - ')[1];
                    return (
                      <button
                        key={index}
                        onClick={() => handleRegenerateVisualization(chartType)}
                        className="suggestion-button"
                        title={reason}
                      >
                        {chartType}
                      </button>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
        )}
        
        {/* SQL Queries (if available) */}
        {message.sql_queries && message.sql_queries.length > 0 && (
          <div className="sql-queries">
            <h5>Executed Queries:</h5>
            {message.sql_queries.map((query, index) => (
              <pre key={index} className="sql-code">{query}</pre>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DatabaseChatMessage;
```

### CSS Styling

```css
.database-chat-message {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  background: white;
}

.visualization-container {
  margin-top: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  padding: 12px;
  background: #fafafa;
}

.visualization-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.visualization-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.chart-type-badge {
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.visualization-image {
  text-align: center;
  margin-bottom: 12px;
}

.chart-image {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.visualization-suggestions {
  border-top: 1px solid #e0e0e0;
  padding-top: 12px;
}

.suggestion-buttons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.suggestion-button {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.suggestion-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.sql-queries {
  margin-top: 16px;
}

.sql-code {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  overflow-x: auto;
  margin-bottom: 8px;
}
```

## Configuration

### Environment Variables
No additional environment variables are required. The visualization feature uses existing dependencies.

### Dependencies
The following dependencies are already included in the requirements:
- `matplotlib` - For chart generation
- `pandas` - For data processing
- `numpy` - For numerical operations

## Best Practices

### 1. Question Formulation
- **Be Specific**: "Show me sales by region" vs "Get data"
- **Use Visualization Keywords**: "chart", "graph", "compare", "trend", "distribution"
- **Specify Chart Types**: "Show as pie chart" or "Display as bar graph"

### 2. Data Considerations
- **Reasonable Data Size**: Visualizations work best with 1-100 data points
- **Structured Data**: Ensure queries return structured data with clear columns
- **Meaningful Categories**: Use categorical data that makes sense for visualization

### 3. Performance Optimization
- **Limit Results**: Use LIMIT clauses in queries for large datasets
- **Aggregate Data**: Use GROUP BY and aggregate functions for meaningful summaries
- **Index Usage**: Ensure proper database indexing for faster queries

## Troubleshooting

### Common Issues

1. **No Visualization Generated**
   - Check if the question contains visualization keywords
   - Ensure query results have sufficient data (1-100 rows)
   - Verify data structure is suitable for visualization

2. **Poor Chart Type Selection**
   - Use the suggestions feature to get alternatives
   - Regenerate with a specific chart type
   - Check the visualization metadata for reasoning

3. **Large Image Files**
   - Base64 images can be large; consider lazy loading
   - Implement image compression if needed
   - Use responsive design for different screen sizes

### Error Handling

```javascript
// Example error handling for visualization features
const handleVisualizationError = (error, operation) => {
  console.error(`Visualization ${operation} failed:`, error);
  
  if (error.response?.status === 400) {
    showNotification('Invalid chart type or data structure', 'warning');
  } else if (error.response?.status === 404) {
    showNotification('Message not found', 'error');
  } else {
    showNotification('Visualization service temporarily unavailable', 'error');
  }
};
```

## Security Considerations

1. **Data Privacy**: Visualizations only use data from authorized database queries
2. **Input Validation**: Chart type parameters are validated before processing
3. **Access Control**: Users can only access visualizations for their own messages
4. **Query Safety**: Only SELECT queries are allowed for visualization generation

## Future Enhancements

1. **Interactive Charts**: Add zoom, pan, and hover functionality
2. **Export Options**: Allow downloading charts as PNG, PDF, or SVG
3. **Custom Styling**: User-configurable chart colors and themes
4. **Advanced Chart Types**: Add heatmaps, box plots, and 3D visualizations
5. **Real-time Updates**: Live chart updates for streaming data
6. **Chart Templates**: Predefined chart templates for common use cases

## Support

For technical support or feature requests related to the database visualization feature, please refer to the main system documentation or contact the development team. 