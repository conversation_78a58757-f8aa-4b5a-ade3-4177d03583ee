# Database User API Documentation

This document provides comprehensive API documentation for the Database User routes, which allow users to access their authorized databases, manage chat sessions, and interact with database schemas.

## Table of Contents

1. [Authentication & Authorization](#authentication--authorization)
2. [Accessible Databases](#accessible-databases)
3. [Database Connections](#database-connections)
4. [Chat Sessions](#chat-sessions)
5. [Database Schema](#database-schema)
6. [Error Handling](#error-handling)

## Authentication & Authorization

All endpoints require:
- **Authentication**: Valid JWT token in Authorization header
- **Authorization**: `DATABASE_CHAT` scope
- **Headers**: `Content-Type: application/json` (for POST requests)

```bash
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json
```

---

## Accessible Databases

### Get Accessible Databases

**Endpoint:** `GET /api/databases/accessible`

**Description:** Get all databases accessible to the current user with optional filtering and pagination

**Query Parameters:**
- `database_type` (optional): Filter by database type (postgresql, mysql, sqlserver, oracle)
- `permission_level` (optional): Filter by permission level (read, write, admin)
- `test_status` (optional): Filter by test status (success, failed, pending)
- `include_expired` (optional): Include expired permissions (default: false)
- `limit` (optional): Number of results per page (default: 50, max: 100)
- `offset` (optional): Number of results to skip (default: 0)

**Response:**
```json
{
  "databases": [
    {
      "id": 1,
      "name": "Production PostgreSQL",
      "db_type": "postgresql",
      "host": "db.example.com",
      "port": 5432,
      "database_name": "production_db",
      "permission_level": "read",
      "last_tested_at": "2024-01-15T10:30:00Z",
      "test_status": "success",
      "expires_at": "2024-12-31T23:59:59Z",
      "is_expired": false
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0,
  "filters_applied": {
    "database_type": null,
    "permission_level": null,
    "test_status": null,
    "include_expired": false
  }
}
```

**cURL Example:**
```bash
# Get all accessible databases
curl -X GET "http://localhost:5000/api/databases/accessible" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get only PostgreSQL databases with read permission
curl -X GET "http://localhost:5000/api/databases/accessible?database_type=postgresql&permission_level=read" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get databases with pagination
curl -X GET "http://localhost:5000/api/databases/accessible?limit=10&offset=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Database Connections

### Connect to Database

**Endpoint:** `POST /api/databases/{db_id}/connect`

**Description:** Test connection to a database and create or resume a chat session

**Path Parameters:**
- `db_id` (integer): Database ID to connect to

**Request Body:**
```json
{
  "session_name": "My Database Chat Session"
}
```

**Request Fields:**
- `session_name` (optional): Custom name for the chat session (default: auto-generated)

**Response:**
```json
{
  "success": true,
  "message": "Connected to database successfully",
  "session": {
    "id": 123,
    "session_name": "My Database Chat Session",
    "database_name": "Production PostgreSQL",
    "connected_at": "2024-01-15T10:30:00Z",
    "permission_level": "read"
  }
}
```

**Error Responses:**

**403 Forbidden - No Permission:**
```json
{
  "error": "You do not have permission to access this database"
}
```

**403 Forbidden - Expired Permission:**
```json
{
  "error": "Your permission to access this database has expired"
}
```

**404 Not Found:**
```json
{
  "error": "Database not found or inactive"
}
```

**400 Bad Request - Connection Failed:**
```json
{
  "success": false,
  "error": "Failed to connect to database: Connection timeout"
}
```

**cURL Example:**
```bash
curl -X POST "http://localhost:5000/api/databases/1/connect" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "session_name": "Production DB Analysis"
  }'
```

---

## Chat Sessions

### Get User Database Sessions

**Endpoint:** `GET /api/databases/sessions`

**Description:** Get all database chat sessions for the current user with optional filtering and pagination

**Query Parameters:**
- `database_type` (optional): Filter by database type
- `is_connected` (optional): Filter by connection status (true/false)
- `date_from` (optional): Filter sessions created from this date (ISO format)
- `date_to` (optional): Filter sessions created until this date (ISO format)
- `limit` (optional): Number of results per page (default: 50)
- `offset` (optional): Number of results to skip (default: 0)

**Response:**
```json
{
  "sessions": [
    {
      "id": 123,
      "session_name": "Production DB Analysis",
      "database_name": "Production PostgreSQL",
      "database_type": "postgresql",
      "is_connected": true,
      "connection_established_at": "2024-01-15T10:30:00Z",
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T11:45:00Z",
      "message_count": 15
    }
  ],
  "total": 1,
  "limit": 50,
  "offset": 0,
  "filters_applied": {
    "database_type": null,
    "is_connected": null,
    "date_from": null,
    "date_to": null
  }
}
```

**cURL Example:**
```bash
# Get all sessions
curl -X GET "http://localhost:5000/api/databases/sessions" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get connected sessions for PostgreSQL databases
curl -X GET "http://localhost:5000/api/databases/sessions?database_type=postgresql&is_connected=true" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get sessions from specific date range
curl -X GET "http://localhost:5000/api/databases/sessions?date_from=2024-01-01T00:00:00&date_to=2024-01-31T23:59:59" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Delete Database Session

**Endpoint:** `DELETE /api/databases/sessions/{session_id}`

**Description:** Delete (deactivate) a database chat session

**Path Parameters:**
- `session_id` (integer): Session ID to delete

**Response:**
```json
{
  "message": "Database session deleted successfully"
}
```

**Error Responses:**

**404 Not Found:**
```json
{
  "error": "Session not found"
}
```

**cURL Example:**
```bash
curl -X DELETE "http://localhost:5000/api/databases/sessions/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Database Schema

### Get Database Schema (User Version)

**Endpoint:** `GET /api/databases/{db_id}/schema`

**Description:** Get schema information for a database (limited information for users)

**Path Parameters:**
- `db_id` (integer): Database ID

**Query Parameters:**
- `schema` (optional): Specific schema name to retrieve

**Response:**
```json
{
  "success": true,
  "database_name": "Production PostgreSQL",
  "schema": {
    "tables": {
      "users": {
        "columns": [
          {
            "name": "id",
            "type": "integer",
            "nullable": false
          },
          {
            "name": "username",
            "type": "varchar(255)",
            "nullable": false
          },
          {
            "name": "email",
            "type": "varchar(255)",
            "nullable": true
          }
        ]
      }
    },
    "views": {
      "user_summary": {
        "columns": [
          {
            "name": "user_id",
            "type": "integer",
            "nullable": false
          },
          {
            "name": "total_orders",
            "type": "bigint",
            "nullable": true
          }
        ]
      }
    }
  },
  "schema_name": "public",
  "allowed_schemas": ["public", "analytics"]
}
```

**Error Responses:**

**403 Forbidden - No Permission:**
```json
{
  "error": "You do not have permission to access this database"
}
```

**403 Forbidden - Schema Permission Denied:**
```json
{
  "error": "You do not have permission to access schema \"private\""
}
```

**404 Not Found:**
```json
{
  "error": "Database not found or inactive"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to retrieve database schema",
  "details": "Connection timeout"
}
```

**cURL Example:**
```bash
# Get all schemas
curl -X GET "http://localhost:5000/api/databases/1/schema" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Get specific schema
curl -X GET "http://localhost:5000/api/databases/1/schema?schema=public" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### Get Available Schemas (User Version)

**Endpoint:** `GET /api/databases/{db_id}/schemas`

**Description:** Get list of available schemas in a database (filtered by user permissions)

**Path Parameters:**
- `db_id` (integer): Database ID

**Response:**
```json
{
  "success": true,
  "database_name": "Production PostgreSQL",
  "schemas": [
    {
      "name": "public",
      "table_count": 10,
      "view_count": 2,
      "total_objects": 12
    },
    {
      "name": "analytics",
      "table_count": 5,
      "view_count": 1,
      "total_objects": 6
    }
  ],
  "total": 2,
  "total_available": 5,
  "has_restricted_access": true
}
```

**Error Responses:**

**403 Forbidden - No Permission:**
```json
{
  "error": "You do not have permission to access this database"
}
```

**404 Not Found:**
```json
{
  "error": "Database not found or inactive"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to retrieve database schemas",
  "details": "Connection timeout"
}
```

**cURL Example:**
```bash
curl -X GET "http://localhost:5000/api/databases/1/schemas" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

---

## Error Handling

### Common Error Responses

**400 Bad Request:**
```json
{
  "error": "Invalid date_from format. Use ISO format (YYYY-MM-DDTHH:MM:SS)"
}
```

**401 Unauthorized:**
```json
{
  "error": "Authentication required"
}
```

**403 Forbidden:**
```json
{
  "error": "You do not have permission to access this database"
}
```

**404 Not Found:**
```json
{
  "error": "Database not found or inactive"
}
```

**500 Internal Server Error:**
```json
{
  "error": "Failed to fetch accessible databases"
}
```

### Permission-Related Errors

**Expired Permission:**
```json
{
  "error": "Your permission to access this database has expired"
}
```

**Schema Permission Denied:**
```json
{
  "error": "You do not have permission to access schema \"private\""
}
```

**Connection Errors:**
```json
{
  "success": false,
  "error": "Failed to connect to database: Connection timeout"
}
```

---

## JavaScript/Fetch Examples

### Get Accessible Databases
```javascript
const getAccessibleDatabases = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/databases/accessible?${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};

// Usage
const databases = await getAccessibleDatabases({
  database_type: 'postgresql',
  permission_level: 'read',
  limit: 10
});
```

### Connect to Database
```javascript
const connectToDatabase = async (dbId, sessionName = null) => {
  const body = sessionName ? { session_name: sessionName } : {};
  
  const response = await fetch(`/api/databases/${dbId}/connect`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(body)
  });
  
  return await response.json();
};

// Usage
const connection = await connectToDatabase(1, 'My Analysis Session');
if (connection.success) {
  console.log('Connected to database:', connection.session);
}
```

### Get Database Sessions
```javascript
const getDatabaseSessions = async (filters = {}) => {
  const params = new URLSearchParams(filters);
  const response = await fetch(`/api/databases/sessions?${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};

// Usage
const sessions = await getDatabaseSessions({
  database_type: 'postgresql',
  is_connected: 'true',
  limit: 20
});
```

### Get Database Schema
```javascript
const getDatabaseSchema = async (dbId, schemaName = null) => {
  const params = schemaName ? `?schema=${schemaName}` : '';
  const response = await fetch(`/api/databases/${dbId}/schema${params}`, {
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  
  return await response.json();
};

// Usage
const schema = await getDatabaseSchema(1, 'public');
if (schema.success) {
  console.log('Tables:', Object.keys(schema.schema.tables));
}
```

---

## Python Examples

### Get Accessible Databases
```python
import requests

def get_accessible_databases(token, filters=None):
    url = "http://localhost:5000/api/databases/accessible"
    headers = {
        'Authorization': f'Bearer {token}'
    }
    params = filters or {}
    
    response = requests.get(url, headers=headers, params=params)
    return response.json()

# Usage
databases = get_accessible_databases(token, {
    'database_type': 'postgresql',
    'permission_level': 'read',
    'limit': 10
})
```

### Connect to Database
```python
def connect_to_database(token, db_id, session_name=None):
    url = f"http://localhost:5000/api/databases/{db_id}/connect"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    data = {}
    if session_name:
        data['session_name'] = session_name
    
    response = requests.post(url, headers=headers, json=data)
    return response.json()

# Usage
connection = connect_to_database(token, 1, 'Analysis Session')
if connection.get('success'):
    print('Connected:', connection['session'])
```

### Get Database Schema
```python
def get_database_schema(token, db_id, schema_name=None):
    url = f"http://localhost:5000/api/databases/{db_id}/schema"
    headers = {
        'Authorization': f'Bearer {token}'
    }
    params = {}
    if schema_name:
        params['schema'] = schema_name
    
    response = requests.get(url, headers=headers, params=params)
    return response.json()

# Usage
schema = get_database_schema(token, 1, 'public')
if schema.get('success'):
    tables = schema['schema']['tables']
    print('Available tables:', list(tables.keys()))
```

---

## React Hooks Example

### Custom Hook for Database Operations
```javascript
import { useState, useEffect } from 'react';

const useDatabaseOperations = (token) => {
  const [databases, setDatabases] = useState([]);
  const [sessions, setSessions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const getAccessibleDatabases = async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/databases/accessible?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setDatabases(data.databases);
        return data;
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to fetch databases');
    } finally {
      setLoading(false);
    }
  };

  const connectToDatabase = async (dbId, sessionName) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await fetch(`/api/databases/${dbId}/connect`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ session_name: sessionName })
      });
      
      const data = await response.json();
      
      if (response.ok && data.success) {
        return data.session;
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to connect to database');
    } finally {
      setLoading(false);
    }
  };

  const getDatabaseSessions = async (filters = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams(filters);
      const response = await fetch(`/api/databases/sessions?${params}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setSessions(data.sessions);
        return data;
      } else {
        setError(data.error);
      }
    } catch (err) {
      setError('Failed to fetch sessions');
    } finally {
      setLoading(false);
    }
  };

  return {
    databases,
    sessions,
    loading,
    error,
    getAccessibleDatabases,
    connectToDatabase,
    getDatabaseSessions
  };
};

// Usage in component
const DatabaseManager = () => {
  const { 
    databases, 
    sessions, 
    loading, 
    error, 
    getAccessibleDatabases, 
    connectToDatabase 
  } = useDatabaseOperations(token);

  useEffect(() => {
    getAccessibleDatabases();
  }, []);

  const handleConnect = async (dbId) => {
    const session = await connectToDatabase(dbId, 'New Session');
    if (session) {
      console.log('Connected to database:', session);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return (
    <div>
      <h2>Accessible Databases</h2>
      {databases.map(db => (
        <div key={db.id}>
          <h3>{db.name}</h3>
          <p>Type: {db.db_type}</p>
          <p>Permission: {db.permission_level}</p>
          <button onClick={() => handleConnect(db.id)}>
            Connect
          </button>
        </div>
      ))}
    </div>
  );
};
```

---

## Best Practices

### 1. Error Handling
- Always check for error responses in your API calls
- Handle permission errors gracefully
- Provide user-friendly error messages

### 2. Session Management
- Reuse existing sessions when possible
- Clean up inactive sessions periodically
- Use meaningful session names for better organization

### 3. Schema Access
- Check schema permissions before making queries
- Use the schema endpoints to understand available data
- Respect schema restrictions set by administrators

### 4. Performance
- Use pagination for large result sets
- Apply filters to reduce data transfer
- Cache schema information when appropriate

### 5. Security
- Never expose database credentials in client-side code
- Validate all user inputs
- Use HTTPS in production environments

---

## Rate Limiting

The API implements rate limiting to prevent abuse:
- **Standard users**: 100 requests per minute
- **Premium users**: 500 requests per minute
- **Admin users**: 1000 requests per minute

Rate limit headers are included in responses:
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642248600
```

---

## Support

For API support and questions:
- Check the error responses for specific guidance
- Review the authentication and authorization requirements
- Ensure your JWT token has the required `DATABASE_CHAT` scope
- Contact your system administrator for permission issues
