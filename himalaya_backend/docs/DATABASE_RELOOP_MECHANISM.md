# Database Agent Reloop Mechanism

## Overview

The Database Agent now includes an intelligent reloop mechanism that automatically fixes failed database queries due to schema permission issues. This feature addresses the common problem where queries fail because they lack proper schema prefixes.

## Problem Statement

When users ask questions like "Give me list of top users who is using chat section", the planner generates SQL queries like:

```sql
SELECT cs.user_id, COUNT(cm.id) AS message_count 
FROM chat_sessions cs 
INNER JOIN chat_messages cm ON cs.id = cm.session_id 
GROUP BY cs.user_id 
ORDER BY message_count DESC LIMIT 10;
```

However, if the database requires schema prefixes (e.g., `public.chat_sessions` instead of just `chat_sessions`), these queries fail with schema permission errors.

## Solution: Reloop Mechanism

The reloop mechanism consists of three main components:

### 1. Query Fixer Component

The `query_fixer` method analyzes failed queries and automatically adds the appropriate schema prefixes.

**Key Features:**
- Uses AI to understand the query structure
- Identifies tables that need schema prefixes
- Preserves original query logic and structure
- Uses the first allowed schema (usually 'public')

**Example Fix:**
```sql
-- Original (fails)
SELECT * FROM chat_sessions cs JOIN chat_messages cm ON cs.id = cm.session_id

-- Fixed (works)
SELECT * FROM public.chat_sessions cs JOIN public.chat_messages cm ON cs.id = cm.session_id
```

### 2. Enhanced Executor

The `executor` method now includes reloop logic:

1. **Initial Execution**: Attempts to execute all planned queries
2. **Failure Detection**: Identifies queries that fail due to schema issues
3. **Query Fixing**: Uses the query fixer to correct failed queries
4. **Re-execution**: Re-runs the fixed queries
5. **Retry Logic**: Continues until success or max attempts reached

### 3. Schema Permission Checker

The `_check_schema_permissions` method validates queries before execution:

- Checks if tables have proper schema prefixes
- Validates against user's allowed schemas
- Prevents unauthorized schema access

## Implementation Details

### Configuration

```python
class DatabaseAgent:
    def __init__(self):
        # ... existing initialization ...
        self.max_reloop_attempts = 3  # Maximum retry attempts
```

### Reloop Flow

```python
def executor(self, plan, db_config, allowed_schemas=None, db_schema=None):
    reloop_attempts = 0
    
    while reloop_attempts <= self.max_reloop_attempts:
        # Execute queries
        failed_queries = []
        
        for step in plan['execution_steps']:
            # Execute query
            if query_fails_due_to_schema:
                failed_queries.append(query_info)
        
        # If no failures, we're done
        if not failed_queries:
            break
            
        # Try to fix queries
        if reloop_attempts < max_attempts:
            fix_result = self.query_fixer(failed_queries, allowed_schemas, db_schema)
            if fix_result['success']:
                # Update plan with fixed queries
                # Continue loop
                reloop_attempts += 1
                continue
```

### Query Fixer AI Prompt

The query fixer uses a specialized AI prompt:

```
You are a SQL query fixer. Your job is to fix failed SQL queries by adding proper schema prefixes.

Allowed schemas: ['public']

Instructions:
1. Analyze each failed query and identify which tables need schema prefixes
2. Add the appropriate schema prefix (from allowed_schemas) to each table name
3. Use the first allowed schema if multiple are available (usually 'public')
4. Preserve the original query structure and logic
5. Only fix schema-related issues, don't change the query logic

Respond in JSON format:
{
    "fixed_queries": [
        {
            "original_query": "original SQL query",
            "fixed_query": "SQL query with schema prefixes added",
            "changes_made": "Description of what was changed"
        }
    ],
    "analysis": "Summary of what was fixed"
}
```

## Usage Examples

### Example 1: Basic Schema Fix

**User Question:** "Give me list of top users who is using chat section"

**Initial Plan:**
```sql
-- Step 1: Count messages per user
SELECT cs.user_id, COUNT(cm.id) AS message_count 
FROM chat_sessions cs 
INNER JOIN chat_messages cm ON cs.id = cm.session_id 
GROUP BY cs.user_id 
ORDER BY message_count DESC LIMIT 10;

-- Step 2: Get user details
SELECT u.id, u.user_name, u.email 
FROM users u 
WHERE u.id IN (SELECT cs.user_id FROM chat_sessions cs 
               INNER JOIN chat_messages cm ON cs.id = cm.session_id 
               GROUP BY cs.user_id 
               ORDER BY COUNT(cm.id) DESC LIMIT 10);
```

**After Reloop Fix:**
```sql
-- Step 1: Count messages per user
SELECT cs.user_id, COUNT(cm.id) AS message_count 
FROM public.chat_sessions cs 
INNER JOIN public.chat_messages cm ON cs.id = cm.session_id 
GROUP BY cs.user_id 
ORDER BY message_count DESC LIMIT 10;

-- Step 2: Get user details
SELECT u.id, u.user_name, u.email 
FROM public.users u 
WHERE u.id IN (SELECT cs.user_id FROM public.chat_sessions cs 
               INNER JOIN public.chat_messages cm ON cs.id = cm.session_id 
               GROUP BY cs.user_id 
               ORDER BY COUNT(cm.id) DESC LIMIT 10);
```

### Example 2: Complex Join Fix

**User Question:** "Show me user activity with their department information"

**Initial Plan:**
```sql
SELECT u.user_name, d.department_name, COUNT(cs.id) as session_count
FROM users u
LEFT JOIN departments d ON u.department_id = d.id
LEFT JOIN chat_sessions cs ON u.id = cs.user_id
GROUP BY u.user_name, d.department_name
ORDER BY session_count DESC;
```

**After Reloop Fix:**
```sql
SELECT u.user_name, d.department_name, COUNT(cs.id) as session_count
FROM public.users u
LEFT JOIN public.departments d ON u.department_id = d.id
LEFT JOIN public.chat_sessions cs ON u.id = cs.user_id
GROUP BY u.user_name, d.department_name
ORDER BY session_count DESC;
```

## API Response Changes

The API response now includes reloop information:

```json
{
    "answer": "Based on the data, here are the top users by chat activity...",
    "sql_queries": ["SELECT cs.user_id, COUNT(cm.id) AS message_count FROM public.chat_sessions cs..."],
    "query_results": [...],
    "execution_plan": {...},
    "qa_feedback": {...},
    "token_usage": {...},
    "reloop_attempts": 1,
    "visualization_data": {...}
}
```

**New Fields:**
- `reloop_attempts`: Number of times the reloop mechanism was used to fix queries

## Error Handling

### Schema Permission Errors

When queries fail due to schema issues, the system:

1. **Detects the error pattern**: "Schema permission denied: Table 'TABLE_NAME' must be prefixed with an allowed schema"
2. **Collects failed queries**: Gathers all queries that failed for this reason
3. **Attempts to fix**: Uses AI to add proper schema prefixes
4. **Re-executes**: Runs the fixed queries
5. **Reports results**: Includes reloop attempt count in response

### Maximum Attempts

If queries continue to fail after the maximum number of attempts:

- The system stops trying to fix them
- Returns the best available results
- Includes error information in the response
- Logs the failure for debugging

## Testing

### Manual Testing

You can test the reloop mechanism by:

1. **Asking a question** that requires database access
2. **Checking the response** for `reloop_attempts` field
3. **Verifying queries** have proper schema prefixes
4. **Confirming results** are returned successfully

### Automated Testing

Run the test script:

```bash
python test_reloop_mechanism.py
```

This script tests:
- Query fixer component
- Reloop executor logic
- Schema permission checking
- AI response parsing

## Configuration Options

### Max Reloop Attempts

```python
# In DatabaseAgent.__init__()
self.max_reloop_attempts = 3  # Adjust as needed
```

### Allowed Schemas

```python
# In user permissions
allowed_schemas = ['public', 'analytics', 'reporting']
```

## Best Practices

### For Developers

1. **Monitor reloop attempts**: High reloop counts may indicate systematic issues
2. **Review failed queries**: Check logs for patterns in query failures
3. **Test with different schemas**: Ensure the fixer works with various schema configurations
4. **Validate AI fixes**: Occasionally review the AI-generated query fixes

### For Users

1. **Be specific**: Clear questions lead to better query generation
2. **Check permissions**: Ensure you have access to the required schemas
3. **Review results**: Verify the returned data matches expectations

## Troubleshooting

### Common Issues

1. **Queries still failing after reloop**
   - Check if the issue is schema-related
   - Verify user has proper permissions
   - Review database connection settings

2. **High reloop attempt counts**
   - May indicate systematic query generation issues
   - Check planner component for improvements
   - Review schema information accuracy

3. **AI fixer not working**
   - Check Azure OpenAI configuration
   - Verify API key and endpoint settings
   - Review AI model deployment status

### Debug Information

Enable debug logging to see reloop details:

```python
import logging
logging.getLogger('utils.database_agent_utils').setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Improvements

1. **Smarter Query Analysis**: Better detection of non-schema related failures
2. **Query Optimization**: AI-driven query performance improvements
3. **Schema Discovery**: Automatic detection of available schemas
4. **Custom Fix Strategies**: User-defined query fixing rules

### Performance Considerations

1. **Token Usage**: Reloop attempts increase AI token consumption
2. **Response Time**: Multiple attempts may increase response time
3. **Database Load**: Re-execution increases database query load

## Conclusion

The reloop mechanism significantly improves the user experience by automatically handling common schema permission issues. It reduces the need for manual query corrections and provides a more robust database chat experience.

The system is designed to be:
- **Intelligent**: Uses AI to understand and fix query issues
- **Safe**: Only fixes schema-related problems, preserves query logic
- **Efficient**: Limits retry attempts to prevent infinite loops
- **Transparent**: Reports reloop usage in API responses 