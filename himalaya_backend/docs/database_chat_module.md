# Database Chat Module

## Overview

The Database Chat Module allows users to interact with external databases through natural language conversations. It provides a secure, permission-based system for connecting to various database types and executing queries through an AI-powered agentic system.

## Features

### 1. Database Management (Admin)
- Add and configure external databases
- Support for multiple database types (PostgreSQL, MySQL, SQL Server, Oracle, SQLite)
- Secure credential storage with encryption
- Connection testing and validation
- Database schema introspection

### 2. User Permission System
- Granular permission levels (read, write, admin)
- Time-based permission expiration
- User-database access control
- Permission management through admin interface

### 3. Database Chat Interface
- Natural language queries to databases
- AI-powered query planning and execution
- Conversation context awareness
- Query result formatting and presentation
- Direct SQL execution for advanced users

### 4. Agentic AI System
- **Planner**: Analyzes questions and creates execution plans
- **Executor**: Safely executes SQL queries
- **Answer Maker**: Generates natural language responses
- **QA Component**: Reviews and validates responses

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Admin Panel   │    │   User Panel    │    │   Chat Interface│
│                 │    │                 │    │                 │
│ • Add Databases │    │ • View Access   │    │ • Ask Questions │
│ • Manage Users  │    │ • Connect to DB │    │ • View Results  │
│ • Set Permissions│   │ • View Schema   │    │ • Chat History  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Database Chat  │
                    │     Backend     │
                    │                 │
                    │ • Authentication│
                    │ • Authorization │
                    │ • Query Safety  │
                    │ • AI Processing │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │  Agentic System │
                    │                 │
                    │ ┌─────────────┐ │
                    │ │   Planner   │ │
                    │ └─────────────┘ │
                    │ ┌─────────────┐ │
                    │ │  Executor   │ │
                    │ └─────────────┘ │
                    │ ┌─────────────┐ │
                    │ │Answer Maker │ │
                    │ └─────────────┘ │
                    │ ┌─────────────┐ │
                    │ │QA Component │ │
                    │ └─────────────┘ │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │External Database│
                    │                 │
                    │ • PostgreSQL    │
                    │ • MySQL         │
                    │ • SQL Server    │
                    │ • Oracle        │
                    │ • SQLite        │
                    └─────────────────┘
```

## API Endpoints

### Admin Endpoints (Require SCOPE_DATABASE_ADMIN)

#### Database Management
- `GET /api/admin/databases` - List all databases
- `POST /api/admin/databases` - Create new database configuration
- `PUT /api/admin/databases/{id}` - Update database configuration
- `DELETE /api/admin/databases/{id}` - Delete database configuration
- `POST /api/admin/databases/{id}/test` - Test database connection
- `GET /api/admin/databases/{id}/schema` - Get database schema

#### Permission Management
- `GET /api/admin/databases/{id}/permissions` - List database permissions
- `POST /api/admin/databases/{id}/permissions` - Grant permission to user
- `PUT /api/admin/databases/{id}/permissions/{user_id}` - Update user permission
- `DELETE /api/admin/databases/{id}/permissions/{user_id}` - Revoke user permission

### User Endpoints (Require SCOPE_DATABASE_CHAT)

#### Database Access
- `GET /api/databases/accessible` - List accessible databases
- `POST /api/databases/{id}/connect` - Connect to database
- `GET /api/databases/{id}/schema` - Get database schema (limited)
- `GET /api/databases/sessions` - List user's chat sessions
- `DELETE /api/databases/sessions/{id}` - Delete chat session

#### Chat Interface
- `GET /api/database-chat/sessions/{id}/messages` - Get chat messages
- `POST /api/database-chat/sessions/{id}/messages` - Send chat message
- `POST /api/database-chat/sessions/{id}/execute` - Execute direct SQL (write+ permissions)

## Database Schema

### Tables

#### external_databases
Stores configuration for external databases.

```sql
CREATE TABLE external_databases (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    db_type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    database_name VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password_encrypted TEXT NOT NULL,
    connection_string_template TEXT,
    ssl_enabled BOOLEAN DEFAULT FALSE,
    ssl_cert_path VARCHAR(500),
    additional_params JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_tested_at TIMESTAMP,
    test_status VARCHAR(50) DEFAULT 'pending',
    test_error_message TEXT
);
```

#### user_database_permissions
Manages user permissions for database access.

```sql
CREATE TABLE user_database_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id) ON DELETE CASCADE,
    permission_level VARCHAR(50) DEFAULT 'read',
    granted_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    UNIQUE(user_id, external_database_id)
);
```

#### database_chat_sessions
Stores chat sessions between users and databases.

```sql
CREATE TABLE database_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    session_name VARCHAR(255),
    is_connected BOOLEAN DEFAULT FALSE,
    connection_established_at TIMESTAMP,
    connection_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### database_chat_messages
Stores individual chat messages and AI responses.

```sql
CREATE TABLE database_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES database_chat_sessions(id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sql_queries JSONB,
    query_results JSONB,
    execution_plan JSONB,
    qa_feedback JSONB,
    token_usage JSONB,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## Security Features

### 1. Credential Encryption
- Database passwords are encrypted using Fernet (AES 128)
- Encryption keys should be stored securely (Azure Key Vault in production)

### 2. Query Safety Validation
- Prevents dangerous operations (DROP, DELETE, etc.)
- Blocks SQL injection patterns
- Validates query structure before execution

### 3. Permission System
- Three permission levels: read, write, admin
- Time-based permission expiration
- Granular access control per database

### 4. Authentication & Authorization
- JWT-based authentication
- Scope-based authorization (SCOPE_DATABASE_CHAT, SCOPE_DATABASE_ADMIN)
- User session management

## Installation & Setup

### 1. Database Migration
Run the migration script to create the necessary tables:

```bash
psql -h your_host -U your_user -d your_database -f database/database_chat_migration.sql
```

### 2. Dependencies
Install required Python packages:

```bash
pip install cryptography sqlalchemy pymysql psycopg2-binary pyodbc
```

### 3. Configuration
Update `config/settings.py` with your encryption key:

```python
DATABASE_ENCRYPTION_KEY = 'your-32-byte-base64-encoded-key-here'
```

### 4. Testing
Run the test script to verify installation:

```bash
python test_database_chat.py
```

## Usage Examples

### Admin: Adding a Database

```python
POST /api/admin/databases
{
    "name": "Production PostgreSQL",
    "db_type": "postgresql",
    "host": "prod-db.company.com",
    "port": 5432,
    "database_name": "production",
    "username": "readonly_user",
    "password": "secure_password",
    "ssl_enabled": true
}
```

### Admin: Granting Permission

```python
POST /api/admin/databases/1/permissions
{
    "user_id": 123,
    "permission_level": "read",
    "expires_at": "2024-12-31T23:59:59"
}
```

### User: Connecting to Database

```python
POST /api/databases/1/connect
{
    "session_name": "Sales Data Analysis"
}
```

### User: Asking a Question

```python
POST /api/database-chat/sessions/1/messages
{
    "question": "Show me the top 10 customers by total sales this year"
}
```

## Troubleshooting

### Common Issues

1. **Connection Failures**
   - Check database credentials and network connectivity
   - Verify SSL settings and certificates
   - Review firewall and security group settings

2. **Permission Errors**
   - Ensure user has appropriate scope (SCOPE_DATABASE_CHAT)
   - Check database-specific permissions
   - Verify permission expiration dates

3. **Query Failures**
   - Review query safety validation rules
   - Check database schema and table names
   - Verify user permission level for query type

### Logs
Check application logs for detailed error messages:
- Database connection errors
- Query execution failures
- AI processing issues
- Authentication problems

## Future Enhancements

- Support for additional database types (MongoDB, Cassandra)
- Query result caching and optimization
- Advanced analytics and reporting
- Integration with data visualization tools
- Multi-language support for AI responses
- Query performance monitoring and optimization suggestions
