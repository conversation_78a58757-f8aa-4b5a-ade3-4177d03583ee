# Database Table Migration Guide

## Overview
This script copies missing tables from the UAT database (`HiGPT_Agents_UAT`) to the production database (`HiGPT_Agents`), including both table structure and data.

## Prerequisites
1. Python 3.7 or higher
2. Access to both databases
3. Required Python packages (see installation)

## Installation
```bash
pip install -r migration_requirements.txt
```

## Database Configuration
The script uses the following databases:
- **Source Database**: `HiGPT_Agents_UAT` (UAT environment)
- **Target Database**: `HiGPT_Agents` (Production environment)
- **Host**: `higpt-dbp2.postgres.database.azure.com`
- **Port**: `5432`
- **User**: `aivideoconferenceAdmin`

## Usage

### Step 1: Dry Run (Recommended)
Always run in dry-run mode first to see what tables would be copied:

```bash
python copy_missing_tables.py
```

This will:
- Connect to both databases
- Compare table structures
- Show which tables are missing in production
- Display row counts for each table
- **NOT make any changes**

### Step 2: Actual Migration
After reviewing the dry-run results, edit the script to enable actual migration:

1. Open `copy_missing_tables.py`
2. Find the line near the end: `# migrator.migrate_tables(dry_run=False)`
3. Uncomment it: `migrator.migrate_tables(dry_run=False)`
4. Run the script: `python copy_missing_tables.py`

## What the Script Does

### 1. Table Discovery
- Lists all tables in both databases
- Identifies tables present in UAT but missing in production
- Reports table statistics

### 2. Table Structure Copy
For each missing table:
- Extracts the complete CREATE TABLE statement
- Handles all PostgreSQL data types correctly
- Preserves column attributes (NOT NULL, DEFAULT values)

### 3. Data Migration
- Uses efficient batch processing for large tables
- Copies data in chunks of 1000 rows for optimal performance
- Maintains data integrity during transfer

### 4. Constraints and Indexes
- Creates primary keys immediately after table creation
- Adds unique constraints after data migration
- Creates all indexes for optimal performance
- Adds foreign key constraints last to avoid dependency issues

## Safety Features

### 1. Transaction Management
- Each table operation is wrapped in a transaction
- Automatic rollback on errors
- Commit only after successful completion

### 2. Error Handling
- Continues processing other tables if one fails
- Detailed error logging for troubleshooting
- Summary report of successful and failed migrations

### 3. Logging
- Comprehensive logging to file and console
- Timestamped log file: `table_migration_YYYYMMDD_HHMMSS.log`
- Different log levels for various operations

## Migration Process

### Order of Operations
1. **Table Creation**: Basic table structure without constraints
2. **Data Copy**: Transfer all data from source to target
3. **Primary Keys**: Add primary key constraints
4. **Unique Constraints**: Add unique constraints
5. **Indexes**: Create performance indexes
6. **Foreign Keys**: Add foreign key constraints last

### Constraint Handling
The script handles constraints in a specific order to avoid dependency issues:
- Primary keys first (required for referential integrity)
- Unique constraints next
- Regular indexes for performance
- Foreign keys last (may reference other tables)

## Common Scenarios

### 1. Empty Tables
- Tables with no data are created with structure only
- Constraints and indexes are still applied
- Logged as "empty table, skipping data copy"

### 2. Large Tables
- Processed in batches of 1000 rows
- Progress logged for monitoring
- Memory-efficient processing

### 3. Constraint Conflicts
- Primary key conflicts are logged but don't stop migration
- Foreign key constraints are added after all tables exist
- Unique constraint violations are handled gracefully

## Monitoring and Troubleshooting

### Log File Analysis
Check the log file for:
- Connection status
- Table discovery results
- Migration progress
- Error details
- Final summary

### Common Issues

1. **Connection Failures**
   - Check database credentials
   - Verify network connectivity
   - Ensure SSL requirements are met

2. **Permission Errors**
   - Verify user has CREATE TABLE permissions
   - Check schema access rights
   - Ensure ALTER TABLE permissions for constraints

3. **Constraint Conflicts**
   - Review existing data for conflicts
   - Check for naming collisions
   - Verify foreign key target tables exist

## Post-Migration Verification

### 1. Table Count Verification
```sql
-- Run on both databases
SELECT COUNT(*) as table_count 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_type = 'BASE TABLE';
```

### 2. Row Count Verification
```sql
-- For each migrated table
SELECT COUNT(*) FROM table_name;
```

### 3. Constraint Verification
```sql
-- Check constraints
SELECT table_name, constraint_name, constraint_type 
FROM information_schema.table_constraints 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

## Rollback Strategy

If you need to rollback the migration:

1. **Before Migration**: Take a database backup
2. **Selective Rollback**: Drop specific tables
   ```sql
   DROP TABLE IF EXISTS table_name CASCADE;
   ```
3. **Full Rollback**: Restore from backup

## Best Practices

1. **Always run dry-run first**
2. **Take database backup before migration**
3. **Run during maintenance window**
4. **Monitor log files during execution**
5. **Verify data integrity after migration**
6. **Test application functionality**

## Security Considerations

- Database credentials are URL-encoded
- SSL connections are enforced
- No sensitive data is logged
- Connections are properly closed

## Performance Notes

- Batch size of 1000 rows balances memory and performance
- Indexes are created after data migration for speed
- Foreign keys are added last to avoid constraint checking during data load
- Transaction boundaries minimize lock time

## Script Customization

To modify the script behavior:

1. **Batch Size**: Change `batch_size = 1000` in `copy_table_data()`
2. **Log Level**: Modify `level=logging.INFO` in logging configuration
3. **Database Names**: Update `source_db` and `target_db` in `__init__()`
4. **Connection Parameters**: Modify connection strings as needed

## Support

For issues or questions:
1. Check the log file for detailed error messages
2. Verify database connectivity manually
3. Test with a single table first
4. Contact database administrator if needed 