# System Limitations and Best Practices

## Executive Summary

This document outlines the technical limitations and recommended best practices for the HiGPT AI system. Understanding these constraints will help ensure optimal performance and user experience when interacting with the intelligent document analysis platform.

## Infrastructure Constraints

### 1. Container Resource Limitations

The system operates within the following Azure Container App resource constraints:

- **CPU Allocation**: 0.5 vCPU per container instance
- **Memory Allocation**: 1 GB RAM per container instance  
- **Storage**: 2 GB ephemeral storage per container
- **Scaling**: Maximum 2 container instances with auto-scaling based on concurrent requests (10 concurrent requests trigger scaling)

**Impact**: These resource constraints directly affect the system's ability to process large numbers of files simultaneously and handle complex queries across multiple documents.

## Document Processing Limitations

### 1. File Upload Constraints

- **Maximum File Size**: 100 MB per document
- **Maximum Pages**: 1,000 pages per document
- **Maximum Tables**: 100 tables per document
- **Supported Formats**: PDF, Excel (.xlsx, .xls, .csv), Word (.docx, .doc), PowerPoint (.pptx, .ppt), Text (.txt, .md, .html), Images (.jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp)
- **Processing Timeout**: 30 minutes per document

**Reason**: These limits ensure system stability and prevent resource exhaustion during document processing.

### 2. Simultaneous File Processing

- **Maximum Concurrent Processing**: 3 files simultaneously
- **Queue Management**: Additional files are queued for processing
- **Retry Mechanism**: Up to 3 retry attempts for failed processing

**Impact**: Large batch uploads may experience delays as files are processed sequentially after the initial 3 concurrent slots.

## Query Performance Limitations

**Technical Reasons**:
- **Memory Constraints**: 1 GB RAM limit restricts the amount of context that can be held in memory
- **Vector Search Limitations**: System retrieves top 20 results initially, then re-ranks to top 10 for final analysis
- **Context Window Limitations**: GPT-4 model has finite context windows that become saturated with large numbers of documents
- **Processing Time**: Increased computational overhead for cross-document analysis

**Recommendation**: For optimal results, limit queries to 10-12 files maximum. For analysis requiring more files, consider breaking the query into focused sub-queries.

### 2. Deep Search vs. Normal Search

**When to Use Deep Search**:
- Querying 6-7+ files where comprehensive coverage is required
- Complex analytical queries requiring information from multiple sources
- When normal search results appear incomplete or lack context from specific files

**Technical Difference**:

**Resource Impact**: Deep search consumes more CPU and memory resources, potentially affecting response times.

### 3. Context Management

**Session-Level Context Mixing**: 
- **Limitation**: Conversation history is limited to 5 previous messages for performance optimization
- **Issue**: When multiple unrelated topics are discussed in a single session, context from previous conversations may interfere with current queries

**Recommendation**: Start a new chat session when switching to unrelated topics or when responses seem to conflate different contexts.

## Best Practices for Optimal Performance

### 1. Query Optimization

**Effective Prompt Engineering**:
- Use specific, well-structured queries rather than vague questions
- Include relevant keywords and context
- Specify the type of analysis or information required
- Reference specific documents or sections when applicable

**Example of Poor Query**: "Tell me about the data"
**Example of Optimized Query**: "Analyze the quarterly sales performance trends from the financial reports, comparing Q1 and Q2 revenue figures across different product categories"

### 2. File Selection Strategy

- **Optimal Range**: 6-10 files for comprehensive analysis
- **Maximum Effective Range**: 12-15 files (with potential quality degradation)
- **File Relevance**: Select only files directly relevant to your query
- **File Organization**: Use descriptive file names to aid system understanding

### 3. Session Management

- **Topic Separation**: Start new sessions for unrelated topics
- **Context Clarity**: Provide clear context in the first message of each session
- **Session Length**: Keep sessions focused on related queries to maintain context quality

### 4. Search Strategy Selection

**Use Normal Search When**:
- Querying 1-5 files
- Looking for specific information or direct answers
- Speed is prioritized over comprehensiveness

**Use Deep Search When**:
- Querying 6+ files
- Requiring comprehensive analysis across multiple documents
- Normal search results seem incomplete
- Conducting comparative analysis across documents

 
## Conclusion

Understanding these limitations and following the recommended best practices will ensure optimal performance and user satisfaction. Regular monitoring and user feedback will help identify opportunities for system optimization and enhancement.

 

   
 