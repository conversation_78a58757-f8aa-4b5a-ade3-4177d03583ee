# 🤖 AI Agentic Chat Platform - Complete User Manual

## Table of Contents

1. [Platform Overview](#platform-overview)
2. [Getting Started](#getting-started)
3. [Understanding the 5 AI Modes](#understanding-the-5-ai-modes)
4. [File Upload Guidelines](#file-upload-guidelines)
5. [System Capabilities & Limitations](#system-capabilities--limitations)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)
8. [Advanced Features](#advanced-features)

---

## Platform Overview

Welcome to the AI Agentic Chat Platform - an intelligent document analysis and conversation system that combines **5 specialized AI agents** to help you interact with your documents and get answers to your questions.

### What This Platform Can Do ✅

- **Analyze Text-Based Documents**: PDFs, Word docs(.docx), Excel files, Images with text PowerPoint presentations(yet to integrate)
- **Extract Text from Images**: Using OCR technology to read text from scanned documents, charts, and screenshots
- **Search the Web**: Get real-time information from the internet
- **Answer General Questions**: Using pre-trained AI knowledge
- **Perform Complex Analysis**: Deep research across multiple documents
 

### What This Platform Cannot Do ❌

- **Analyze Pure Visual Content**: Cannot understand pictures of buildings, people, landscapes, artwork, or other non-text imagery
- **Understand Visual Charts Without Text**: Cannot interpret graphs, diagrams, or charts unless they contain readable text

---

## Getting Started

### 1. Create a Chat Session

1. Click **"Start New Conversation"**
2. Give your session a descriptive name (optional - the system will auto-generate one)
3. Select your department/vertical if required

### 2. Choose Your AI Mode

Before asking questions, select one of the **5 AI modes** based on your needs:

| Mode | Icon | Use When | File Selection Options | Expectation |
|------|------|----------|----------------------|------------|
| **Work** 📁 | Work mode | Analyzing uploaded documents | All files, selected files, only my files | Provides answers based strictly on your uploaded documents with direct citations. Extracts specific data, quotes, and information from PDFs, Excel files, Word docs. Response quality depends on document relevance and content quality. |
| **Web Search** 🌐 | Globe icon | Need current/real-time information | Not allowed to select files | Delivers current, real-time information from the internet. Provides recent news, stock prices, weather updates, and trending topics. No access to your documents - purely web-based responses with source links. |
| **GPT** 🧠 | Brain icon | General knowledge questions | Not allowed to select files | Offers comprehensive explanations using pre-trained knowledge. Best for educational content, concept explanations, and general advice. No document access or web search - relies on existing AI knowledge base. |
| **Hybrid** ⭐ | Star icon | Complex queries needing multiple sources | Allow to select files | Intelligently combines document analysis, web search, and general knowledge as needed. Automatically determines the best approach for your query. May use documents + web data for comprehensive answers. |
| **Deep Think** 🔍 | Magnifying glass | In-depth research with citations | Only filtered files or selected files | Conducts thorough analysis across multiple selected documents with detailed citations, page references, and cross-document comparisons. Takes longer but provides comprehensive research-level responses with complete source attribution. |

### 3. Upload Files (If Needed)

- Click the **file upload button**
- Select your documents
- Wait for processing to complete
- Green checkmarks indicate successful processing

---

## Understanding the 5 AI Modes

### 📁 **Work Mode (RAG - Retrieval Augmented Generation)**

**When to Use:**
- Analyzing your uploaded documents
- Finding specific information in PDFs, Excel files, Word docs
- Answering questions based on your uploaded document

**Best For:**
- "What are the key findings in this report?"
- "Summarize the financial data in these Excel files"
- "Find information about project timeline in the documents"

**File Selection:**
- **All Files**: Searches across all accessible documents
- **Selected Files**: Search only specific documents you choose
- **Filtered**: Search by department, date range, or tags

---

### 🌐 **Web Search Mode**

**When to Use:**
- Current events and real-time information
- Recent news, stock prices, weather
- Information not in your documents
- Fact-checking and verification

**Best For:**
- "What's the latest news about AI developments?"
- "Current stock price of Apple"
- "What happened in the markets today?"

**Note:** Does not use your uploaded documents

---

### 🧠 **GPT Mode**

**When to Use:**
- General knowledge questions
- Explanations of concepts
- Educational content
- Questions that don't require specific documents or current data

**Best For:**
- "Explain how machine learning works"
- "What is the difference between AI and ML?"
- "How do solar panels work?"

**Note:** Uses pre-trained knowledge, no documents or web search

---

### ⭐ **Hybrid Mode**

**When to Use:**
- Complex questions that might need multiple information sources
- When you're unsure which mode to use
- Questions that might require both documents AND web search
- Comprehensive research queries

**Best For:**
- "Compare our sales data with industry trends"
- "Analyze our performance against market conditions"
- "What do our documents say about AI, and what's the latest industry news?"

**How it Works:** Automatically combines Work, Web Search, and GPT modes as needed

---

### 🔍 **Deep Think Mode (Deep Search)**

**When to Use:**
- Complex research requiring multiple documents
- In-depth analysis with detailed citations
- When you need comprehensive coverage of selected files
- Academic or professional research

**Best For:**
- "Conduct a comprehensive analysis of these 10 research papers"
- "Compare findings across all quarterly reports"
- "Provide detailed analysis with specific citations"

**Requirements:**
- ⚠️ **Must select specific files** (cannot use "All Files")
- Works best with 3-15 documents
- Takes longer but provides more detailed answers

---

## File Upload Guidelines

### ✅ Supported File Types

| Document Type | Extensions | What We Can Extract |
|---------------|------------|-------------------|
| **PDF** | .pdf | Text, tables, forms, scanned text (OCR) |
| **Excel** | .xlsx, .xls, .csv | Data, formulas, charts with text |
| **Word** | .docx, .doc | Text, tables, formatted content |
| **PowerPoint** | .pptx, .ppt | Slide text, speaker notes |
| **Text Files** | .txt, .md, .html | Plain text content |
| **Images** | .jpg, .png, .gif, .bmp, .tiff | Text via OCR only |

### 📏 File Limits

- **Maximum file size:** 100 MB per file
- **Maximum pages:** 1,000 pages per document
- **Processing time:** Up to 30 minutes per large file
- **Concurrent processing:** 3 files at once

### 🖼️ Image Upload Guidelines

**✅ Good for Images:**
- Screenshots of documents or web pages
- Scanned documents with text
- Charts and graphs with text labels
- Forms and invoices
- Presentations saved as images

**❌ Not Suitable for Images:**
- Photos of people, buildings, landscapes
- Artwork, paintings, drawings
- Product photos without text
- Screenshots of videos
- Images without readable text

**Example Messages for Image Issues:**
> ❌ "Can you tell me about this building?" (showing a photo of architecture)
> 
> ✅ "Can you extract the text from this document screenshot?"

---

## System Capabilities & Limitations

### ⚡ Performance Characteristics

**Resource Constraints:**
- **Memory:** 1 GB RAM per session
- **Storage:** 2 GB for file processing
- **CPU:** 0.5 vCPU allocation
- **Concurrent users:** Auto-scaling based on demand

**Query Performance:**
- **Optimal file count:** 5-10 files per query
- **Maximum recommended:** 12 files per query
- **Deep Search limit:** 25 files maximum
- **Response time:** 10-60 seconds depending on complexity

### 🎯 Accuracy Guidelines

**High Accuracy Scenarios:**
- Text-based document analysis
- Structured data in Excel/CSV files
- OCR of clear, typed text
- Current web information

**Lower Accuracy Scenarios:**
- Handwritten text in images
- Poor quality scanned documents
- Complex multi-column layouts
- Images with background interference

### 🔄 Context Management

**Session Memory:**
- Remembers last 5 conversations in the session
- Maintains context within a single chat session
- **Best Practice:** Start new sessions for unrelated topics

---

## Best Practices

### 📝 Writing Effective Queries

**✅ Good Query Examples:**

```
Specific and Clear:
"What are the revenue figures for Q3 2024 in the financial report?"

Context-Rich:
"Summarize the key risks mentioned in the risk assessment document, focusing on financial and operational risks"

Action-Oriented:
"Compare the sales performance across all regions in the quarterly reports and identify the top 3 performing areas"
```

**❌ Poor Query Examples:**

```
Too Vague:
"Tell me about the data"

Ambiguous References:
"What does it say about them?" (unclear what "it" and "them" refer to)

Visual Content Requests:
"What does this picture show?" (for non-text images)
```

### 📊 Data Visualization Requests

**Effective Visualization Queries:**

```
For Charts:
"Show me a bar chart comparing sales across quarters"
"Create a line graph of revenue trends over time"
"Generate a pie chart of budget allocation by department"

For Tables:
"Create a table showing all employee data by department"
"List the top 10 products by sales volume"
"Show me a breakdown of expenses by category"
```

### 🎯 Mode Selection Strategy

**Decision Tree:**

1. **Need current information?** → Use **Web Search** 🌐
2. **Have relevant documents?** → Use **Work** 📁
3. **General knowledge question?** → Use **GPT** 🧠
4. **Complex research across multiple sources?** → Use **Hybrid** ⭐
5. **In-depth analysis with citations?** → Use **Deep Think** 🔍

### 📁 File Organization Tips

**For Better Results:**
- Upload related documents together
- Use descriptive file names
- Group files by project or topic
- Remove duplicate or outdated files
- Keep file sizes reasonable (<50 MB when possible)

---

## Troubleshooting

### 🚫 Common Issues and Solutions

#### **"I uploaded an image but the AI can't understand it"**

**Likely Cause:** The image contains visual content (photos, artwork, diagrams) rather than text

**Solutions:**
- Ensure images contain clear, readable text
- Use high-contrast, well-lit images
- For photos of objects/people: The system cannot process these
- Try converting image to PDF if it contains document text

#### **"The AI says no files found matching criteria"**

**Solutions:**
- Check file permissions and access rights
- Verify files completed processing (green checkmarks)
- Try selecting "All Files" instead of filters
- Ensure files contain relevant content for your query

#### **"Response is incomplete or doesn't cover all my files"**

**Solutions:**
- Switch to **Deep Think** mode for comprehensive coverage
- Break down complex queries into smaller parts
- Ensure query is specific enough to guide the search
- Try rephrasing with more specific keywords

#### **"Processing is taking too long"**

**Normal Processing Times:**
- Simple queries: 10-30 seconds
- Complex queries: 30-90 seconds
- Deep Search: 2-5 minutes
- Large file uploads: 5-30 minutes

**If stuck:**
- Refresh the page and try again
- Reduce number of files in the query
- Try a simpler version of your question first

### 🔧 File Processing Issues

#### **File Upload Fails**

**Check:**
- File size under 100 MB
- Supported file format
- Stable internet connection
- Browser allows file uploads

#### **OCR Not Working on Images**

**Requirements for OCR:**
- Clear, readable text
- High contrast (dark text on light background)
- Minimal rotation or skewing
- Text should be at least 12pt font size

---

## Advanced Features

### 📈 Data Visualization

The platform can automatically generate charts and graphs from your data:

**Automatic Detection:**
- System detects when visualizations would be helpful
- Suggests appropriate chart types based on data

**Supported Chart Types:**
- **Bar Charts:** Comparing categories
- **Line Charts:** Trends over time
- **Pie Charts:** Parts of a whole
- **Scatter Plots:** Relationships between variables

**Requesting Visualizations:**
```
"Show me a bar chart of sales by region"
"Create a line graph showing revenue trends"
"Generate a pie chart of expense distribution"
```

### 🔄 Session Management

**Multiple Sessions:**
- Create different sessions for different projects
- Sessions maintain their own conversation history
- Switch between sessions without losing context

**Session Export:**
- Export chat history as CSV files
- Include questions, answers, and sources
- Useful for reporting and documentation

### 🏷️ File Filtering and Organization

**Filter Options:**
- **By Department:** Access files specific to your team
- **By Date Range:** Find recent or historical documents
- **By Tags:** Organize files with custom labels
- **By File Type:** Focus on specific document types

**Tag System:**
- Add custom tags to uploaded files
- Search by tags for better organization
- Group related documents together

### 🔍 Advanced Search Features

**Search Modes:**
- **Semantic Search:** Finds content by meaning, not just keywords
- **Exact Match:** Find specific phrases or terms
- **Cross-Reference:** Find connections between documents

**Search Operators:**
```
"financial data" AND "Q3"          # Both terms required
"revenue" OR "sales"               # Either term
"project timeline" -"completed"    # Exclude completed projects
```

---

## Summary

This AI Agentic Chat Platform is designed to be your intelligent document analysis companion. By understanding its capabilities and limitations, you can make the most of its powerful features:

### ✅ **Remember to:**
- Choose the right AI mode for your task
- Upload text-based documents for best results
- Write clear, specific queries
- Use Deep Think mode for comprehensive research
- Start new sessions for different topics

### ❌ **Keep in Mind:**
- The system works with text content, not visual imagery
- File size and processing limits apply
- Complex queries may take time to process
- Context is maintained within sessions only

### 🎯 **For Best Results:**
- Combine different modes as needed
- Organize your files thoughtfully
- Be specific in your requests
- Use visualization features for data analysis
- Export important conversations for record-keeping

---

**Need Help?** If you encounter issues not covered in this manual, try rephrasing your question, checking file formats, or starting a new session with a simpler query to troubleshoot the problem. 