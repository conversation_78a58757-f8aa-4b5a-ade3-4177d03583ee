#!/usr/bin/env python3
"""
Flask-SQLAlchemy Model Migration Script
This script creates all database tables based on the current models.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from flask import Flask
from sqlalchemy import text
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_app():
    """Create Flask app with database configuration"""
    app = Flask(__name__)
    
    # Import configuration
    try:
        from src.config.settings import FLASK_CONFIG
        app.config.update(FLASK_CONFIG)
    except ImportError:
        logger.error("Could not import Flask configuration. Please check config/settings.py")
        sys.exit(1)
    
    return app

def run_migration():
    """Run the database migration using Flask-SQLAlchemy"""
    logger.info("🚀 Starting Database Migration")
    logger.info("=" * 50)
    
    try:
        # Create Flask app
        app = create_app()
        
        # Import database and models
        from src.models.models import (
            db, User, Vertical, Department, Scope, Position,
            ExternalDatabase, DatabaseChatSession, DatabaseChatMessage,
            user_database_permissions, user_verticals, user_departments
        )
        
        # Initialize database with app
        db.init_app(app)
        
        with app.app_context():
            logger.info("📊 Checking current database state...")
            
            # Check if tables exist
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            
            # List of new tables for Database Chat module
            new_tables = [
                'external_databases',
                'user_database_permissions', 
                'database_chat_sessions',
                'database_chat_messages'
            ]
            
            # Check which tables need to be created
            tables_to_create = []
            for table in new_tables:
                if table not in existing_tables:
                    tables_to_create.append(table)
                else:
                    logger.info(f"⏭️  Table already exists: {table}")
            
            if tables_to_create:
                logger.info(f"📝 Creating {len(tables_to_create)} new tables...")
                
                # Create all tables
                db.create_all()
                
                for table in tables_to_create:
                    logger.info(f"✅ Created table: {table}")
            else:
                logger.info("✅ All tables already exist")
            
            # Add new scopes
            logger.info("🔐 Managing scopes...")
            add_database_scopes(db)
            
            # Create indexes
            logger.info("📈 Creating indexes...")
            create_indexes(db)
            
            # Create triggers
            logger.info("⚡ Creating triggers...")
            create_triggers(db)
            
            # Add comments
            logger.info("📝 Adding table comments...")
            add_table_comments(db)
            
            logger.info("=" * 50)
            logger.info("🎉 Database Migration Completed Successfully!")
            
            # Show summary
            show_migration_summary(db)
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def add_database_scopes(db):
    """Add new scopes for database functionality"""
    from src.models.models import Scope, User
    
    new_scopes = [
        ('DATABASE_CHAT', 'Permission to chat with databases'),
        ('DATABASE_ADMIN', 'Permission to manage database configurations')
    ]
    
    for scope_name, scope_description in new_scopes:
        existing_scope = Scope.query.filter_by(name=scope_name).first()
        if not existing_scope:
            scope = Scope(name=scope_name)
            db.session.add(scope)
            logger.info(f"✅ Added scope: {scope_name}")
        else:
            logger.info(f"⏭️  Scope already exists: {scope_name}")
    
    db.session.commit()
    
    # Update admin users with new scopes
    logger.info("👑 Updating admin user scopes...")
    
    # Get scope IDs
    db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
    db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
    
    if db_chat_scope and db_admin_scope:
        admin_users = User.query.filter_by(is_admin=True).all()
        
        for admin in admin_users:
            current_scopes = admin.scopes or []
            new_scopes_to_add = []
            
            if db_chat_scope.id not in current_scopes:
                new_scopes_to_add.append(db_chat_scope.id)
            
            if db_admin_scope.id not in current_scopes:
                new_scopes_to_add.append(db_admin_scope.id)
            
            if new_scopes_to_add:
                admin.scopes = list(set(current_scopes + new_scopes_to_add))
                logger.info(f"✅ Updated scopes for admin: {admin.email}")
        
        db.session.commit()
    else:
        logger.warning("⚠️  Could not find new scopes to assign to admins")

def create_indexes(db):
    """Create performance indexes"""
    indexes = [
        "CREATE INDEX IF NOT EXISTS idx_external_databases_created_by ON external_databases(created_by);",
        "CREATE INDEX IF NOT EXISTS idx_external_databases_is_active ON external_databases(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_user_database_permissions_user_id ON user_database_permissions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_user_database_permissions_database_id ON user_database_permissions(external_database_id);",
        "CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_user_id ON database_chat_sessions(user_id);",
        "CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_database_id ON database_chat_sessions(external_database_id);",
        "CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_is_active ON database_chat_sessions(is_active);",
        "CREATE INDEX IF NOT EXISTS idx_database_chat_messages_session_id ON database_chat_messages(session_id);",
        "CREATE INDEX IF NOT EXISTS idx_database_chat_messages_created_at ON database_chat_messages(created_at);"
    ]
    
    for index_sql in indexes:
        try:
            db.session.execute(text(index_sql))
            index_name = index_sql.split()[5]  # Extract index name
            logger.info(f"✅ Created index: {index_name}")
        except Exception as e:
            logger.warning(f"⚠️  Index creation warning: {str(e)}")
    
    db.session.commit()

def create_triggers(db):
    """Create automatic timestamp update triggers"""
    # Create trigger function
    trigger_function_sql = """
    CREATE OR REPLACE FUNCTION update_updated_at_column()
    RETURNS TRIGGER AS $$
    BEGIN
        NEW.updated_at = CURRENT_TIMESTAMP;
        RETURN NEW;
    END;
    $$ language 'plpgsql';
    """
    
    # Create triggers for tables with updated_at columns
    triggers = [
        ("update_external_databases_updated_at", "external_databases"),
        ("update_database_chat_sessions_updated_at", "database_chat_sessions")
    ]
    
    try:
        # Create the trigger function
        db.session.execute(text(trigger_function_sql))
        logger.info("✅ Created trigger function: update_updated_at_column")
        
        # Create triggers
        for trigger_name, table_name in triggers:
            trigger_sql = f"""
            DROP TRIGGER IF EXISTS {trigger_name} ON {table_name};
            CREATE TRIGGER {trigger_name}
                BEFORE UPDATE ON {table_name}
                FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """
            db.session.execute(text(trigger_sql))
            logger.info(f"✅ Created trigger: {trigger_name}")
        
        db.session.commit()
        
    except Exception as e:
        logger.warning(f"⚠️  Trigger creation warning: {str(e)}")

def add_table_comments(db):
    """Add documentation comments to tables"""
    comments = [
        ("external_databases", "Configuration for external databases that users can connect to and chat with"),
        ("user_database_permissions", "User permissions for accessing specific external databases"),
        ("database_chat_sessions", "Chat sessions between users and external databases"),
        ("database_chat_messages", "Individual messages in database chat sessions with AI responses")
    ]
    
    for table_name, comment in comments:
        try:
            db.session.execute(text(f"COMMENT ON TABLE {table_name} IS :comment"), {"comment": comment})
            logger.info(f"✅ Added comment to: {table_name}")
        except Exception as e:
            logger.warning(f"⚠️  Comment warning for {table_name}: {str(e)}")
    
    db.session.commit()

def show_migration_summary(db):
    """Show summary of migration results"""
    inspector = db.inspect(db.engine)
    tables = inspector.get_table_names()
    
    # Count tables
    database_chat_tables = [t for t in tables if t.startswith(('external_databases', 'database_chat', 'user_database_permissions'))]
    
    logger.info("📋 Migration Summary:")
    logger.info(f"  • Total tables in database: {len(tables)}")
    logger.info(f"  • Database Chat tables: {len(database_chat_tables)}")
    
    # Check scopes
    from src.models.models import Scope
    db_scopes = Scope.query.filter(Scope.name.in_(['DATABASE_CHAT', 'DATABASE_ADMIN'])).all()
    logger.info(f"  • Database Chat scopes: {len(db_scopes)}")
    
    # Check admin users
    from src.models.models import User
    admin_count = User.query.filter_by(is_admin=True).count()
    logger.info(f"  • Admin users updated: {admin_count}")
    
    logger.info("🚀 Database is ready for Database Chat functionality!")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database Migration Script')
    parser.add_argument('--check-only', action='store_true', help='Only check database state without making changes')
    
    args = parser.parse_args()
    
    if args.check_only:
        logger.info("🔍 CHECK MODE - Analyzing database state...")
        # Add check-only logic here if needed
        return
    
    success = run_migration()
    
    if success:
        logger.info("✅ Migration completed successfully!")
        logger.info("💡 Next steps:")
        logger.info("  1. Restart your Flask application")
        logger.info("  2. Test the Database Chat functionality")
        logger.info("  3. Add external databases through the admin interface")
    else:
        logger.error("❌ Migration failed!")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
