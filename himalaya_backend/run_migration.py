#!/usr/bin/env python3
"""
Database Migration Runner for Database Chat Module
This script safely migrates the database to support Database Chat functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Main migration function"""
    print("🚀 Database Chat Module Migration")
    print("=" * 50)
    
    try:
        # Step 1: Import and setup Flask app
        print("1️⃣  Setting up Flask application...")
        from flask import Flask
        app = Flask(__name__)
        
        # Load configuration
        from src.config.settings import FLASK_CONFIG
        app.config.update(FLASK_CONFIG)
        print("   ✅ Flask app configured")
        
        # Step 2: Initialize database
        print("2️⃣  Initializing database connection...")
        from src.models.models import db
        db.init_app(app)
        print("   ✅ Database connection initialized")
        
        with app.app_context():
            # Step 3: Import all models
            print("3️⃣  Loading database models...")
            from src.models.models import (
                User, Vertical, Department, Scope, Position,
                ExternalDatabase, DatabaseChatSession, DatabaseChatMessage,
                user_database_permissions
            )
            print("   ✅ All models loaded")
            
            # Step 4: Test database connection
            print("4️⃣  Testing database connection...")
            try:
                db.session.execute(db.text("SELECT 1"))
                print("   ✅ Database connection successful")
            except Exception as e:
                print(f"   ❌ Database connection failed: {e}")
                return False
            
            # Step 5: Check existing tables
            print("5️⃣  Checking existing database structure...")
            inspector = db.inspect(db.engine)
            existing_tables = inspector.get_table_names()
            print(f"   ✅ Found {len(existing_tables)} existing tables")
            
            # Step 6: Create new tables
            print("6️⃣  Creating new tables...")
            new_tables = [
                'external_databases',
                'user_database_permissions',
                'database_chat_sessions',
                'database_chat_messages'
            ]
            
            tables_created = 0
            for table_name in new_tables:
                if table_name not in existing_tables:
                    tables_created += 1
                    print(f"   📝 Will create: {table_name}")
                else:
                    print(f"   ⏭️  Already exists: {table_name}")
            
            # Create all tables (safe to run multiple times)
            db.create_all()
            print(f"   ✅ Database schema updated ({tables_created} new tables)")
            
            # Step 7: Add new scopes
            print("7️⃣  Managing database scopes...")
            
            # DATABASE_CHAT scope
            db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
            if not db_chat_scope:
                db_chat_scope = Scope(name='DATABASE_CHAT')
                db.session.add(db_chat_scope)
                print("   ✅ Added DATABASE_CHAT scope")
            else:
                print("   ⏭️  DATABASE_CHAT scope exists")
            
            # DATABASE_ADMIN scope
            db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            if not db_admin_scope:
                db_admin_scope = Scope(name='DATABASE_ADMIN')
                db.session.add(db_admin_scope)
                print("   ✅ Added DATABASE_ADMIN scope")
            else:
                print("   ⏭️  DATABASE_ADMIN scope exists")
            
            # Commit scope changes
            db.session.commit()
            
            # Step 8: Update admin users
            print("8️⃣  Updating admin user permissions...")
            
            # Refresh scope objects after commit
            db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
            db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            
            admin_users = User.query.filter_by(is_admin=True).all()
            updated_admins = 0
            
            for admin in admin_users:
                current_scopes = admin.scopes or []
                updated = False
                
                if db_chat_scope and db_chat_scope.id not in current_scopes:
                    current_scopes.append(db_chat_scope.id)
                    updated = True
                
                if db_admin_scope and db_admin_scope.id not in current_scopes:
                    current_scopes.append(db_admin_scope.id)
                    updated = True
                
                if updated:
                    admin.scopes = current_scopes
                    updated_admins += 1
                    print(f"   ✅ Updated: {admin.email}")
                else:
                    print(f"   ⏭️  Already current: {admin.email}")
            
            if updated_admins > 0:
                db.session.commit()
                print(f"   ✅ Updated {updated_admins} admin users")
            else:
                print("   ✅ All admin users already have correct scopes")
            
            # Step 9: Verify migration
            print("9️⃣  Verifying migration results...")
            
            # Check tables
            final_tables = db.inspect(db.engine).get_table_names()
            missing_tables = []
            for table in new_tables:
                if table in final_tables:
                    print(f"   ✅ Verified: {table}")
                else:
                    print(f"   ❌ Missing: {table}")
                    missing_tables.append(table)
            
            if missing_tables:
                print(f"   ❌ Migration incomplete - missing tables: {missing_tables}")
                return False
            
            # Check scopes
            final_db_chat = Scope.query.filter_by(name='DATABASE_CHAT').first()
            final_db_admin = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            
            if final_db_chat and final_db_admin:
                print("   ✅ Database scopes verified")
            else:
                print("   ❌ Database scopes missing")
                return False
            
            # Final success message
            print("=" * 50)
            print("🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            print()
            print("📋 Migration Summary:")
            print(f"   • Tables created/verified: {len(new_tables)}")
            print(f"   • Scopes added: DATABASE_CHAT (ID: {final_db_chat.id}), DATABASE_ADMIN (ID: {final_db_admin.id})")
            print(f"   • Admin users updated: {len(admin_users)}")
            print()
            print("🚀 Next Steps:")
            print("   1. Restart your Flask application")
            print("   2. Log in as admin to access Database Management")
            print("   3. Add external databases via Admin > Database Management")
            print("   4. Assign database permissions to users")
            print("   5. Test Database Chat functionality")
            print()
            print("📚 Documentation:")
            print("   • API Documentation: docs/database_chat_module.md")
            print("   • Migration Guide: DATABASE_MIGRATION_README.md")
            print()
            print("🔒 Security Note:")
            print("   • This is a READ-ONLY chat system")
            print("   • Only SELECT queries are allowed")
            print("   • No data modification operations permitted")
            print("   • Permission levels: 'read' and 'admin' only")
            
            return True
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the correct directory and all dependencies are installed")
        return False
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("Your database is now ready for Database Chat functionality.")
    else:
        print("\n❌ Migration failed!")
        print("Please check the error messages above and try again.")
    
    sys.exit(0 if success else 1)
