#!/usr/bin/env python3
"""
Database Migration Script for Database Chat Module
This script creates or updates the database schema for the Database Chat functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import create_engine, text, inspect, MetaData
from sqlalchemy.exc import SQLAlchemyError
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Handles database migration for Database Chat module"""
    
    def __init__(self, database_url=None):
        """Initialize migrator with database URL"""
        if database_url:
            self.database_url = database_url
        else:
            # Try to get from config
            try:
                from src.config.settings import FLASK_CONFIG
                self.database_url = FLASK_CONFIG['SQLALCHEMY_DATABASE_URI']
            except ImportError:
                logger.error("Could not import database URL from config. Please provide database_url parameter.")
                sys.exit(1)
        
        self.engine = create_engine(self.database_url)
        self.inspector = inspect(self.engine)
    
    def table_exists(self, table_name):
        """Check if a table exists in the database"""
        return table_name in self.inspector.get_table_names()
    
    def column_exists(self, table_name, column_name):
        """Check if a column exists in a table"""
        if not self.table_exists(table_name):
            return False
        columns = [col['name'] for col in self.inspector.get_columns(table_name)]
        return column_name in columns
    
    def index_exists(self, index_name):
        """Check if an index exists"""
        try:
            with self.engine.connect() as conn:
                result = conn.execute(text("""
                    SELECT indexname FROM pg_indexes 
                    WHERE indexname = :index_name
                """), {"index_name": index_name})
                return result.fetchone() is not None
        except Exception:
            return False
    
    def create_external_databases_table(self):
        """Create external_databases table"""
        logger.info("Creating external_databases table...")
        
        sql = """
        CREATE TABLE IF NOT EXISTS external_databases (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            db_type VARCHAR(50) NOT NULL,
            host VARCHAR(255) NOT NULL,
            port INTEGER NOT NULL,
            database_name VARCHAR(255) NOT NULL,
            username VARCHAR(255) NOT NULL,
            password_encrypted TEXT NOT NULL,
            connection_string_template TEXT,
            ssl_enabled BOOLEAN DEFAULT FALSE,
            ssl_cert_path VARCHAR(500),
            additional_params JSONB,
            is_active BOOLEAN DEFAULT TRUE,
            created_by INTEGER NOT NULL REFERENCES users(id),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_tested_at TIMESTAMP,
            test_status VARCHAR(50) DEFAULT 'pending',
            test_error_message TEXT
        );
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
        
        logger.info("✅ external_databases table created successfully")
    
    def create_user_database_permissions_table(self):
        """Create user_database_permissions table"""
        logger.info("Creating user_database_permissions table...")
        
        sql = """
        CREATE TABLE IF NOT EXISTS user_database_permissions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            external_database_id INTEGER NOT NULL REFERENCES external_databases(id) ON DELETE CASCADE,
            permission_level VARCHAR(50) DEFAULT 'read',
            granted_by INTEGER REFERENCES users(id),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            UNIQUE(user_id, external_database_id)
        );
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
        
        logger.info("✅ user_database_permissions table created successfully")
    
    def create_database_chat_sessions_table(self):
        """Create database_chat_sessions table"""
        logger.info("Creating database_chat_sessions table...")
        
        sql = """
        CREATE TABLE IF NOT EXISTS database_chat_sessions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id),
            external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
            session_name VARCHAR(255),
            is_connected BOOLEAN DEFAULT FALSE,
            connection_established_at TIMESTAMP,
            connection_error TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_active BOOLEAN DEFAULT TRUE
        );
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
        
        logger.info("✅ database_chat_sessions table created successfully")
    
    def create_database_chat_messages_table(self):
        """Create database_chat_messages table"""
        logger.info("Creating database_chat_messages table...")
        
        sql = """
        CREATE TABLE IF NOT EXISTS database_chat_messages (
            id SERIAL PRIMARY KEY,
            session_id INTEGER NOT NULL REFERENCES database_chat_sessions(id),
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            sql_queries JSONB,
            query_results JSONB,
            execution_plan JSONB,
            qa_feedback JSONB,
            token_usage JSONB,
            execution_time_ms INTEGER,
            error_message TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(sql))
            conn.commit()
        
        logger.info("✅ database_chat_messages table created successfully")
    
    def create_indexes(self):
        """Create indexes for better performance"""
        logger.info("Creating indexes...")
        
        indexes = [
            ("idx_external_databases_created_by", "external_databases", "created_by"),
            ("idx_external_databases_is_active", "external_databases", "is_active"),
            ("idx_user_database_permissions_user_id", "user_database_permissions", "user_id"),
            ("idx_user_database_permissions_database_id", "user_database_permissions", "external_database_id"),
            ("idx_database_chat_sessions_user_id", "database_chat_sessions", "user_id"),
            ("idx_database_chat_sessions_database_id", "database_chat_sessions", "external_database_id"),
            ("idx_database_chat_sessions_is_active", "database_chat_sessions", "is_active"),
            ("idx_database_chat_messages_session_id", "database_chat_messages", "session_id"),
            ("idx_database_chat_messages_created_at", "database_chat_messages", "created_at"),
        ]
        
        with self.engine.connect() as conn:
            for index_name, table_name, column_name in indexes:
                if not self.index_exists(index_name):
                    sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name}({column_name});"
                    conn.execute(text(sql))
                    logger.info(f"✅ Created index: {index_name}")
                else:
                    logger.info(f"⏭️  Index already exists: {index_name}")
            conn.commit()
    
    def add_new_scopes(self):
        """Add new scopes for database chat functionality"""
        logger.info("Adding new scopes...")
        
        scopes = [
            ('DATABASE_CHAT', 'Permission to chat with databases'),
            ('DATABASE_ADMIN', 'Permission to manage database configurations')
        ]
        
        with self.engine.connect() as conn:
            for scope_name, scope_description in scopes:
                # Check if scope already exists
                result = conn.execute(text("""
                    SELECT id FROM scopes WHERE name = :name
                """), {"name": scope_name})
                
                if result.fetchone() is None:
                    conn.execute(text("""
                        INSERT INTO scopes (name) VALUES (:name)
                    """), {"name": scope_name})
                    logger.info(f"✅ Added scope: {scope_name}")
                else:
                    logger.info(f"⏭️  Scope already exists: {scope_name}")
            conn.commit()
    
    def update_admin_scopes(self):
        """Update admin users to have the new scopes"""
        logger.info("Updating admin user scopes...")
        
        with self.engine.connect() as conn:
            # Get the new scope IDs
            result = conn.execute(text("""
                SELECT id FROM scopes WHERE name IN ('DATABASE_CHAT', 'DATABASE_ADMIN')
            """))
            new_scope_ids = [row[0] for row in result.fetchall()]
            
            if new_scope_ids:
                # Update admin users to include new scopes
                conn.execute(text("""
                    UPDATE users 
                    SET scopes = ARRAY(
                        SELECT DISTINCT unnest(
                            CASE 
                                WHEN scopes IS NULL THEN ARRAY[:scope1, :scope2]
                                ELSE scopes || ARRAY[:scope1, :scope2]
                            END
                        )
                    )
                    WHERE is_admin = TRUE
                """), {"scope1": new_scope_ids[0], "scope2": new_scope_ids[1] if len(new_scope_ids) > 1 else new_scope_ids[0]})
                
                conn.commit()
                logger.info("✅ Updated admin user scopes")
            else:
                logger.warning("⚠️  Could not find new scope IDs")
    
    def create_triggers(self):
        """Create triggers for automatic timestamp updates"""
        logger.info("Creating triggers...")
        
        # Create or replace the trigger function
        trigger_function_sql = """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        """
        
        triggers = [
            ("update_external_databases_updated_at", "external_databases"),
            ("update_database_chat_sessions_updated_at", "database_chat_sessions")
        ]
        
        with self.engine.connect() as conn:
            # Create the trigger function
            conn.execute(text(trigger_function_sql))
            
            # Create triggers
            for trigger_name, table_name in triggers:
                trigger_sql = f"""
                DROP TRIGGER IF EXISTS {trigger_name} ON {table_name};
                CREATE TRIGGER {trigger_name}
                    BEFORE UPDATE ON {table_name}
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
                """
                conn.execute(text(trigger_sql))
                logger.info(f"✅ Created trigger: {trigger_name}")
            
            conn.commit()
    
    def add_table_comments(self):
        """Add comments to tables for documentation"""
        logger.info("Adding table comments...")
        
        comments = [
            ("external_databases", "Configuration for external databases that users can connect to and chat with"),
            ("user_database_permissions", "User permissions for accessing specific external databases"),
            ("database_chat_sessions", "Chat sessions between users and external databases"),
            ("database_chat_messages", "Individual messages in database chat sessions with AI responses")
        ]
        
        with self.engine.connect() as conn:
            for table_name, comment in comments:
                if self.table_exists(table_name):
                    conn.execute(text(f"COMMENT ON TABLE {table_name} IS :comment"), {"comment": comment})
                    logger.info(f"✅ Added comment to table: {table_name}")
            conn.commit()
    
    def run_migration(self):
        """Run the complete migration"""
        logger.info("🚀 Starting Database Chat Module Migration")
        logger.info("=" * 60)
        
        try:
            # Check database connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("✅ Database connection successful")
            
            # Create tables
            self.create_external_databases_table()
            self.create_user_database_permissions_table()
            self.create_database_chat_sessions_table()
            self.create_database_chat_messages_table()
            
            # Create indexes
            self.create_indexes()
            
            # Add scopes
            self.add_new_scopes()
            self.update_admin_scopes()
            
            # Create triggers
            self.create_triggers()
            
            # Add comments
            self.add_table_comments()
            
            logger.info("=" * 60)
            logger.info("🎉 Database Chat Module Migration Completed Successfully!")
            logger.info("📋 Summary:")
            logger.info("  • Created external_databases table")
            logger.info("  • Created user_database_permissions table")
            logger.info("  • Created database_chat_sessions table")
            logger.info("  • Created database_chat_messages table")
            logger.info("  • Created performance indexes")
            logger.info("  • Added DATABASE_CHAT and DATABASE_ADMIN scopes")
            logger.info("  • Updated admin user permissions")
            logger.info("  • Created automatic timestamp triggers")
            logger.info("  • Added table documentation")
            
            return True
            
        except SQLAlchemyError as e:
            logger.error(f"❌ Database migration failed: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during migration: {str(e)}")
            return False

def main():
    """Main function to run the migration"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database Chat Module Migration Script')
    parser.add_argument('--database-url', help='Database URL (if not using config)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without executing')
    
    args = parser.parse_args()
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
        logger.info("This would create the following tables:")
        logger.info("  • external_databases")
        logger.info("  • user_database_permissions")
        logger.info("  • database_chat_sessions")
        logger.info("  • database_chat_messages")
        logger.info("And add indexes, scopes, triggers, and comments.")
        return
    
    migrator = DatabaseMigrator(args.database_url)
    success = migrator.run_migration()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
