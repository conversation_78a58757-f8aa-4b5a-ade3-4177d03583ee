# Database Migration Guide

## Overview

This guide explains how to migrate your database to support the new Database Chat functionality. The migration adds new tables, scopes, and relationships while preserving all existing data.

## Migration Scripts

### 1. `migrate_models.py` (Recommended)
**Flask-SQLAlchemy based migration using your existing models**

```bash
python migrate_models.py
```

**Features:**
- ✅ Uses Flask-SQLAlchemy ORM for safe migrations
- ✅ Automatically detects existing tables
- ✅ Creates only missing tables and data
- ✅ Updates admin user scopes automatically
- ✅ Creates performance indexes
- ✅ Adds automatic timestamp triggers
- ✅ Safe to run multiple times (idempotent)

### 2. `migrate_database_chat.py` (Advanced)
**Direct SQL migration with more control**

```bash
python migrate_database_chat.py
```

**Features:**
- ✅ Direct SQL execution for maximum control
- ✅ Detailed logging and error handling
- ✅ Supports custom database URLs
- ✅ Dry-run mode available

```bash
# Dry run to see what would be done
python migrate_database_chat.py --dry-run

# Use custom database URL
python migrate_database_chat.py --database-url "postgresql://user:pass@host:port/db"
```

## What Gets Migrated

### New Tables Created

#### 1. `external_databases`
Stores configuration for external databases that users can connect to.

```sql
CREATE TABLE external_databases (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    db_type VARCHAR(50) NOT NULL,
    host VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    database_name VARCHAR(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password_encrypted TEXT NOT NULL,
    connection_string_template TEXT,
    ssl_enabled BOOLEAN DEFAULT FALSE,
    ssl_cert_path VARCHAR(500),
    additional_params JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_tested_at TIMESTAMP,
    test_status VARCHAR(50) DEFAULT 'pending',
    test_error_message TEXT
);
```

#### 2. `user_database_permissions`
Manages user permissions for accessing specific external databases.

```sql
CREATE TABLE user_database_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id) ON DELETE CASCADE,
    permission_level VARCHAR(50) DEFAULT 'read',
    granted_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    UNIQUE(user_id, external_database_id)
);
```

#### 3. `database_chat_sessions`
Stores chat sessions between users and external databases.

```sql
CREATE TABLE database_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    session_name VARCHAR(255),
    is_connected BOOLEAN DEFAULT FALSE,
    connection_established_at TIMESTAMP,
    connection_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);
```

#### 4. `database_chat_messages`
Stores individual messages in database chat sessions with AI responses.

```sql
CREATE TABLE database_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES database_chat_sessions(id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sql_queries JSONB,
    query_results JSONB,
    execution_plan JSONB,
    qa_feedback JSONB,
    token_usage JSONB,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### New Scopes Added

#### DATABASE_CHAT (Scope ID: 10)
- Permission to chat with databases
- Required for users to access database chat functionality

#### DATABASE_ADMIN (Scope ID: 11)
- Permission to manage database configurations
- Required for admins to add/configure external databases

### Performance Indexes Created

```sql
-- External databases indexes
CREATE INDEX idx_external_databases_created_by ON external_databases(created_by);
CREATE INDEX idx_external_databases_is_active ON external_databases(is_active);

-- User permissions indexes
CREATE INDEX idx_user_database_permissions_user_id ON user_database_permissions(user_id);
CREATE INDEX idx_user_database_permissions_database_id ON user_database_permissions(external_database_id);

-- Chat sessions indexes
CREATE INDEX idx_database_chat_sessions_user_id ON database_chat_sessions(user_id);
CREATE INDEX idx_database_chat_sessions_database_id ON database_chat_sessions(external_database_id);
CREATE INDEX idx_database_chat_sessions_is_active ON database_chat_sessions(is_active);

-- Chat messages indexes
CREATE INDEX idx_database_chat_messages_session_id ON database_chat_messages(session_id);
CREATE INDEX idx_database_chat_messages_created_at ON database_chat_messages(created_at);
```

### Automatic Triggers Created

```sql
-- Function for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for automatic timestamp updates
CREATE TRIGGER update_external_databases_updated_at
    BEFORE UPDATE ON external_databases
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_database_chat_sessions_updated_at
    BEFORE UPDATE ON database_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## Migration Process

### Step 1: Backup Your Database
```bash
# Create a backup before migration
pg_dump -h your_host -U your_user -d your_database > backup_before_migration.sql
```

### Step 2: Run Migration
```bash
# Navigate to the backend directory
cd himalaya_backend

# Run the migration (recommended)
python migrate_models.py

# Or use the advanced migration
python migrate_database_chat.py
```

### Step 3: Verify Migration
```bash
# Test that migration was successful
python test_migration.py
```

### Step 4: Restart Application
```bash
# Restart your Flask application to load new models
# The exact command depends on your deployment setup
```

## Verification Checklist

After running the migration, verify:

- [ ] All 4 new tables are created
- [ ] New scopes (DATABASE_CHAT, DATABASE_ADMIN) are added
- [ ] Admin users have the new scopes
- [ ] Indexes are created for performance
- [ ] Triggers are working for timestamp updates
- [ ] Application starts without errors
- [ ] Database Chat functionality is accessible

## Troubleshooting

### Common Issues

#### 1. Permission Denied
```
ERROR: permission denied for table users
```
**Solution:** Ensure your database user has CREATE and ALTER permissions.

#### 2. Table Already Exists
```
ERROR: relation "external_databases" already exists
```
**Solution:** This is normal. The migration scripts handle existing tables safely.

#### 3. Foreign Key Constraint Fails
```
ERROR: insert or update on table violates foreign key constraint
```
**Solution:** Ensure the `users` table exists and has data before running migration.

#### 4. Connection Failed
```
ERROR: could not connect to server
```
**Solution:** Check your database connection settings in `config/settings.py`.

### Manual Verification

If you want to manually verify the migration:

```sql
-- Check tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name LIKE '%database%';

-- Check scopes
SELECT * FROM scopes WHERE name IN ('DATABASE_CHAT', 'DATABASE_ADMIN');

-- Check admin user scopes
SELECT email, scopes FROM users WHERE is_admin = true;

-- Check indexes
SELECT indexname FROM pg_indexes 
WHERE tablename IN ('external_databases', 'user_database_permissions');
```

## Rollback (If Needed)

If you need to rollback the migration:

```sql
-- Drop new tables (in order due to foreign keys)
DROP TABLE IF EXISTS database_chat_messages;
DROP TABLE IF EXISTS database_chat_sessions;
DROP TABLE IF EXISTS user_database_permissions;
DROP TABLE IF EXISTS external_databases;

-- Remove new scopes
DELETE FROM scopes WHERE name IN ('DATABASE_CHAT', 'DATABASE_ADMIN');

-- Drop triggers and function
DROP TRIGGER IF EXISTS update_external_databases_updated_at ON external_databases;
DROP TRIGGER IF EXISTS update_database_chat_sessions_updated_at ON database_chat_sessions;
DROP FUNCTION IF EXISTS update_updated_at_column();
```

## Next Steps

After successful migration:

1. **Restart your application**
2. **Test the Database Chat functionality**
3. **Add external databases through admin interface**
4. **Assign database permissions to users**
5. **Test user database chat functionality**

## Support

If you encounter issues:

1. Check the migration logs for detailed error messages
2. Verify your database permissions
3. Ensure all dependencies are installed
4. Run the test script to identify specific issues

The migration scripts are designed to be safe and idempotent - you can run them multiple times without causing issues.
