#!/usr/bin/env python3
"""
Simple Database Migration Script for Database Chat Module
This script creates the necessary tables and data for the Database Chat functionality.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def run_migration():
    """Run the database migration"""
    print("🚀 Starting Database Chat Migration")
    print("=" * 50)
    
    try:
        # Import Flask and create app
        from flask import Flask
        app = Flask(__name__)
        
        # Load configuration
        try:
            from src.config.settings import FLASK_CONFIG
            app.config.update(FLASK_CONFIG)
            print("✅ Configuration loaded")
        except ImportError as e:
            print(f"❌ Could not load configuration: {e}")
            return False
        
        # Import database and models
        try:
            from src.models.models import db
            db.init_app(app)
            print("✅ Database initialized")
        except ImportError as e:
            print(f"❌ Could not import database models: {e}")
            return False
        
        with app.app_context():
            # Import all models to ensure they're registered
            from src.models.models import (
                User, Vertical, Department, Scope, Position,
                ExternalDatabase, DatabaseChatSession, DatabaseChatMessage,
                user_database_permissions, user_verticals, user_departments
            )
            print("✅ Models imported")
            
            # Create all tables
            print("📊 Creating database tables...")
            db.create_all()
            print("✅ Tables created successfully")
            
            # Add new scopes
            print("🔐 Adding database scopes...")
            
            # Check if DATABASE_CHAT scope exists
            db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
            if not db_chat_scope:
                db_chat_scope = Scope(name='DATABASE_CHAT')
                db.session.add(db_chat_scope)
                print("✅ Added DATABASE_CHAT scope")
            else:
                print("⏭️  DATABASE_CHAT scope already exists")
            
            # Check if DATABASE_ADMIN scope exists
            db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            if not db_admin_scope:
                db_admin_scope = Scope(name='DATABASE_ADMIN')
                db.session.add(db_admin_scope)
                print("✅ Added DATABASE_ADMIN scope")
            else:
                print("⏭️  DATABASE_ADMIN scope already exists")
            
            # Commit scope changes
            db.session.commit()
            
            # Update admin users with new scopes
            print("👑 Updating admin user scopes...")
            
            # Get the scope IDs after commit
            db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
            db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            
            if db_chat_scope and db_admin_scope:
                admin_users = User.query.filter_by(is_admin=True).all()
                
                for admin in admin_users:
                    current_scopes = admin.scopes or []
                    new_scopes = list(current_scopes)
                    
                    # Add DATABASE_CHAT scope if not present
                    if db_chat_scope.id not in new_scopes:
                        new_scopes.append(db_chat_scope.id)
                    
                    # Add DATABASE_ADMIN scope if not present
                    if db_admin_scope.id not in new_scopes:
                        new_scopes.append(db_admin_scope.id)
                    
                    # Update if scopes changed
                    if new_scopes != current_scopes:
                        admin.scopes = new_scopes
                        print(f"✅ Updated scopes for admin: {admin.email}")
                    else:
                        print(f"⏭️  Admin already has scopes: {admin.email}")
                
                db.session.commit()
                print(f"✅ Updated {len(admin_users)} admin users")
            else:
                print("⚠️  Could not find database scopes")
            
            # Verify tables were created
            print("🔍 Verifying migration...")
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            required_tables = [
                'external_databases',
                'user_database_permissions',
                'database_chat_sessions',
                'database_chat_messages'
            ]
            
            all_tables_exist = True
            for table in required_tables:
                if table in tables:
                    print(f"✅ Table verified: {table}")
                else:
                    print(f"❌ Table missing: {table}")
                    all_tables_exist = False
            
            if not all_tables_exist:
                print("❌ Some tables are missing!")
                return False
            
            # Show summary
            print("=" * 50)
            print("🎉 Migration Completed Successfully!")
            print("📋 Summary:")
            print(f"  • Created {len(required_tables)} new tables")
            print("  • Added DATABASE_CHAT and DATABASE_ADMIN scopes")
            print(f"  • Updated {len(User.query.filter_by(is_admin=True).all())} admin users")
            print("  • Database is ready for Database Chat functionality")
            print()
            print("🚀 Next Steps:")
            print("  1. Restart your Flask application")
            print("  2. Access admin panel to add external databases")
            print("  3. Assign database permissions to users")
            print("  4. Test the Database Chat functionality")
            
            return True
            
    except Exception as e:
        print(f"❌ Migration failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def verify_migration():
    """Verify that migration was successful"""
    print("🧪 Verifying Database Migration")
    print("=" * 40)
    
    try:
        from flask import Flask
        app = Flask(__name__)
        
        from src.config.settings import FLASK_CONFIG
        app.config.update(FLASK_CONFIG)
        
        from src.models.models import db, Scope, User
        db.init_app(app)
        
        with app.app_context():
            # Check scopes
            db_chat_scope = Scope.query.filter_by(name='DATABASE_CHAT').first()
            db_admin_scope = Scope.query.filter_by(name='DATABASE_ADMIN').first()
            
            if db_chat_scope and db_admin_scope:
                print("✅ Database scopes exist")
            else:
                print("❌ Database scopes missing")
                return False
            
            # Check admin users
            admin_users = User.query.filter_by(is_admin=True).all()
            if admin_users:
                print(f"✅ Found {len(admin_users)} admin users")
                
                for admin in admin_users:
                    scopes = admin.scopes or []
                    if db_chat_scope.id in scopes and db_admin_scope.id in scopes:
                        print(f"✅ {admin.email} has database scopes")
                    else:
                        print(f"⚠️  {admin.email} missing database scopes")
            else:
                print("⚠️  No admin users found")
            
            # Check tables
            inspector = db.inspect(db.engine)
            tables = inspector.get_table_names()
            
            required_tables = [
                'external_databases',
                'user_database_permissions',
                'database_chat_sessions',
                'database_chat_messages'
            ]
            
            for table in required_tables:
                if table in tables:
                    print(f"✅ Table exists: {table}")
                else:
                    print(f"❌ Table missing: {table}")
                    return False
            
            print("=" * 40)
            print("🎉 Migration verification successful!")
            return True
            
    except Exception as e:
        print(f"❌ Verification failed: {str(e)}")
        return False

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Database Chat Migration Script')
    parser.add_argument('--verify', action='store_true', help='Verify migration instead of running it')
    
    args = parser.parse_args()
    
    if args.verify:
        success = verify_migration()
    else:
        success = run_migration()
    
    if success:
        print("✅ Operation completed successfully!")
    else:
        print("❌ Operation failed!")
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
