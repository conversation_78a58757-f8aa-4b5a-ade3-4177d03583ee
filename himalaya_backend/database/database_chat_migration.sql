-- Database Chat Module Migration Script
-- This script adds the necessary tables for the Database Chat functionality

-- Create external_databases table
CREATE TABLE IF NOT EXISTS external_databases (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HAR(255) NOT NULL,
    db_type VARCHAR(50) NOT NULL,
    host VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    port INTEGER NOT NULL,
    database_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    username VARCHAR(255) NOT NULL,
    password_encrypted TEXT NOT NULL,
    connection_string_template TEXT,
    ssl_enabled BOOLEAN DEFAULT FALSE,
    ssl_cert_path VARCHAR(500),
    additional_params JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INTEGER NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_tested_at TIMESTAMP,
    test_status VARCHAR(50) DEFAULT 'pending',
    test_error_message TEXT
);

-- Create user_database_permissions table
CREATE TABLE IF NOT EXISTS user_database_permissions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id) ON DELETE CASCADE,
    permission_level VARCHAR(50) DEFAULT 'read',
    granted_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP,
    UNIQUE(user_id, external_database_id)
);

-- Create database_chat_sessions table
CREATE TABLE IF NOT EXISTS database_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id),
    external_database_id INTEGER NOT NULL REFERENCES external_databases(id),
    session_name VARCHAR(255),
    is_connected BOOLEAN DEFAULT FALSE,
    connection_established_at TIMESTAMP,
    connection_error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Create database_chat_messages table
CREATE TABLE IF NOT EXISTS database_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER NOT NULL REFERENCES database_chat_sessions(id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sql_queries JSONB,
    query_results JSONB,
    execution_plan JSONB,
    qa_feedback JSONB,
    token_usage JSONB,
    execution_time_ms INTEGER,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_external_databases_created_by ON external_databases(created_by);
CREATE INDEX IF NOT EXISTS idx_external_databases_is_active ON external_databases(is_active);
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_user_id ON user_database_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_database_id ON user_database_permissions(external_database_id);
CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_user_id ON database_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_database_id ON database_chat_sessions(external_database_id);
CREATE INDEX IF NOT EXISTS idx_database_chat_sessions_is_active ON database_chat_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_database_chat_messages_session_id ON database_chat_messages(session_id);
CREATE INDEX IF NOT EXISTS idx_database_chat_messages_created_at ON database_chat_messages(created_at);

-- Add new scopes to the scopes table
INSERT INTO scopes (name) VALUES 
    ('DATABASE_CHAT'),
    ('DATABASE_ADMIN')
ON CONFLICT (name) DO NOTHING;

-- Update admin user to have the new scopes (assuming admin has user_id = 1)
-- You may need to adjust this based on your admin user setup
UPDATE users 
SET scopes = ARRAY(
    SELECT DISTINCT unnest(
        CASE 
            WHEN scopes IS NULL THEN ARRAY[1,2,3,4,5,6,7,8,9,10,11]
            ELSE scopes || ARRAY[10,11]
        END
    )
)
WHERE is_admin = TRUE;

-- Add comments to tables for documentation
COMMENT ON TABLE external_databases IS 'Configuration for external databases that users can connect to and chat with';
COMMENT ON TABLE user_database_permissions IS 'User permissions for accessing specific external databases';
COMMENT ON TABLE database_chat_sessions IS 'Chat sessions between users and external databases';
COMMENT ON TABLE database_chat_messages IS 'Individual messages in database chat sessions with AI responses';

COMMENT ON COLUMN external_databases.password_encrypted IS 'Encrypted password for database connection';
COMMENT ON COLUMN external_databases.test_status IS 'Status of last connection test: pending, success, failed';
COMMENT ON COLUMN user_database_permissions.permission_level IS 'Permission level: read, write, admin';
COMMENT ON COLUMN database_chat_messages.sql_queries IS 'Array of SQL queries executed for this message';
COMMENT ON COLUMN database_chat_messages.query_results IS 'Results from executed SQL queries';
COMMENT ON COLUMN database_chat_messages.execution_plan IS 'AI planner execution plan';
COMMENT ON COLUMN database_chat_messages.qa_feedback IS 'Quality assurance feedback from AI';

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at columns
CREATE TRIGGER update_external_databases_updated_at 
    BEFORE UPDATE ON external_databases 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_database_chat_sessions_updated_at 
    BEFORE UPDATE ON database_chat_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Sample data for testing (optional - remove in production)
-- INSERT INTO external_databases (
--     name, db_type, host, port, database_name, username, password_encrypted, created_by
-- ) VALUES (
--     'Sample PostgreSQL DB', 'postgresql', 'localhost', 5432, 'sample_db', 'sample_user', 'encrypted_password_here', 1
-- );

COMMIT;
