import unittest
import json
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from src.api.database_admin_routes import (
    database_admin_bp, DATABASE_TYPE_CONFIGS,
    get_database_types, create_database, get_database_schema,
    get_database_schemas, get_databases_for_connections
)
from src.models.models import ExternalDatabase, User, user_database_permissions
from src.utils.database_utils import db_connection_manager

class TestEnhancedDatabaseAdminRoutes(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = MagicMock()
        self.app.config = {'TESTING': True}
        
        # Mock request context
        self.request_context = MagicMock()
        self.request_context.json = {}
        self.request_context.args = {}
        self.request_context.user = MagicMock()
        self.request_context.user.id = 1
        
    def test_database_type_configs(self):
        """Test database type configurations"""
        # Test that all required database types are present
        expected_types = ['postgresql', 'mysql', 'sqlserver', 'oracle']
        self.assertEqual(set(DATABASE_TYPE_CONFIGS.keys()), set(expected_types))
        
        # Test PostgreSQL configuration
        postgresql_config = DATABASE_TYPE_CONFIGS['postgresql']
        self.assertEqual(postgresql_config['name'], 'PostgreSQL')
        self.assertEqual(postgresql_config['default_port'], 5432)
        self.assertIn('name', postgresql_config['required_fields'])
        self.assertIn('host', postgresql_config['required_fields'])
        self.assertIn('port', postgresql_config['required_fields'])
        self.assertIn('database_name', postgresql_config['required_fields'])
        self.assertIn('username', postgresql_config['required_fields'])
        self.assertIn('password', postgresql_config['required_fields'])
        
        # Test schema support
        self.assertIn('schema', postgresql_config['optional_fields'])
        self.assertTrue(postgresql_config['schema_support'])
        self.assertEqual(postgresql_config['default_schema'], 'public')
        
        # Test MySQL configuration
        mysql_config = DATABASE_TYPE_CONFIGS['mysql']
        self.assertIn('schema', mysql_config['optional_fields'])
        self.assertTrue(mysql_config['schema_support'])
        self.assertIsNone(mysql_config['default_schema'])  # MySQL uses database name as schema
        
        # Test SQL Server configuration
        sqlserver_config = DATABASE_TYPE_CONFIGS['sqlserver']
        self.assertIn('schema', sqlserver_config['optional_fields'])
        self.assertTrue(sqlserver_config['schema_support'])
        self.assertEqual(sqlserver_config['default_schema'], 'dbo')
        
        # Test Oracle configuration
        oracle_config = DATABASE_TYPE_CONFIGS['oracle']
        self.assertIn('schema', oracle_config['optional_fields'])
        self.assertTrue(oracle_config['schema_support'])
        self.assertIsNone(oracle_config['default_schema'])  # Oracle uses username as default schema
        
        # Test that SQLite is not supported
        self.assertNotIn('sqlite', DATABASE_TYPE_CONFIGS)
    
    @patch('src.api.database_admin_routes.jsonify')
    def test_get_database_types(self, mock_jsonify):
        """Test getting database types endpoint"""
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = get_database_types()
        
        # Verify the call
        mock_jsonify.assert_called_once_with({
            'database_types': DATABASE_TYPE_CONFIGS,
            'total': len(DATABASE_TYPE_CONFIGS)
        })
        self.assertEqual(result, mock_response)
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_postgresql_database(self, mock_jsonify, mock_db_manager, mock_db, mock_request):
        """Test creating a PostgreSQL database configuration"""
        # Mock request data
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            'host': 'localhost',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password',
            'ssl_enabled': True
        }
        
        # Mock database session
        mock_db.session.add = MagicMock()
        mock_db.session.commit = MagicMock()
        mock_db.session.rollback = MagicMock()
        
        # Mock password encryption
        mock_db_manager.encrypt_password.return_value = 'encrypted_password'
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = create_database()
        
        # Verify database was added and committed
        mock_db.session.add.assert_called_once()
        mock_db.session.commit.assert_called_once()
        
        # Verify password was encrypted
        mock_db_manager.encrypt_password.assert_called_once_with('test_password')
        
        # Verify response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertEqual(call_args['message'], 'Database configuration created successfully')
        self.assertIn('database', call_args)
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_database_invalid_type(self, mock_jsonify, mock_request):
        """Test creating database with invalid type"""
        # Mock request data with invalid type
        mock_request.json = {
            'name': 'Test DB',
            'db_type': 'invalid_type',
            'host': 'localhost',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
        }
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = create_database()
        
        # Verify error response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertIn('error', call_args)
        self.assertIn('Unsupported database type', call_args['error'])
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_database_missing_required_fields(self, mock_jsonify, mock_request):
        """Test creating database with missing required fields"""
        # Mock request data missing required fields
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            # Missing host, port, database_name, username, password
        }
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = create_database()
        
        # Verify error response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertIn('error', call_args)
        self.assertIn('Missing required field', call_args['error'])
    

    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.ExternalDatabase')
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.jsonify')
    def test_get_database_schema_with_schema(self, mock_jsonify, mock_db_manager, mock_db_model, mock_request):
        """Test getting database schema with specific schema"""
        # Mock request args
        mock_request.args = {'schema': 'public'}
        
        # Mock database model
        mock_database = MagicMock()
        mock_database.db_type = 'postgresql'
        mock_database.host = 'localhost'
        mock_database.port = 5432
        mock_database.database_name = 'test_db'
        mock_database.username = 'test_user'
        mock_database.password_encrypted = 'encrypted_password'
        mock_database.ssl_enabled = False
        mock_database.ssl_cert_path = None
        mock_database.additional_params = {}
        
        mock_db_model.query.get_or_404.return_value = mock_database
        
        # Mock schema result
        mock_schema_result = {
            'success': True,
            'schema': {
                'tables': {
                    'users': {
                        'schema': 'public',
                        'columns': [
                            {'name': 'id', 'type': 'INTEGER'},
                            {'name': 'username', 'type': 'VARCHAR'}
                        ]
                    }
                }
            }
        }
        mock_db_manager.get_database_schema.return_value = mock_schema_result
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = get_database_schema(1)
        
        # Verify schema was retrieved with schema parameter
        mock_db_manager.get_database_schema.assert_called_once()
        call_args = mock_db_manager.get_database_schema.call_args[0][0]
        self.assertEqual(call_args['additional_params']['schema'], 'public')
        
        # Verify response
        mock_jsonify.assert_called_once_with(mock_schema_result)
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.ExternalDatabase')
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.jsonify')
    def test_get_database_schema_without_schema(self, mock_jsonify, mock_db_manager, mock_db_model, mock_request):
        """Test getting database schema without specific schema"""
        # Mock request args (no schema)
        mock_request.args = {}
        
        # Mock database model
        mock_database = MagicMock()
        mock_database.db_type = 'postgresql'
        mock_database.host = 'localhost'
        mock_database.port = 5432
        mock_database.database_name = 'test_db'
        mock_database.username = 'test_user'
        mock_database.password_encrypted = 'encrypted_password'
        mock_database.ssl_enabled = False
        mock_database.ssl_cert_path = None
        mock_database.additional_params = {}
        
        mock_db_model.query.get_or_404.return_value = mock_database
        
        # Mock schema result
        mock_schema_result = {
            'success': True,
            'schema': {
                'tables': {
                    'users': {
                        'columns': [
                            {'name': 'id', 'type': 'INTEGER'},
                            {'name': 'username', 'type': 'VARCHAR'}
                        ]
                    }
                }
            }
        }
        mock_db_manager.get_database_schema.return_value = mock_schema_result
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = get_database_schema(1)
        
        # Verify schema was retrieved without schema parameter
        mock_db_manager.get_database_schema.assert_called_once()
        call_args = mock_db_manager.get_database_schema.call_args[0][0]
        self.assertNotIn('schema', call_args['additional_params'])
        
        # Verify response
        mock_jsonify.assert_called_once_with(mock_schema_result)
    
    @patch('src.api.database_admin_routes.ExternalDatabase')
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.jsonify')
    def test_get_database_schemas(self, mock_jsonify, mock_db_manager, mock_db_model):
        """Test getting available schemas"""
        # Mock database model
        mock_database = MagicMock()
        mock_database.db_type = 'postgresql'
        mock_database.host = 'localhost'
        mock_database.port = 5432
        mock_database.database_name = 'test_db'
        mock_database.username = 'test_user'
        mock_database.password_encrypted = 'encrypted_password'
        mock_database.ssl_enabled = False
        mock_database.ssl_cert_path = None
        mock_database.additional_params = {}
        
        mock_db_model.query.get_or_404.return_value = mock_database
        
        # Mock schemas result
        mock_schemas_result = {
            'success': True,
            'schemas': [
                {
                    'name': 'public',
                    'table_count': 10,
                    'view_count': 2,
                    'total_objects': 12
                },
                {
                    'name': 'analytics',
                    'table_count': 5,
                    'view_count': 1,
                    'total_objects': 6
                }
            ],
            'total': 2
        }
        mock_db_manager.get_available_schemas.return_value = mock_schemas_result
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = get_database_schemas(1)
        
        # Verify schemas were retrieved
        mock_db_manager.get_available_schemas.assert_called_once()
        
        # Verify response
        mock_jsonify.assert_called_once_with(mock_schemas_result)
    
    @patch('src.api.database_admin_routes.ExternalDatabase')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.user_database_permissions')
    @patch('src.api.database_admin_routes.jsonify')
    def test_get_databases_for_connections(self, mock_jsonify, mock_permissions, mock_db, mock_db_model):
        """Test getting databases available for connections"""
        # Mock database model
        mock_database = MagicMock()
        mock_database.id = 1
        mock_database.name = 'Test PostgreSQL'
        mock_database.db_type = 'postgresql'
        mock_database.database_name = 'test_db'
        mock_database.host = 'localhost'
        mock_database.port = 5432
        mock_database.last_tested_at = datetime.now()
        mock_database.created_at = datetime.now()
        mock_database.creator = MagicMock()
        mock_database.creator.user_name = 'admin'
        
        # Mock query filter
        mock_query = MagicMock()
        mock_query.filter_by.return_value = mock_query
        mock_query.all.return_value = [mock_database]
        mock_db_model.query = mock_query
        
        # Mock permissions count
        mock_permissions_query = MagicMock()
        mock_permissions_query.filter_by.return_value = mock_permissions_query
        mock_permissions_query.count.return_value = 3
        mock_db.session.query.return_value = mock_permissions_query
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = get_databases_for_connections()
        
        # Verify query was filtered correctly
        mock_query.filter_by.assert_called_with(is_active=True, test_status='success')
        
        # Verify response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertIn('databases', call_args)
        self.assertEqual(call_args['total'], 1)
        self.assertEqual(len(call_args['databases']), 1)
        self.assertEqual(call_args['databases'][0]['name'], 'Test PostgreSQL')
    
    def test_database_type_validation(self):
        """Test database type validation logic"""
        # Test valid database types
        valid_types = ['postgresql', 'mysql', 'sqlserver', 'oracle']
        for db_type in valid_types:
            self.assertIn(db_type, DATABASE_TYPE_CONFIGS)
        
        # Test invalid database type
        invalid_types = ['invalid', 'mongodb', 'redis', '']
        for db_type in invalid_types:
            self.assertNotIn(db_type, DATABASE_TYPE_CONFIGS)
    
    def test_port_validation(self):
        """Test port validation logic"""
        # Test valid ports
        valid_ports = [1, 5432, 3306, 1433, 1521, 65535]
        for port in valid_ports:
            self.assertTrue(1 <= port <= 65535)
        
        # Test invalid ports
        invalid_ports = [0, -1, 65536, 70000]
        for port in invalid_ports:
            self.assertFalse(1 <= port <= 65535)
    

    
    def test_postgresql_ssl_options(self):
        """Test PostgreSQL SSL options"""
        postgresql_config = DATABASE_TYPE_CONFIGS['postgresql']
        
        # PostgreSQL should have SSL options
        self.assertIn('ssl_options', postgresql_config)
        expected_ssl_options = ['disable', 'allow', 'prefer', 'require', 'verify-ca', 'verify-full']
        self.assertEqual(postgresql_config['ssl_options'], expected_ssl_options)
        
        # PostgreSQL should have SSL-related optional fields
        self.assertIn('ssl_enabled', postgresql_config['optional_fields'])
        self.assertIn('ssl_cert_path', postgresql_config['optional_fields'])
    
    def test_mysql_charset_configuration(self):
        """Test MySQL charset configuration"""
        mysql_config = DATABASE_TYPE_CONFIGS['mysql']
        
        # MySQL should have charset in additional params
        self.assertIn('charset', mysql_config['additional_params'])
        self.assertEqual(mysql_config['additional_params']['charset'], 'utf8mb4')
        
        # MySQL should have autocommit setting
        self.assertIn('autocommit', mysql_config['additional_params'])
        self.assertTrue(mysql_config['additional_params']['autocommit'])
    
    def test_sqlserver_driver_configuration(self):
        """Test SQL Server driver configuration"""
        sqlserver_config = DATABASE_TYPE_CONFIGS['sqlserver']
        
        # SQL Server should have driver in additional params
        self.assertIn('driver', sqlserver_config['additional_params'])
        self.assertEqual(sqlserver_config['additional_params']['driver'], 'ODBC Driver 17 for SQL Server')
        
        # SQL Server should have encryption settings
        self.assertIn('encrypt', sqlserver_config['additional_params'])
        self.assertEqual(sqlserver_config['additional_params']['encrypt'], 'yes')
    
    def test_oracle_encoding_configuration(self):
        """Test Oracle encoding configuration"""
        oracle_config = DATABASE_TYPE_CONFIGS['oracle']
        
        # Oracle should have encoding settings
        self.assertIn('encoding', oracle_config['additional_params'])
        self.assertEqual(oracle_config['additional_params']['encoding'], 'UTF-8')
        
        # Oracle should have threaded setting
        self.assertIn('threaded', oracle_config['additional_params'])
        self.assertTrue(oracle_config['additional_params']['threaded'])

    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_database_localhost_restriction(self, mock_jsonify, mock_request):
        """Test that localhost connections are not allowed"""
        # Mock request data with localhost
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            'host': 'localhost',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
        }
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = create_database()
        
        # Verify error response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertIn('error', call_args)
        self.assertIn('Localhost connections are not allowed', call_args['error'])
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_database_127_0_0_1_restriction(self, mock_jsonify, mock_request):
        """Test that 127.0.0.1 connections are not allowed"""
        # Mock request data with 127.0.0.1
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            'host': '127.0.0.1',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
        }
        
        # Mock response
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response
        
        # Call the function
        result = create_database()
        
        # Verify error response
        mock_jsonify.assert_called_once()
        call_args = mock_jsonify.call_args[0][0]
        self.assertIn('error', call_args)
        self.assertIn('Localhost connections are not allowed', call_args['error'])
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_create_database_valid_remote_host(self, mock_jsonify, mock_request):
        """Test that valid remote hosts are allowed"""
        # Mock request data with remote host
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            'host': 'db.example.com',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
        }
        
        # Mock database session
        with patch('src.api.database_admin_routes.db') as mock_db:
            mock_db.session.add = MagicMock()
            mock_db.session.commit = MagicMock()
            
            # Mock password encryption
            with patch('src.api.database_admin_routes.db_connection_manager') as mock_db_manager:
                mock_db_manager.encrypt_password.return_value = 'encrypted_password'
                
                # Mock response
                mock_response = MagicMock()
                mock_jsonify.return_value = mock_response
                
                # Call the function
                result = create_database()
                
                # Verify success response
                mock_jsonify.assert_called_once()
                call_args = mock_jsonify.call_args[0][0]
                self.assertEqual(call_args['message'], 'Database configuration created successfully')
    
    def test_sqlite_not_supported(self):
        """Test that SQLite is not supported"""
        self.assertNotIn('sqlite', DATABASE_TYPE_CONFIGS)
    
    def test_localhost_patterns(self):
        """Test localhost restriction patterns"""
        from src.api.database_admin_routes import create_database
        
        localhost_patterns = [
            'localhost', '127.0.0.1', '::1', '0.0.0.0',
            'localhost.localdomain', 'localhost.local',
            '*********/8', '::1/128'
        ]
        
        # These should all be blocked
        for pattern in localhost_patterns:
            with patch('src.api.database_admin_routes.request') as mock_request:
                mock_request.json = {
                    'name': 'Test DB',
                    'db_type': 'postgresql',
                    'host': pattern,
                    'port': 5432,
                    'database_name': 'test_db',
                    'username': 'test_user',
                    'password': 'test_password'
                }
                
                with patch('src.api.database_admin_routes.jsonify') as mock_jsonify:
                    mock_jsonify.return_value = MagicMock()
                    create_database()
                    
                    # Verify error response
                    call_args = mock_jsonify.call_args[0][0]
                    self.assertIn('error', call_args)
                    self.assertIn('Localhost connections are not allowed', call_args['error'])
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.jsonify')
    def test_grant_database_permission_with_schemas(self, mock_jsonify, mock_db_manager, mock_db, mock_request):
        """Test granting database permission with schema restrictions"""
        # Mock request data with schema permissions
        mock_request.json = {
            'user_id': 1,
            'permission_level': 'read',
            'allowed_schemas': ['public', 'analytics']
        }
        
        # Mock database and user
        with patch('src.api.database_admin_routes.ExternalDatabase') as mock_db_model:
            mock_database = MagicMock()
            mock_database.name = 'Test Database'
            mock_db_model.query.get_or_404.return_value = mock_database
            
            with patch('src.api.database_admin_routes.User') as mock_user_model:
                mock_user = MagicMock()
                mock_user.user_name = 'test_user'
                mock_user.email = '<EMAIL>'
                mock_user_model.query.get.return_value = mock_user
                
                # Mock database session
                mock_db.session.execute = MagicMock()
                mock_db.session.commit = MagicMock()
                
                # Mock response
                mock_response = MagicMock()
                mock_jsonify.return_value = mock_response
                
                # Call the function
                from src.api.database_admin_routes import grant_database_permission
                result = grant_database_permission(1)
                
                # Verify permission was granted with schemas
                mock_db.session.execute.assert_called_once()
                call_args = mock_db.session.execute.call_args[0][0]
                # Verify allowed_schemas was included in the permission data
                self.assertIn('allowed_schemas', str(call_args))
    
    @patch('src.api.database_admin_routes.request')
    @patch('src.api.database_admin_routes.jsonify')
    def test_grant_database_permission_invalid_schemas(self, mock_jsonify, mock_request):
        """Test granting database permission with invalid schema data"""
        # Mock request data with invalid schema permissions
        mock_request.json = {
            'user_id': 1,
            'permission_level': 'read',
            'allowed_schemas': 'invalid_schema_data'  # Should be a list
        }
        
        # Mock database and user
        with patch('src.api.database_admin_routes.ExternalDatabase') as mock_db_model:
            mock_database = MagicMock()
            mock_db_model.query.get_or_404.return_value = mock_database
            
            with patch('src.api.database_admin_routes.User') as mock_user_model:
                mock_user = MagicMock()
                mock_user_model.query.get.return_value = mock_user
                
                # Mock response
                mock_response = MagicMock()
                mock_jsonify.return_value = mock_response
                
                # Call the function
                from src.api.database_admin_routes import grant_database_permission
                result = grant_database_permission(1)
                
                # Verify error response
                mock_jsonify.assert_called_once()
                call_args = mock_jsonify.call_args[0][0]
                self.assertIn('error', call_args)
                self.assertIn('allowed_schemas must be a list', call_args['error'])

    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.request')
    def test_create_database_with_schema(self, mock_request, mock_db, mock_connection_manager):
        """Test creating database with schema specification"""
        # Mock request data with schema
        mock_request.json = {
            'name': 'Test PostgreSQL with Schema',
            'db_type': 'postgresql',
            'host': 'test.example.com',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password',
            'schema': 'analytics'
        }
        mock_request.user.id = 1
        
        # Mock database session
        mock_db.session.add.return_value = None
        mock_db.session.commit.return_value = None
        
        # Mock password encryption
        mock_connection_manager.encrypt_password.return_value = 'encrypted_password'
        
        # Mock ExternalDatabase model
        mock_db_config = MagicMock()
        mock_db_config.id = 1
        mock_db_config.name = 'Test PostgreSQL with Schema'
        mock_db_config.db_type = 'postgresql'
        mock_db_config.host = 'test.example.com'
        mock_db_config.port = 5432
        mock_db_config.database_name = 'test_db'
        mock_db_config.additional_params = {'schema': 'analytics'}
        mock_db_config.is_active = True
        
        with patch('src.api.database_admin_routes.ExternalDatabase', return_value=mock_db_config):
            result = create_database()
            
            # Verify the response
            self.assertEqual(result[1], 201)
            response_data = json.loads(result[0].get_data(as_text=True))
            self.assertEqual(response_data['database']['schema'], 'analytics')
    
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.request')
    def test_create_database_with_invalid_schema(self, mock_request, mock_db, mock_connection_manager):
        """Test creating database with invalid schema"""
        # Mock request data with invalid schema
        mock_request.json = {
            'name': 'Test PostgreSQL',
            'db_type': 'postgresql',
            'host': 'test.example.com',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password',
            'schema': ''  # Empty schema
        }
        mock_request.user.id = 1
        
        result = create_database()
        
        # Verify the response
        self.assertEqual(result[1], 400)
        response_data = json.loads(result[0].get_data(as_text=True))
        self.assertIn('Schema must be a non-empty string', response_data['error'])
    
    @patch('src.api.database_admin_routes.db_connection_manager')
    @patch('src.api.database_admin_routes.db')
    @patch('src.api.database_admin_routes.request')
    def test_create_database_with_default_schema(self, mock_request, mock_db, mock_connection_manager):
        """Test creating database with default schema"""
        # Mock request data without schema (should use default)
        mock_request.json = {
            'name': 'Test PostgreSQL Default',
            'db_type': 'postgresql',
            'host': 'test.example.com',
            'port': 5432,
            'database_name': 'test_db',
            'username': 'test_user',
            'password': 'test_password'
            # No schema specified, should use default 'public'
        }
        mock_request.user.id = 1
        
        # Mock database session
        mock_db.session.add.return_value = None
        mock_db.session.commit.return_value = None
        
        # Mock password encryption
        mock_connection_manager.encrypt_password.return_value = 'encrypted_password'
        
        # Mock ExternalDatabase model
        mock_db_config = MagicMock()
        mock_db_config.id = 1
        mock_db_config.name = 'Test PostgreSQL Default'
        mock_db_config.db_type = 'postgresql'
        mock_db_config.host = 'test.example.com'
        mock_db_config.port = 5432
        mock_db_config.database_name = 'test_db'
        mock_db_config.additional_params = {'schema': 'public'}  # Default schema
        mock_db_config.is_active = True
        
        with patch('src.api.database_admin_routes.ExternalDatabase', return_value=mock_db_config):
            result = create_database()
            
            # Verify the response
            self.assertEqual(result[1], 201)
            response_data = json.loads(result[0].get_data(as_text=True))
            self.assertEqual(response_data['database']['schema'], 'public')

if __name__ == '__main__':
    unittest.main() 