"""
Test file for Agentic Database Visualization Feature

This file contains tests and examples for the enhanced agentic visualization functionality.
"""

import unittest
import json
import pandas as pd
from unittest.mock import Mock, patch, MagicMock
from services.database_visualization_service import DatabaseVisualizationService

class TestAgenticVisualization(unittest.TestCase):
    """Test cases for agentic visualization functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.visualization_service = DatabaseVisualizationService()
        
        # Sample query results for testing
        self.sample_sales_data = [
            {
                'success': True,
                'data': [
                    {'region': 'North', 'sales': 150000},
                    {'region': 'South', 'sales': 120000},
                    {'region': 'East', 'sales': 180000},
                    {'region': 'West', 'sales': 90000}
                ],
                'columns': ['region', 'sales'],
                'row_count': 4
            }
        ]
        
        self.sample_time_series_data = [
            {
                'success': True,
                'data': [
                    {'month': 'Jan', 'sales': 100000},
                    {'month': 'Feb', 'sales': 120000},
                    {'month': 'Mar', 'sales': 110000},
                    {'month': 'Apr', 'sales': 140000},
                    {'month': 'May', 'sales': 130000},
                    {'month': 'Jun', 'sales': 160000}
                ],
                'columns': ['month', 'sales'],
                'row_count': 6
            }
        ]
        
        self.sample_correlation_data = [
            {
                'success': True,
                'data': [
                    {'advertising': 10000, 'sales': 150000},
                    {'advertising': 15000, 'sales': 180000},
                    {'advertising': 20000, 'sales': 220000},
                    {'advertising': 25000, 'sales': 250000},
                    {'advertising': 30000, 'sales': 280000}
                ],
                'columns': ['advertising', 'sales'],
                'row_count': 5
            }
        ]
    
    def test_agentic_decision_structure(self):
        """Test that agentic decision returns proper structure"""
        decision = self.visualization_service.should_generate_visualization(
            "Compare sales by region", self.sample_sales_data
        )
        
        # Check required fields
        required_fields = ['should_generate', 'chart_type', 'confidence', 'reasoning', 'alternative_types']
        for field in required_fields:
            self.assertIn(field, decision)
        
        # Check data types
        self.assertIsInstance(decision['should_generate'], bool)
        self.assertIsInstance(decision['confidence'], (int, float))
        self.assertIsInstance(decision['reasoning'], str)
        self.assertIsInstance(decision['alternative_types'], list)
        
        # Check confidence range
        self.assertGreaterEqual(decision['confidence'], 0.0)
        self.assertLessEqual(decision['confidence'], 1.0)
    
    def test_agentic_decision_with_visualization_keywords(self):
        """Test agentic decision with explicit visualization requests"""
        questions_with_keywords = [
            "Show me a chart of sales by region",
            "Create a graph comparing performance",
            "Display the data as a pie chart",
            "Plot the trends over time",
            "Visualize the distribution"
        ]
        
        for question in questions_with_keywords:
            with self.subTest(question=question):
                decision = self.visualization_service.should_generate_visualization(
                    question, self.sample_sales_data
                )
                self.assertTrue(decision['should_generate'], f"Should generate for: {question}")
                self.assertGreater(decision['confidence'], 0.6, f"High confidence for: {question}")
    
    def test_agentic_decision_without_keywords(self):
        """Test agentic decision for questions without explicit visualization keywords"""
        questions_without_keywords = [
            "What is the total sales?",
            "Get me the data",
            "Show the records",
            "List all items"
        ]
        
        for question in questions_without_keywords:
            with self.subTest(question=question):
                decision = self.visualization_service.should_generate_visualization(
                    question, self.sample_sales_data
                )
                # Should still generate for structured data, but with lower confidence
                self.assertTrue(decision['should_generate'], f"Should generate for structured data: {question}")
                self.assertLess(decision['confidence'], 0.8, f"Lower confidence for: {question}")
    
    def test_agentic_decision_with_empty_data(self):
        """Test agentic decision with empty query results"""
        empty_data = []
        decision = self.visualization_service.should_generate_visualization(
            "Show me sales data", empty_data
        )
        
        self.assertFalse(decision['should_generate'])
        self.assertEqual(decision['confidence'], 0.0)
        self.assertIn('No query results', decision['reasoning'])
    
    def test_agentic_decision_with_large_dataset(self):
        """Test agentic decision with large datasets"""
        # Create a large dataset
        large_data = [
            {
                'success': True,
                'data': [{'region': f'Region_{i}', 'sales': i * 1000} for i in range(150)],
                'columns': ['region', 'sales'],
                'row_count': 150
            }
        ]
        
        decision = self.visualization_service.should_generate_visualization(
            "Show me all sales data", large_data
        )
        
        self.assertFalse(decision['should_generate'])
        self.assertIn('not suitable for visualization', decision['reasoning'])
    
    def test_agentic_chart_type_detection(self):
        """Test agentic chart type detection for different question types"""
        test_cases = [
            ("Show me sales trends over time", "line"),
            ("Compare sales by region", "bar"),
            ("Show market share breakdown", "pie"),
            ("Show correlation between variables", "scatter"),
            ("Display cumulative sales", "area")
        ]
        
        for question, expected_type in test_cases:
            with self.subTest(question=question):
                decision = self.visualization_service.should_generate_visualization(
                    question, self.sample_sales_data
                )
                if decision['should_generate']:
                    self.assertEqual(decision['chart_type'], expected_type)
    
    def test_agentic_decision_with_context(self):
        """Test agentic decision with conversation context"""
        previous_messages = [
            Mock(question="What are our total sales?"),
            Mock(question="Can you show me a breakdown by region?")
        ]
        
        decision = self.visualization_service.should_generate_visualization(
            "Compare the performance", self.sample_sales_data, previous_messages
        )
        
        self.assertTrue(decision['should_generate'])
        self.assertGreater(decision['confidence'], 0.5)
    
    def test_agentic_decision_with_user_context(self):
        """Test agentic decision with user context"""
        user_context = {
            'user_id': 123,
            'permission_level': 'read_write',
            'preferences': {'preferred_chart_type': 'bar'}
        }
        
        decision = self.visualization_service.should_generate_visualization(
            "Show me sales data", self.sample_sales_data, None, user_context
        )
        
        self.assertTrue(decision['should_generate'])
    
    @patch('services.database_visualization_service.AzureOpenAI')
    def test_ai_decision_making(self, mock_openai):
        """Test AI-powered decision making"""
        # Mock AI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = json.dumps({
            "should_generate": True,
            "chart_type": "bar",
            "confidence": 0.85,
            "reasoning": "Bar chart is best for comparing sales across regions",
            "alternative_types": ["pie", "line"],
            "user_benefit": "Visual comparison makes it easy to see regional differences"
        })
        
        mock_client = Mock()
        mock_client.chat.completions.create.return_value = mock_response
        mock_openai.return_value = mock_client
        
        decision = self.visualization_service.should_generate_visualization(
            "Compare sales by region", self.sample_sales_data
        )
        
        self.assertTrue(decision['should_generate'])
        self.assertEqual(decision['chart_type'], 'bar')
        self.assertEqual(decision['confidence'], 0.85)
        self.assertIn('Bar chart is best', decision['reasoning'])
    
    def test_data_structure_analysis(self):
        """Test data structure analysis functionality"""
        analysis = self.visualization_service._analyze_data_structure(self.sample_sales_data)
        
        self.assertTrue(analysis['has_data'])
        self.assertEqual(analysis['characteristics']['row_count'], 4)
        self.assertEqual(analysis['characteristics']['column_count'], 2)
        self.assertTrue(analysis['characteristics']['has_categories'])
        self.assertTrue(analysis['characteristics']['has_numeric_data'])
        self.assertTrue(analysis['characteristics']['data_range']['suitable_for_viz'])
    
    def test_question_classification(self):
        """Test question type classification"""
        test_cases = [
            ("Compare sales by region", "comparison"),
            ("Show trends over time", "trend_analysis"),
            ("What is the distribution?", "distribution"),
            ("Is there a correlation?", "correlation"),
            ("What is the total?", "aggregation"),
            ("List all items", "listing"),
            ("General question", "general")
        ]
        
        for question, expected_type in test_cases:
            with self.subTest(question=question):
                question_type = self.visualization_service._classify_question_type(question)
                self.assertEqual(question_type, expected_type)
    
    def test_visualization_keyword_detection(self):
        """Test visualization keyword detection"""
        questions_with_keywords = [
            "Show me a chart",
            "Create a graph",
            "Plot the data",
            "Visualize the results",
            "Display as bar chart"
        ]
        
        for question in questions_with_keywords:
            with self.subTest(question=question):
                has_keywords = self.visualization_service._has_visualization_keywords(question)
                self.assertTrue(has_keywords)
        
        questions_without_keywords = [
            "What is the total?",
            "Get the data",
            "Show records"
        ]
        
        for question in questions_without_keywords:
            with self.subTest(question=question):
                has_keywords = self.visualization_service._has_visualization_keywords(question)
                self.assertFalse(has_keywords)
    
    def test_ai_decision_validation(self):
        """Test AI decision validation against data constraints"""
        # Test with valid decision
        valid_decision = {
            'should_generate': True,
            'chart_type': 'bar',
            'confidence': 0.8,
            'reasoning': 'Good choice'
        }
        
        data_analysis = {
            'has_data': True,
            'characteristics': {
                'row_count': 5,
                'data_range': {'suitable_for_viz': True},
                'numeric_columns': ['sales'],
                'has_time_series': False
            }
        }
        
        validated = self.visualization_service._validate_ai_decision(valid_decision, data_analysis)
        self.assertTrue(validated['should_generate'])
        self.assertEqual(validated['chart_type'], 'bar')
        
        # Test with invalid chart type
        invalid_decision = {
            'should_generate': True,
            'chart_type': 'unsupported_type',
            'confidence': 0.8,
            'reasoning': 'Test'
        }
        
        validated = self.visualization_service._validate_ai_decision(invalid_decision, data_analysis)
        self.assertEqual(validated['chart_type'], 'bar')  # Should fallback to bar
        self.assertLess(validated['confidence'], 0.8)  # Confidence should be reduced
    
    def test_enhanced_generate_visualization(self):
        """Test enhanced visualization generation with context"""
        previous_messages = [Mock(question="Previous question")]
        user_context = {'user_id': 123, 'permission_level': 'read'}
        
        with patch.object(self.visualization_service, 'should_generate_visualization') as mock_decision:
            mock_decision.return_value = {
                'should_generate': True,
                'chart_type': 'bar',
                'confidence': 0.8,
                'reasoning': 'Good choice',
                'alternative_types': ['pie', 'line'],
                'user_benefit': 'Easy comparison'
            }
            
            with patch.object(self.visualization_service, 'extract_visualization_data') as mock_extract:
                mock_extract.return_value = {
                    'type': 'bar',
                    'title': 'Test Chart',
                    'labels': ['A', 'B'],
                    'datasets': [{'label': 'Data', 'data': [1, 2]}]
                }
                
                with patch('services.database_visualization_service.generate_chart') as mock_generate:
                    mock_generate.return_value = "data:image/png;base64,test"
                    
                    result = self.visualization_service.generate_visualization(
                        "Test question", self.sample_sales_data, previous_messages, user_context
                    )
                    
                    self.assertIsNotNone(result)
                    self.assertIn('visualization_data', result)
                    self.assertIn('visualization_metadata', result)
                    
                    # Check that AI decision was passed to metadata
                    metadata = result['visualization_metadata']
                    self.assertIn('ai_decision', metadata)
                    self.assertEqual(metadata['ai_decision']['chart_type'], 'bar')
    
    def test_enhanced_suggestions(self):
        """Test enhanced visualization suggestions"""
        with patch.object(self.visualization_service, 'should_generate_visualization') as mock_decision:
            mock_decision.return_value = {
                'should_generate': True,
                'chart_type': 'bar',
                'alternative_types': ['pie', 'line', 'scatter']
            }
            
            suggestions = self.visualization_service.get_visualization_suggestions(
                "Test question", self.sample_sales_data
            )
            
            self.assertIsInstance(suggestions, list)
            self.assertGreater(len(suggestions), 0)
            
            # Check that suggestions are properly formatted
            for suggestion in suggestions:
                self.assertIn(' - ', suggestion)  # Should have description

def run_agentic_visualization_demo():
    """Run a demonstration of the agentic visualization functionality"""
    print("=== Agentic Database Visualization Feature Demo ===\n")
    
    service = DatabaseVisualizationService()
    
    # Demo 1: Explicit visualization request
    print("Demo 1: Explicit Visualization Request")
    print("Question: 'Show me a chart comparing sales by region'")
    
    decision = service.should_generate_visualization(
        "Show me a chart comparing sales by region", 
        service.sample_sales_data
    )
    
    print(f"Should generate: {decision['should_generate']}")
    print(f"Chart type: {decision['chart_type']}")
    print(f"Confidence: {decision['confidence']}")
    print(f"Reasoning: {decision['reasoning']}")
    print(f"Alternative types: {decision['alternative_types']}")
    print(f"User benefit: {decision.get('user_benefit', 'N/A')}")
    
    print("\n" + "="*60 + "\n")
    
    # Demo 2: Implicit visualization need
    print("Demo 2: Implicit Visualization Need")
    print("Question: 'What are our sales by region?'")
    
    decision = service.should_generate_visualization(
        "What are our sales by region?", 
        service.sample_sales_data
    )
    
    print(f"Should generate: {decision['should_generate']}")
    print(f"Chart type: {decision['chart_type']}")
    print(f"Confidence: {decision['confidence']}")
    print(f"Reasoning: {decision['reasoning']}")
    
    print("\n" + "="*60 + "\n")
    
    # Demo 3: Time series analysis
    print("Demo 3: Time Series Analysis")
    print("Question: 'Show me sales trends over the last 6 months'")
    
    decision = service.should_generate_visualization(
        "Show me sales trends over the last 6 months", 
        service.sample_time_series_data
    )
    
    print(f"Should generate: {decision['should_generate']}")
    print(f"Chart type: {decision['chart_type']}")
    print(f"Confidence: {decision['confidence']}")
    print(f"Reasoning: {decision['reasoning']}")
    
    print("\n" + "="*60 + "\n")
    
    # Demo 4: Correlation analysis
    print("Demo 4: Correlation Analysis")
    print("Question: 'Is there a relationship between advertising spend and sales?'")
    
    decision = service.should_generate_visualization(
        "Is there a relationship between advertising spend and sales?", 
        service.sample_correlation_data
    )
    
    print(f"Should generate: {decision['should_generate']}")
    print(f"Chart type: {decision['chart_type']}")
    print(f"Confidence: {decision['confidence']}")
    print(f"Reasoning: {decision['reasoning']}")
    
    print("\n" + "="*60 + "\n")
    
    # Demo 5: Data structure analysis
    print("Demo 5: Data Structure Analysis")
    analysis = service._analyze_data_structure(service.sample_sales_data)
    
    print("Data Analysis Results:")
    print(f"Has data: {analysis['has_data']}")
    print(f"Row count: {analysis['characteristics']['row_count']}")
    print(f"Column count: {analysis['characteristics']['column_count']}")
    print(f"Numeric columns: {analysis['characteristics']['numeric_columns']}")
    print(f"Categorical columns: {analysis['characteristics']['categorical_columns']}")
    print(f"Has time series: {analysis['characteristics']['has_time_series']}")
    print(f"Suitable for visualization: {analysis['characteristics']['data_range']['suitable_for_viz']}")
    
    print("\n=== Demo Complete ===")

if __name__ == '__main__':
    # Run the demo
    run_agentic_visualization_demo()
    
    # Run tests
    unittest.main(verbosity=2) 