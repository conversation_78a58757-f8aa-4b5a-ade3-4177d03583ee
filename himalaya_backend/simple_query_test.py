#!/usr/bin/env python3
"""
Simple test for query validation without Flask dependencies
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Mock the database connection manager for testing
class MockDatabaseConnectionManager:
    def validate_query_safety(self, query: str):
        """Basic validation to ensure query safety (only SELECT queries allowed)"""
        query_upper = query.upper().strip()
        
        # Only allow SELECT queries and CTEs - this is a read-only chat system
        if not (query_upper.startswith('SELECT') or query_upper.startswith('WITH')):
            return False, "Only SELECT queries and CTEs (WITH statements) are allowed in this chat system"
        
        # List of dangerous keywords/operations (comprehensive list)
        dangerous_keywords = [
            'DROP', 'DELETE', 'TRUNCATE', 'ALTER', 'CREATE', 'INSERT', 'UPDATE',
            'GRANT', 'REVOKE', 'EXEC', 'EXECUTE', 'CALL', 'MERGE', 'REPLACE',
            'LOAD', 'BULK', 'COPY', 'IMPORT', 'EXPORT'
        ]
        
        for keyword in dangerous_keywords:
            if keyword in query_upper:
                return False, f"Query contains forbidden operation: {keyword}. Only SELECT queries are allowed."
        
        # Additional checks for SQL injection patterns
        # Note: UNION SELECT is actually safe and useful, so we allow it
        injection_patterns = [
            ';--',      # Comment after semicolon
            '/*',       # Block comment start
            '*/',       # Block comment end
            'OR 1=1',   # Classic injection
            'AND 1=1',  # Classic injection
            '--',       # SQL comments (could be used maliciously)
            'EXEC ',    # Execute commands
            'EXECUTE ', # Execute commands
            'SP_',      # Stored procedures
            'XP_'       # Extended procedures
        ]
        
        for pattern in injection_patterns:
            if pattern.upper() in query_upper:
                return False, f"Query contains potentially malicious pattern: {pattern}"
        
        # Check for dangerous multiple statement patterns (semicolon-separated)
        # Allow multiple SELECTs in unions, subqueries, and CTEs
        if ';' in query and query.strip().count(';') > 0:
            # Remove trailing semicolon if it's the only one
            if query.strip().endswith(';') and query.count(';') == 1:
                pass  # Single trailing semicolon is OK
            else:
                return False, "Multiple statements separated by semicolons are not allowed"
        
        return True, None

def test_query_validation():
    """Test various query patterns to ensure proper validation"""
    print("🧪 Testing Query Validation")
    print("=" * 50)
    
    db_manager = MockDatabaseConnectionManager()
    
    # Test cases: (query, should_pass, description)
    test_cases = [
        # Valid SELECT queries that should pass
        (
            "SELECT * FROM users",
            True,
            "Simple SELECT query"
        ),
        (
            "SELECT name FROM customers WHERE country = 'USA' UNION SELECT name FROM customers WHERE country = 'Canada'",
            True,
            "UNION query (multiple SELECTs - should be allowed)"
        ),
        (
            "SELECT * FROM orders WHERE customer_id IN (SELECT id FROM customers WHERE city = 'New York')",
            True,
            "Subquery (multiple SELECTs - should be allowed)"
        ),
        (
            "WITH sales_summary AS (SELECT region, SUM(amount) as total FROM sales GROUP BY region) SELECT * FROM sales_summary",
            True,
            "CTE query (multiple SELECTs - should be allowed)"
        ),
        (
            "SELECT * FROM products WHERE price > (SELECT AVG(price) FROM products)",
            True,
            "Subquery with aggregate (should be allowed)"
        ),
        (
            "SELECT * FROM users;",
            True,
            "SELECT with trailing semicolon (should be allowed)"
        ),
        
        # Invalid queries that should be blocked
        (
            "INSERT INTO users (name) VALUES ('test')",
            False,
            "INSERT query (should be blocked)"
        ),
        (
            "UPDATE users SET name = 'test'",
            False,
            "UPDATE query (should be blocked)"
        ),
        (
            "DELETE FROM users",
            False,
            "DELETE query (should be blocked)"
        ),
        (
            "DROP TABLE users",
            False,
            "DROP query (should be blocked)"
        ),
        (
            "SELECT * FROM users; DROP TABLE users;",
            False,
            "Multiple statements with semicolon (should be blocked)"
        ),
        (
            "SELECT * FROM users WHERE 1=1 OR 1=1",
            False,
            "SQL injection pattern (should be blocked)"
        ),
        (
            "SELECT * FROM users -- comment",
            False,
            "SQL comment (should be blocked)"
        ),
        (
            "SELECT * FROM users /* comment */",
            False,
            "Block comment (should be blocked)"
        )
    ]
    
    passed = 0
    failed = 0
    
    for query, should_pass, description in test_cases:
        is_safe, error_message = db_manager.validate_query_safety(query)
        
        if is_safe == should_pass:
            print(f"✅ PASS: {description}")
            if not is_safe:
                print(f"    Correctly blocked: {error_message}")
            passed += 1
        else:
            print(f"❌ FAIL: {description}")
            print(f"    Expected: {'PASS' if should_pass else 'BLOCK'}")
            print(f"    Got: {'PASS' if is_safe else 'BLOCK'}")
            if error_message:
                print(f"    Error: {error_message}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Query validation is working correctly.")
        print()
        print("✅ Complex SELECT queries are now properly allowed:")
        print("  • UNION queries")
        print("  • Subqueries") 
        print("  • CTEs (Common Table Expressions)")
        print("  • JOINs with aggregation")
        print("  • Queries with trailing semicolons")
        print()
        print("🔒 Security is maintained by blocking:")
        print("  • All non-SELECT statements")
        print("  • Multiple statements separated by semicolons")
        print("  • SQL injection patterns")
        print("  • Comments and dangerous functions")
        return True
    else:
        print("❌ Some tests failed. Please review the validation logic.")
        return False

if __name__ == "__main__":
    success = test_query_validation()
    sys.exit(0 if success else 1)
