#!/usr/bin/env python3
"""
Quick Database Schema Debugging Script

This script provides quick diagnostics for common database schema retrieval issues.
Run this to get immediate insights into what might be causing the error.
"""

import sys
import os
import traceback
import logging

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def check_common_issues():
    """Check for common issues that cause schema retrieval failures"""
    print("🔧 Quick Database Schema Diagnostics")
    print("=" * 50)
    
    issues_found = []
    
    # 1. Check database drivers
    print("\n1. Checking database drivers...")
    
    drivers = {
        'postgresql': 'psycopg2',
        'mysql': 'pymysql', 
        'sqlserver': 'pyodbc',
        'oracle': 'cx_Oracle'
    }
    
    for db_type, driver in drivers.items():
        try:
            if db_type == 'postgresql':
                import psycopg2
                print(f"   ✅ {db_type}: psycopg2 available")
            elif db_type == 'mysql':
                import pymysql
                print(f"   ✅ {db_type}: pymysql available")
            elif db_type == 'sqlserver':
                import pyodbc
                print(f"   ✅ {db_type}: pyodbc available")
            elif db_type == 'oracle':
                import cx_Oracle
                print(f"   ✅ {db_type}: cx_Oracle available")
        except ImportError:
            print(f"   ❌ {db_type}: {driver} not installed")
            issues_found.append(f"Missing {driver} for {db_type}")
    
    # 2. Check SQLAlchemy
    print("\n2. Checking SQLAlchemy...")
    try:
        import sqlalchemy
        print(f"   ✅ SQLAlchemy version: {sqlalchemy.__version__}")
        
        from sqlalchemy import inspect
        print("   ✅ SQLAlchemy inspect available")
        
    except ImportError as e:
        print(f"   ❌ SQLAlchemy import error: {str(e)}")
        issues_found.append("SQLAlchemy import issue")
    
    # 3. Check cryptography
    print("\n3. Checking cryptography...")
    try:
        from cryptography.fernet import Fernet
        print("   ✅ Cryptography available")
    except ImportError:
        print("   ❌ Cryptography not installed")
        issues_found.append("Missing cryptography library")
    
    # 4. Check database utils import
    print("\n4. Checking database utils...")
    try:
        from utils.database_utils import db_connection_manager
        print("   ✅ Database utils imported successfully")
        
        # Test encryption
        test_password = "test123"
        encrypted = db_connection_manager.encrypt_password(test_password)
        decrypted = db_connection_manager.decrypt_password(encrypted)
        
        if decrypted == test_password:
            print("   ✅ Password encryption/decryption working")
        else:
            print("   ❌ Password encryption/decryption failed")
            issues_found.append("Password encryption issue")
            
    except Exception as e:
        print(f"   ❌ Database utils import error: {str(e)}")
        issues_found.append(f"Database utils import: {str(e)}")
    
    # 5. Check settings
    print("\n5. Checking settings...")
    try:
        from config.settings import DATABASE_ENCRYPTION_KEY
        if DATABASE_ENCRYPTION_KEY:
            print("   ✅ Database encryption key configured")
        else:
            print("   ⚠️  Database encryption key not set")
            issues_found.append("Database encryption key not configured")
    except ImportError:
        print("   ⚠️  Could not import settings")
    except Exception as e:
        print(f"   ❌ Settings error: {str(e)}")
        issues_found.append(f"Settings error: {str(e)}")
    
    # 6. Check logging configuration
    print("\n6. Checking logging...")
    try:
        from config.logging_config import setup_logging
        print("   ✅ Logging config available")
    except Exception as e:
        print(f"   ⚠️  Logging config issue: {str(e)}")
    
    # Summary
    print(f"\n📊 Quick Diagnostics Summary:")
    print("=" * 50)
    
    if issues_found:
        print("❌ Issues found:")
        for i, issue in enumerate(issues_found, 1):
            print(f"   {i}. {issue}")
        
        print(f"\n🔧 Recommended fixes:")
        print("1. Install missing database drivers:")
        print("   pip install psycopg2-binary pymysql pyodbc cx_Oracle")
        print("2. Install cryptography:")
        print("   pip install cryptography")
        print("3. Check your settings.py for DATABASE_ENCRYPTION_KEY")
        print("4. Ensure all required packages are in requirements.txt")
        
    else:
        print("✅ No obvious issues found in quick diagnostics")
        print("\n💡 The error might be related to:")
        print("- Database connection issues (network, credentials)")
        print("- Database permissions")
        print("- Database server configuration")
        print("- Specific database type compatibility")
        print("\n🔍 Run the full debugging script for detailed analysis:")
        print("   python debug_schema_issue.py")

def test_simple_connection():
    """Test a simple database connection with common configurations"""
    print(f"\n🔍 Testing simple database connections...")
    
    # Test configurations for common databases
    test_configs = [
        {
            'name': 'PostgreSQL Test',
            'db_type': 'postgresql',
            'host': 'localhost',
            'port': 5432,
            'database_name': 'postgres',
            'username': 'postgres',
            'password': 'password',
            'ssl_enabled': False,
            'additional_params': {}
        },
        {
            'name': 'MySQL Test', 
            'db_type': 'mysql',
            'host': 'localhost',
            'port': 3306,
            'database_name': 'mysql',
            'username': 'root',
            'password': 'password',
            'ssl_enabled': False,
            'additional_params': {}
        }
    ]
    
    try:
        from utils.database_utils import db_connection_manager
        
        for config in test_configs:
            print(f"\n   Testing {config['name']}...")
            
            # Encrypt password
            config['password_encrypted'] = db_connection_manager.encrypt_password(config['password'])
            
            # Test connection string building
            try:
                conn_str = db_connection_manager.build_connection_string(config)
                print(f"   ✅ Connection string built")
            except Exception as e:
                print(f"   ❌ Connection string failed: {str(e)}")
                continue
            
            # Test connection (this will likely fail, but we can see the error)
            try:
                success, error = db_connection_manager.test_connection(config)
                if success:
                    print(f"   ✅ Connection successful")
                else:
                    print(f"   ❌ Connection failed: {error}")
            except Exception as e:
                print(f"   ❌ Connection test exception: {str(e)}")
                
    except Exception as e:
        print(f"   ❌ Test setup failed: {str(e)}")

if __name__ == "__main__":
    check_common_issues()
    test_simple_connection() 