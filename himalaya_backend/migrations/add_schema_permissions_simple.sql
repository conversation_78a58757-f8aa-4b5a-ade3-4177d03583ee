-- Migration: Add schema permissions to user_database_permissions table (Simple Version)
-- Date: 2024-01-15
-- Description: Add schema-level permissions for database access control without GIN indexing

-- Add schema column to user_database_permissions table
ALTER TABLE user_database_permissions 
ADD COLUMN allowed_schemas JSON DEFAULT NULL;

-- Add comment to explain the schema column
COMMENT ON COLUMN user_database_permissions.allowed_schemas IS 'JSON array of schema names that the user is allowed to access. NULL means all schemas are allowed.';

-- Create simple index for better performance (no GIN index to avoid operator class issues)
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
ON user_database_permissions (allowed_schemas);

-- Update existing permissions to allow all schemas (backward compatibility)
UPDATE user_database_permissions 
SET allowed_schemas = NULL 
WHERE allowed_schemas IS NULL;

-- Add constraint to ensure schemas is either NULL or a valid JSON array
ALTER TABLE user_database_permissions 
ADD CONSTRAINT check_allowed_schemas 
CHECK (allowed_schemas IS NULL OR jsonb_typeof(allowed_schemas::jsonb) = 'array');

-- Migration completed successfully
-- Note: Migration logging removed as migration_log table doesn't exist 