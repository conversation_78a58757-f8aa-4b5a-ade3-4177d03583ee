-- Migration: Add schema permissions to user_database_permissions table (Final Version)
-- Date: 2024-01-15
-- Description: Add schema-level permissions for database access control
-- This migration is self-contained and doesn't depend on external tables

-- Check if the column already exists to avoid errors
DO $$
BEGIN
    -- Check if the column already exists
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_database_permissions' 
        AND column_name = 'allowed_schemas'
    ) THEN
        -- Add schema column to user_database_permissions table
        ALTER TABLE user_database_permissions 
        ADD COLUMN allowed_schemas JSON DEFAULT NULL;
        
        RAISE NOTICE 'Added allowed_schemas column to user_database_permissions table';
    ELSE
        RAISE NOTICE 'Column allowed_schemas already exists';
    END IF;
END $$;

-- Add comment to explain the schema column
COMMENT ON COLUMN user_database_permissions.allowed_schemas IS 'JSON array of schema names that the user is allowed to access. NULL means all schemas are allowed.';

-- Create simple index for better performance (no GIN index to avoid operator class issues)
CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
ON user_database_permissions (allowed_schemas);

-- Update existing permissions to allow all schemas (backward compatibility)
UPDATE user_database_permissions 
SET allowed_schemas = NULL 
WHERE allowed_schemas IS NULL;

-- Add constraint to ensure schemas is either NULL or a valid JSON array
-- Drop constraint first if it exists to avoid errors
ALTER TABLE user_database_permissions 
DROP CONSTRAINT IF EXISTS check_allowed_schemas;

ALTER TABLE user_database_permissions 
ADD CONSTRAINT check_allowed_schemas 
CHECK (allowed_schemas IS NULL OR jsonb_typeof(allowed_schemas::jsonb) = 'array');

-- Migration completed successfully
SELECT 'Schema permissions migration completed successfully' as status; 