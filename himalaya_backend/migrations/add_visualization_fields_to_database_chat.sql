-- Migration: Add visualization fields to database_chat_messages table
-- Date: 2024-01-15
-- Description: Adds visualization support to database chat messages

-- Add visualization fields to database_chat_messages table
ALTER TABLE database_chat_messages 
ADD COLUMN visualization_data JSON NULL,
ADD COLUMN visualization_type VARCHAR(50) NULL,
ADD COLUMN visualization_image TEXT NULL,
ADD COLUMN visualization_metadata JSON NULL;

-- Add comments to document the new fields
COMMENT ON COLUMN database_chat_messages.visualization_data IS 'Chart configuration and data for visualization';
COMMENT ON COLUMN database_chat_messages.visualization_type IS 'Type of chart (bar, line, pie, scatter, area, etc.)';
COMMENT ON COLUMN database_chat_messages.visualization_image IS 'Base64 encoded chart image';
COMMENT ON COLUMN database_chat_messages.visualization_metadata IS 'Additional visualization information and metadata';

-- Create index on visualization_type for faster queries
CREATE INDEX idx_database_chat_messages_visualization_type 
ON database_chat_messages(visualization_type);

-- Update existing messages to have NULL values for new fields
UPDATE database_chat_messages 
SET visualization_data = NULL,
    visualization_type = NULL,
    visualization_image = NULL,
    visualization_metadata = NULL
WHERE visualization_data IS NULL;

-- Migration completed successfully 