-- Migration: Fix schema permissions JSONB conversion
-- Date: 2024-01-15
-- Description: Convert allowed_schemas column from JSON to JSONB for proper GIN indexing

-- Check if the column exists and is JSON type
DO $$
BEGIN
    -- Check if the column exists and is JSON type
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_database_permissions' 
        AND column_name = 'allowed_schemas' 
        AND data_type = 'json'
    ) THEN
        -- Convert JSON to JSONB
        ALTER TABLE user_database_permissions 
        ALTER COLUMN allowed_schemas TYPE JSONB USING allowed_schemas::jsonb;
        
        -- Update comment
        COMMENT ON COLUMN user_database_permissions.allowed_schemas IS 'JSONB array of schema names that the user is allowed to access. NULL means all schemas are allowed.';
        
        -- Drop the old index if it exists
        DROP INDEX IF EXISTS idx_user_database_permissions_schemas;
        
        -- Create new GIN index for JSONB
        CREATE INDEX IF NOT EXISTS idx_user_database_permissions_schemas 
        ON user_database_permissions USING GIN (allowed_schemas);
        
        -- Update constraint to use jsonb_typeof
        ALTER TABLE user_database_permissions 
        DROP CONSTRAINT IF EXISTS check_allowed_schemas;
        
        ALTER TABLE user_database_permissions 
        ADD CONSTRAINT check_allowed_schemas 
        CHECK (allowed_schemas IS NULL OR jsonb_typeof(allowed_schemas) = 'array');
        
        RAISE NOTICE 'Successfully converted allowed_schemas from JSON to JSONB';
    ELSE
        RAISE NOTICE 'Column allowed_schemas does not exist or is already JSONB type';
    END IF;
END $$;

-- Migration completed successfully
-- Note: Migration logging removed as migration_log table doesn't exist 