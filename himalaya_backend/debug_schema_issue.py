#!/usr/bin/env python3
"""
Database Schema Debugging Script

This script helps identify the specific cause of database schema retrieval errors
by testing different aspects of the database connection and schema retrieval process.
"""

import sys
import os
import traceback
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

logger = logging.getLogger(__name__)

def test_database_connection(db_config):
    """Test basic database connection"""
    try:
        from utils.database_utils import db_connection_manager
        
        print(f"\n🔍 Testing connection to {db_config['db_type']} database...")
        print(f"   Host: {db_config['host']}:{db_config['port']}")
        print(f"   Database: {db_config['database_name']}")
        print(f"   Username: {db_config['username']}")
        
        # Test connection
        success, error = db_connection_manager.test_connection(db_config)
        
        if success:
            print("   ✅ Connection successful")
            return True
        else:
            print(f"   ❌ Connection failed: {error}")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection test exception: {str(e)}")
        traceback.print_exc()
        return False

def test_schema_retrieval(db_config):
    """Test schema retrieval with detailed error reporting"""
    try:
        from utils.database_utils import db_connection_manager
        
        print(f"\n🔍 Testing schema retrieval...")
        
        # Test get_available_schemas first
        print("   Testing get_available_schemas...")
        schemas_result = db_connection_manager.get_available_schemas(db_config)
        
        if schemas_result.get('success'):
            print(f"   ✅ Available schemas: {len(schemas_result.get('schemas', []))}")
            for schema in schemas_result.get('schemas', [])[:5]:  # Show first 5
                print(f"      - {schema['name']} ({schema['total_objects']} objects)")
        else:
            print(f"   ❌ Schema listing failed: {schemas_result.get('error')}")
        
        # Test get_database_schema
        print("   Testing get_database_schema...")
        schema_result = db_connection_manager.get_database_schema(db_config)
        
        if schema_result.get('success'):
            schema = schema_result.get('schema', {})
            tables = schema.get('tables', {})
            views = schema.get('views', {})
            print(f"   ✅ Schema retrieval successful")
            print(f"      Tables: {len(tables)}")
            print(f"      Views: {len(views)}")
            
            # Show first few tables
            for table_name in list(tables.keys())[:3]:
                table_info = tables[table_name]
                columns = table_info.get('columns', [])
                print(f"      - {table_name}: {len(columns)} columns")
                
        else:
            print(f"   ❌ Schema retrieval failed: {schema_result.get('error')}")
            return False
            
        return True
        
    except Exception as e:
        print(f"   ❌ Schema retrieval exception: {str(e)}")
        traceback.print_exc()
        return False

def test_connection_string_building(db_config):
    """Test connection string building"""
    try:
        from utils.database_utils import db_connection_manager
        
        print(f"\n🔍 Testing connection string building...")
        
        # Build connection string
        conn_str = db_connection_manager.build_connection_string(db_config)
        
        # Mask password in output
        masked_conn_str = conn_str.replace(db_config['password_encrypted'], '***MASKED***')
        print(f"   ✅ Connection string built successfully")
        print(f"   Connection string: {masked_conn_str}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Connection string building failed: {str(e)}")
        traceback.print_exc()
        return False

def test_password_encryption(db_config):
    """Test password encryption/decryption"""
    try:
        from utils.database_utils import db_connection_manager
        
        print(f"\n🔍 Testing password encryption...")
        
        # Test decryption
        try:
            decrypted_password = db_connection_manager.decrypt_password(db_config['password_encrypted'])
            print(f"   ✅ Password decryption successful")
            print(f"   Password length: {len(decrypted_password)}")
            return True
        except Exception as e:
            print(f"   ❌ Password decryption failed: {str(e)}")
            return False
            
    except Exception as e:
        print(f"   ❌ Password encryption test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_sqlalchemy_inspection(db_config):
    """Test SQLAlchemy inspection directly"""
    try:
        from utils.database_utils import db_connection_manager
        from sqlalchemy import inspect
        
        print(f"\n🔍 Testing SQLAlchemy inspection...")
        
        # Get connection
        connection = db_connection_manager.get_connection(db_config)
        inspector = inspect(connection.engine)
        
        # Test basic inspection methods
        print("   Testing get_schema_names...")
        try:
            schemas = inspector.get_schema_names()
            print(f"   ✅ Found {len(schemas)} schemas: {schemas[:5]}")  # Show first 5
        except Exception as e:
            print(f"   ❌ get_schema_names failed: {str(e)}")
        
        # Test table listing
        print("   Testing get_table_names...")
        try:
            tables = inspector.get_table_names()
            print(f"   ✅ Found {len(tables)} tables: {tables[:5]}")  # Show first 5
        except Exception as e:
            print(f"   ❌ get_table_names failed: {str(e)}")
        
        # Test column inspection
        if tables:
            print(f"   Testing get_columns for table '{tables[0]}'...")
            try:
                columns = inspector.get_columns(tables[0])
                print(f"   ✅ Found {len(columns)} columns in {tables[0]}")
                for col in columns[:3]:  # Show first 3 columns
                    print(f"      - {col['name']}: {col['type']}")
            except Exception as e:
                print(f"   ❌ get_columns failed: {str(e)}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"   ❌ SQLAlchemy inspection failed: {str(e)}")
        traceback.print_exc()
        return False

def check_database_dependencies(db_type):
    """Check if required database drivers are installed"""
    print(f"\n🔍 Checking database dependencies for {db_type}...")
    
    missing_deps = []
    
    if db_type == 'postgresql':
        try:
            import psycopg2
            print("   ✅ psycopg2 installed")
        except ImportError:
            print("   ❌ psycopg2 not installed")
            missing_deps.append('psycopg2-binary')
    
    elif db_type == 'mysql':
        try:
            import pymysql
            print("   ✅ pymysql installed")
        except ImportError:
            print("   ❌ pymysql not installed")
            missing_deps.append('pymysql')
    
    elif db_type == 'sqlserver':
        try:
            import pyodbc
            print("   ✅ pyodbc installed")
        except ImportError:
            print("   ❌ pyodbc not installed")
            missing_deps.append('pyodbc')
    
    elif db_type == 'oracle':
        try:
            import cx_Oracle
            print("   ✅ cx_Oracle installed")
        except ImportError:
            print("   ❌ cx_Oracle not installed")
            missing_deps.append('cx_Oracle')
    
    if missing_deps:
        print(f"   📦 Install missing dependencies: pip install {' '.join(missing_deps)}")
        return False
    
    return True

def main():
    """Main debugging function"""
    print("🔧 Database Schema Debugging Tool")
    print("=" * 50)
    
    # Get database configuration from user
    print("\nPlease provide database configuration:")
    
    db_type = input("Database type (postgresql/mysql/sqlserver/oracle): ").strip().lower()
    host = input("Host: ").strip()
    port = input("Port: ").strip()
    database_name = input("Database name: ").strip()
    username = input("Username: ").strip()
    password = input("Password: ").strip()
    
    # Create database config
    db_config = {
        'db_type': db_type,
        'host': host,
        'port': int(port),
        'database_name': database_name,
        'username': username,
        'password': password,
        'ssl_enabled': False,
        'additional_params': {}
    }
    
    # Encrypt password
    try:
        from utils.database_utils import db_connection_manager
        db_config['password_encrypted'] = db_connection_manager.encrypt_password(password)
    except Exception as e:
        print(f"❌ Failed to encrypt password: {str(e)}")
        return
    
    print(f"\n🚀 Starting diagnostics for {db_type} database...")
    
    # Run tests
    tests = [
        ("Database Dependencies", lambda: check_database_dependencies(db_type)),
        ("Password Encryption", lambda: test_password_encryption(db_config)),
        ("Connection String Building", lambda: test_connection_string_building(db_config)),
        ("Database Connection", lambda: test_database_connection(db_config)),
        ("SQLAlchemy Inspection", lambda: test_sqlalchemy_inspection(db_config)),
        ("Schema Retrieval", lambda: test_schema_retrieval(db_config))
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results Summary:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed < len(results):
        print(f"\n🔧 Troubleshooting Tips:")
        print("1. Check if database server is running and accessible")
        print("2. Verify database credentials are correct")
        print("3. Ensure required database drivers are installed")
        print("4. Check firewall settings and network connectivity")
        print("5. Verify database user has sufficient permissions")
        print("6. Check database logs for connection errors")
        
        if db_type == 'postgresql':
            print("\nPostgreSQL specific:")
            print("- Ensure pg_hba.conf allows connections from your IP")
            print("- Check if database exists and user has access")
            print("- Verify SSL settings if enabled")
        
        elif db_type == 'mysql':
            print("\nMySQL specific:")
            print("- Ensure user has proper host permissions")
            print("- Check if database exists")
            print("- Verify authentication plugin compatibility")
        
        elif db_type == 'sqlserver':
            print("\nSQL Server specific:")
            print("- Ensure SQL Server is configured for TCP/IP connections")
            print("- Check if SQL Server Browser service is running")
            print("- Verify Windows Authentication vs SQL Authentication")
    
    else:
        print(f"\n🎉 All tests passed! The schema retrieval should work correctly.")

if __name__ == "__main__":
    main() 